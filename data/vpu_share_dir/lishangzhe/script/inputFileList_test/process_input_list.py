#!/usr/bin/env python3
import os
import re
import sys

def extract_dimensions(filename):
    """从文件名中提取宽度和高度"""
    match = re.search(r'(\d+)x(\d+)', filename)
    if match:
        return match.group(1), match.group(2)
    return None, None

def find_vpu_testsuit_path(current_path):
    """向上查找包含vpu_testsuit的路径"""
    while current_path != '/':
        if os.path.exists(os.path.join(current_path, 'vpu_testsuit')):
            return os.path.join(current_path, 'vpu_testsuit')
        current_path = os.path.dirname(current_path)
    
    # 如果找不到，尝试从当前工作目录查找
    current_path = os.getcwd()
    while current_path != '/':
        if os.path.exists(os.path.join(current_path, 'vpu_testsuit')):
            return os.path.join(current_path, 'vpu_testsuit')
        current_path = os.path.dirname(current_path)
    
    return None

def main():
    # 获取输入文件路径
    input_file = "input_list.txt"
    if len(sys.argv) > 1:
        input_file = sys.argv[1]
    
    # 查找vpu_testsuit路径
    vpu_path = find_vpu_testsuit_path(os.path.dirname(os.path.abspath(input_file)))
    if not vpu_path:
        print("错误：无法找到vpu_testsuit目录")
        sys.exit(1)
    
    base_path = os.path.dirname(vpu_path)
    input_yuv_path = os.path.join(base_path, "vpu_testsuit/input/vc9000e/stream/yuv")
    output_path = os.path.join(base_path, "vpu_testsuit/output/1")
    
    # 确保输出目录存在
    os.makedirs(output_path, exist_ok=True)
    
    # 读取输入文件
    with open(input_file, 'r') as f:
        lines = f.readlines()
    
    new_lines = []
    stream_count = 1
    
    for line in lines:
        line = line.strip()
        if not line or not line.startswith('-i '):
            continue
        
        # 提取文件名
        parts = line.split()
        if len(parts) < 2:
            continue
        
        filename = parts[1]
        
        # 提取宽度和高度
        width, height = extract_dimensions(filename)
        if not width or not height:
            print(f"警告：无法从 {filename} 提取尺寸")
            continue
        
        # 构建完整路径
        full_input_path = os.path.join(input_yuv_path, filename)
        output_file = f"stream{stream_count}.h264"
        full_output_path = os.path.join(output_path, output_file)
        
        # 构建新行
        new_line = f"-i {full_input_path} -w {width} -h {height} -o {full_output_path}"
        new_lines.append(new_line)
        
        stream_count += 1
    
    # 写入新文件
    with open(input_file, 'w') as f:
        for line in new_lines:
            f.write(line + '\n')
    
    print(f"处理完成，已更新 {input_file} 文件，共处理 {stream_count-1} 个条目")

if __name__ == "__main__":
    main()