case_id,flag,milestone,cv_id,codec,category,coverage,INPUT_BS_DIR,INPUT_BS,OUT_FILE_DIR,out_file,theading_options,__FLOW_OPTIONS,input_options_yaml,logmsg,decode_options,ec,crop_scale,filter,yuv2rgb,3D_lut,codec_specifed,pp_in_w,pp_in_h,pp_in_s,__PP_IN_FMT,__PP_OUT_FMT,pp_out_y_s,pp_out_c_s,__OUT_OPTIONS,out_s_a,target_frame_num,MD5Ref,MD5,status,comments
1,,cv0.5,,h264,,"""Baseline,QCIF / 15fps,CAVLC,IPPIPP...,Frame only,Loop filter,Number Slice Groups=1,Num. of ref-frames=4,all-I,Access unit delimiter""",stream_dir,AUD_MW_E.264,output_dir,AUD_MW_E.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,6b21e735087f84899bb8ac41aeebee2c,6b21e735087f84899bb8ac41aeebee2c,pass,
2,,cv0.5,,h264,,"""CIF,Frame,CAVLC,Every I""",stream_dir,BA1_FT_C.264,output_dir,BA1_FT_C.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,5c90b8e711ebcd1bdf698213a04c6d26,5c90b8e711ebcd1bdf698213a04c6d26,pass,
3,,cv0.5,,h264,,"""QCIF,Frame,CABAC,I,Loop Filter,Number Slice Groups=1,First frame""",stream_dir,BA1_Sony_D.264,output_dir,BA1_Sony_D.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,4306df7f1fbeb45ab400c355c053fb7b,4306df7f1fbeb45ab400c355c053fb7b,pass,
4,,cv0.5,,h264,,"""QCIF,Frame,CAVLC,IP,Loop Filter,Direct 8x8 Inference,Number Slice Groups=1,First only""",stream_dir,BA2_Sony_F.264,output_dir,BA2_Sony_F.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,e7b6d1088f422739e893d2f7577b943a,e7b6d1088f422739e893d2f7577b943a,pass,
5,,cv0.5,,h264,,"""QCIF,Frame,CAVLC,IPB,Loop Filter,Spatial,Direct 8x8 Inference,Number Slice Groups=1,First Frame""",stream_dir,BA3_SVA_C.264,output_dir,BA3_SVA_C.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,8cfd8350f9f8d670d80f282e534f0b8a,8cfd8350f9f8d670d80f282e534f0b8a,pass,
6,,cv0.5,,h264,,"""QCIF,Frame,CAVLC,I only,Loop Filter,Direct 8x8 Inference,Number Slice Groups=1,First only""",stream_dir,BAMQ1_JVC_C.264,output_dir,BAMQ1_JVC_C.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,9b747399e1ab233f0988c15e77852f12,9b747399e1ab233f0988c15e77852f12,pass,
7,,cv0.5,,h264,,"""QCIF,Frame,CAVLC,IP,Loop Filter,Direct 8x8 Inference,Number Slice Groups=1,First only""",stream_dir,BAMQ2_JVC_C.264,output_dir,BAMQ2_JVC_C.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,c27e099c23ce4a930703c410681346e5,c27e099c23ce4a930703c410681346e5,pass,
8,,cv0.5,,h264,,"""Baseline,QCIF / 15fps,CAVLC,IPPIPP...,Frame only,Loop filter,Number Slice Groups=1,Num. of ref-frames=1,all-I""",stream_dir,BANM_MW_D.264,output_dir,BANM_MW_D.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,9dfe3782e8498462170e21a91c4e4045,9dfe3782e8498462170e21a91c4e4045,pass,
9,,cv0.5,,h264,,"""QCIF,Frame,CAVLC,I,Loop Filter,Direct 8x8 Inference,Number Slice Groups=1,First frame""",stream_dir,BASQP1_Sony_C.264,output_dir,BASQP1_Sony_C.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,5746d067544e2c5f3c7e2d2fad393a87,5746d067544e2c5f3c7e2d2fad393a87,pass,
10,,cv0.5,,h264,,"""Baseline,QCIF / 15fps,CAVLC,IPPIPP...,Frame only,Loop filter,Number Slice Groups=1,Num. of ref-frames=4,all-I""",stream_dir,BA_MW_D.264,output_dir,BA_MW_D.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,25b4d57bf245d6357522bfc5e537168a,25b4d57bf245d6357522bfc5e537168a,pass,
11,,cv0.5,,h264,,"""QCIF,Frame,CABAC,I,Loop Filter,Number Slice Groups=1,First frame""",stream_dir,CABA1_Sony_D.264,output_dir,CABA1_Sony_D.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,4b250c3affb3a422d59291c31d074519,4b250c3affb3a422d59291c31d074519,pass,
12,,cv0.5,,h264,,"""QCIF,Frame,CABAC,I,Loop Filter,Number Slice Groups=1,First Frame""",stream_dir,CABA1_SVA_B.264,output_dir,CABA1_SVA_B.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,d2b3d8463a77fb01b87e5ac353ccca2d,d2b3d8463a77fb01b87e5ac353ccca2d,pass,
13,,cv0.5,,h264,,"""QCIF,Frame,CABAC,IP,Loop Filter,Number Slice Groups=1,First frame""",stream_dir,CABA2_Sony_E.264,output_dir,CABA2_Sony_E.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,389b11729068e938f06641fdb16fa35d,389b11729068e938f06641fdb16fa35d,pass,
14,,cv0.5,,h264,,"""QCIF,Frame,CABAC,IP,Loop Filter,Number Slice Groups=1,First Frame""",stream_dir,CABA2_SVA_B.264,output_dir,CABA2_SVA_B.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,0415aa3cd1d6aeafcde5b67932d14191,0415aa3cd1d6aeafcde5b67932d14191,pass,
15,,cv0.5,,h264,,"""QCIF,Frame,CABAC,IPB,Loop Filter,Temporal,Number Slice Groups=1,First frame""",stream_dir,CABA3_Sony_C.264,output_dir,CABA3_Sony_C.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,6938a4c02fde446c4c9defa38d951d5b,6938a4c02fde446c4c9defa38d951d5b,pass,
16,,cv0.5,,h264,,"""QCIF,Frame,CABAC,IPB,Loop Filter,Temporal,Number Slice Groups=1,First Frame""",stream_dir,CABA3_SVA_B.264,output_dir,CABA3_SVA_B.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,6b3932589fdb43eee1c0b59e7275483f,6b3932589fdb43eee1c0b59e7275483f,pass,
17,,cv0.5,,h264,,"""Main,CABAC,IPPPPPPPPPPPPPP,Frame only""",stream_dir,CABA3_TOSHIBA_E.264,output_dir,CABA3_TOSHIBA_E.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,4dd735bc7d2da806381b758f0f6950b3,4dd735bc7d2da806381b758f0f6950b3,pass,
18,,cv0.5,,h264,,"""QCIF,Frame,CABAC,IPB,Loop Filter,Temporal,Number Slice Groups=1,First frame""",stream_dir,CABACI3_Sony_B.264,output_dir,CABACI3_Sony_B.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,2487cca10c122632a6f3afbf288c7fa6,2487cca10c122632a6f3afbf288c7fa6,pass,
19,0,cv0.5,,h264,,"""full,field,CABAC,IBBP,Loop Filter,temporal,Direct 8x8 Inference,Number Slice Groups=1,First only""",stream_dir,camp_mot_fld0_full.264,output_dir,camp_mot_fld0_full.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,field not supported
20,,cv0.5,,h264,,"""full,Frame,CABAC,IBBP,Loop Filter,temporal,Number Slice Groups=1,First only""",stream_dir,camp_mot_frm0_full.264,output_dir,camp_mot_frm0_full.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,05e3892458431625f09318778308801b,05e3892458431625f09318778308801b,pass,
21,0,cv0.5,,h264,,"""full,field,CABAC,IBBP,Loop Filter,temporal,Direct 8x8 Inference,Number Slice Groups=1,First only""",stream_dir,camp_mot_mbaff0_full.264,output_dir,camp_mot_mbaff0_full.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,field not supported
22,,cv0.5,,h264,,"""full,Picture AFF,CABAC,IBBP,Loop Filter,temporal,Direct 8x8 Inference,Number Slice Groups=1,First only""",stream_dir,camp_mot_picaff0_full.264,output_dir,camp_mot_picaff0_full.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,field not supported
23,,cv0.5,,h264,,"""CIF,Frame,CABAC,IPB (multiple slice types per picture),Loop Filter,Number Slice Groups=1,First frame""",stream_dir,CABAST3_Sony_E.264,output_dir,CABAST3_Sony_E.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,0895cc700536d441b321a99d03afa310,0895cc700536d441b321a99d03afa310,pass,
24,,cv0.5,,h264,,"""CIF,Frame,CABAC,IPB (multiple slice types per picture),Loop Filter,Number Slice Groups=1,First frame""",stream_dir,CABASTBR3_Sony_B.264,output_dir,CABASTBR3_Sony_B.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,d8cbe88b2ba3abf2f0e4a05d101a35e9,d8cbe88b2ba3abf2f0e4a05d101a35e9,pass,
25,0,cv0.5,,h264,,"""CIF,Field coded,CABAC,Loop Filter,Temporal,Direct 8x8 Inference,Number Slice Groups=1,First frame""",stream_dir,CABREF3_Sand_D.264,output_dir,CABREF3_Sand_D.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,field not supported
26,,cv0.5,,h264,,"""QCIF,Frame,CABAC,IPB,Loop Filter,Temporal,Direct 8x8 Inference,Number Slice Groups=1,First frame""",stream_dir,CACQP3_Sony_D.264,output_dir,CACQP3_Sony_D.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,05bf412ded750e9292977efa6234b921,05bf412ded750e9292977efa6234b921,pass,
27,0,cv0.5,,h264,,"""Video Resolution=720,Field,CABAC,IPB,Loop Filter,Spatial,Number Slice Groups=1,First Frame""",stream_dir,CAFI1_SVA_C.264,output_dir,CAFI1_SVA_C.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,field not supported
28,,cv0.5,,h264,,"""Video Resolution=720,MB-AFF,CABAC,I,Loop Filter,Temporal,Number Slice Groups=1,First frame""",stream_dir,CAMA1_Sony_C.264,output_dir,CAMA1_Sony_C.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,field not supported
29,,cv0.5,,h264,,"""Main,CABAC,Slice type=5,MB-AFF frame,first picture only""",stream_dir,CAMA1_TOSHIBA_B.264,output_dir,CAMA1_TOSHIBA_B.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,
30,0,cv0.5,,h264,,"""Video Resolution=720,First frame IP field bottom field first, the rest are MBAFF,CABAC,IPB,Loop Filter,Spatial,Direct 8x8 Inference=1,Number Slice Groups=1,First frame""",stream_dir,cama1_vtc_c.264,output_dir,cama1_vtc_c.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,field not supported
31,0,cv0.5,,h264,,"""Video Resolution=720,First frame IP field, the rest are MBAFF,CABAC,IPB,Spatial,Direct 8x8 Inference=1,Number Slice Groups=1,First frame""",stream_dir,cama2_vtc_b.264,output_dir,cama2_vtc_b.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,field not supported
32,,cv0.5,,h264,,"""CIF,MB Adaptive,CABAC,Loop Filter,Temporal,Direct 8x8 Inference,Number Slice Groups=1,First frame""",stream_dir,CAMA3_Sand_E.264,output_dir,CAMA3_Sand_E.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,field not supported
33,0,cv0.5,,h264,,"""Video Resolution=720,First frame IP field, the rest are MBAFF,CABAC,IPB,Loop Filter,Spatial,Direct 8x8 Inference=1,Number Slice Groups=1,First frame""",stream_dir,cama3_vtc_b.264,output_dir,cama3_vtc_b.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,field not supported
34,,cv0.5,,h264,,"""Video Resolution=192,Frame  MB AFF,CABAC,IPB,Loop Filter,Direct 8x8 Inference,Number Slice Groups=1,First frame,Constrained Intra Prediction""",stream_dir,CAMACI3_Sony_C.264,output_dir,CAMACI3_Sony_C.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,field not supported
35,,cv0.5,,h264,,"""Main,CABAC,Slice type=5,MB-AFF frame,first picture only""",stream_dir,CAMANL1_TOSHIBA_B.264,output_dir,CAMANL1_TOSHIBA_B.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,
36,,cv0.5,,h264,,"""Main,CABAC,Slice type=5,MB-AFF frame,first picture only""",stream_dir,CAMANL2_TOSHIBA_B.264,output_dir,CAMANL2_TOSHIBA_B.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,
37,,cv0.5,,h264,,"""CIF,MB Adaptive,CABAC,Temporal,Direct 8x8 Inference,Number Slice Groups=1,First frame""",stream_dir,CAMANL3_Sand_E.264,output_dir,CAMANL3_Sand_E.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,field not supported
38,,cv0.5,,h264,,"""Video Resolution=192,Frame  MB AFF,CABAC,IPB,Loop Filter,Direct 8x8 Inference,Number Slice Groups=1,First frame""",stream_dir,CAMASL3_Sony_B.264,output_dir,CAMASL3_Sony_B.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,field not supported
39,,cv0.5,,h264,,"""profile_idc=77,Video Resolution=720,mbaff,CABAC,IBBP,Loop Filter,Direct 8x8 Inference,Number Slice Groups=1,First only""",stream_dir,CAMP_MOT_MBAFF_L30.264,output_dir,CAMP_MOT_MBAFF_L30.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,field not supported
40,,cv0.5,,h264,,"""profile_idc=77,Video Resolution=720,MBAFF,CABAC,IBBP,Loop Filter,Direct 8x8 Inference,Number Slice Groups=1,First only""",stream_dir,CAMP_MOT_MBAFF_L31.264,output_dir,CAMP_MOT_MBAFF_L31.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,field not supported
41,,cv0.5,,h264,,"""QCIF,Frame,CABAC,I,Number Slice Groups=1,First frame""",stream_dir,CANL1_Sony_E.264,output_dir,CANL1_Sony_E.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,868687e1b66bc611bef723b65c524f30,868687e1b66bc611bef723b65c524f30,pass,
42,,cv0.5,,h264,,"""QCIF,Frame,CABAC,I,Number Slice Groups=1,First Frame""",stream_dir,CANL1_SVA_B.264,output_dir,CANL1_SVA_B.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,5c32cc068047b4fffaa8c4078a8d20fd,5c32cc068047b4fffaa8c4078a8d20fd,pass,
43,,cv0.5,,h264,,"""Main,CABAC,I-slice only,Frame only""",stream_dir,CANL1_TOSHIBA_G.264,output_dir,CANL1_TOSHIBA_G.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,0121ee4d86eee4a3e006300963cff8ad,0121ee4d86eee4a3e006300963cff8ad,pass,
44,,cv0.5,,h264,,"""QCIF,Frame,CABAC,IP,Number Slice Groups=1,First frame""",stream_dir,CANL2_Sony_E.264,output_dir,CANL2_Sony_E.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,5cb13d81e7d951c0e91119c2ee0f89f4,5cb13d81e7d951c0e91119c2ee0f89f4,pass,
45,,cv0.5,,h264,,"""QCIF,Frame,CABAC,I,Number Slice Groups=1,First Frame""",stream_dir,CANL2_SVA_B.264,output_dir,CANL2_SVA_B.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,5c32cc068047b4fffaa8c4078a8d20fd,5c32cc068047b4fffaa8c4078a8d20fd,pass,
46,,cv0.5,,h264,,"""QCIF,Frame,CABAC,IPB,Temporal,Number Slice Groups=1,First frame""",stream_dir,CANL3_Sony_C.264,output_dir,CANL3_Sony_C.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,d58a4d9dac40e795583af8d8237a03b5,d58a4d9dac40e795583af8d8237a03b5,pass,
47,,cv0.5,,h264,,"""QCIF,Frame,CABAC,IP,Number Slice Groups=1,First Frame""",stream_dir,CANL3_SVA_B.264,output_dir,CANL3_SVA_B.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,cb030eec8626991531f3f55a4b6d5b9a,cb030eec8626991531f3f55a4b6d5b9a,pass,
48,,cv0.5,,h264,,"""QCIF,Frame,CABAC,IPB,Spatial,Number Slice Groups=1,First Frame""",stream_dir,CANL4_SVA_B.264,output_dir,CANL4_SVA_B.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,df672cedf9a4537e4b7d0e1d00886394,df672cedf9a4537e4b7d0e1d00886394,pass,
49,,cv0.5,,h264,,"""Video Resolution=720,MB-AFF,CAVLC+CABAC,IP,Temporal,Number Slice Groups=1,First frame""",stream_dir,CANLMA2_Sony_C.264,output_dir,CANLMA2_Sony_C.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,field not supported
50,,cv0.5,,h264,,"""Video Resolution=720,MB-AFF,CABAC,IP,Temporal,Number Slice Groups=1,First frame""",stream_dir,CANLMA3_Sony_C.264,output_dir,CANLMA3_Sony_C.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,field not supported
51,,cv0.5,,h264,,"""Main,CABAC,Slice type=5,Picture-AFF,first picture only""",stream_dir,CAPA1_TOSHIBA_B.264,output_dir,CAPA1_TOSHIBA_B.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,
52,,cv0.5,,h264,,"""CIF,Picture-level adaptive and MB-level Adaptive,CABAC,Loop Filter,Temporal,Direct 8x8 Inference,Number Slice Groups=1,First frame""",stream_dir,CAPAMA3_Sand_F.264,output_dir,CAPAMA3_Sand_F.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,field not supported
53,,cv0.5,,h264,,"""CIF,Frame Coded,CABAC,Loop Filter,Temporal,Direct 8x8 Inference,Number Slice Groups=1,First frame""",stream_dir,CAPCM1_Sand_E.264,output_dir,CAPCM1_Sand_E.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,ee174f018019c2dd5fd74641594dc8ec,ee174f018019c2dd5fd74641594dc8ec,pass,
54,,cv0.5,,h264,,"""CIF,Frame Coded,CABAC,Temporal,Direct 8x8 Inference,Number Slice Groups=1,First frame""",stream_dir,CAPCMNL1_Sand_E.264,output_dir,CAPCMNL1_Sand_E.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,fe4e505ce2480d99d6fcd66995d0abbe,fe4e505ce2480d99d6fcd66995d0abbe,pass,
55,,cv0.5,,h264,,"""QCIF,Frame,CABAC,IPB,Loop Filter,Temporal,Direct 8x8 Inference,Number Slice Groups=1,First frame""",stream_dir,CAPM3_Sony_D.264,output_dir,CAPM3_Sony_D.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,5632d53aa44ba0703ae48df5a83ee7c1,5632d53aa44ba0703ae48df5a83ee7c1,pass,
56,,cv0.5,,h264,,"""QCIF,Frame,CABAC,I,Loop Filter,Number Slice Groups=1,First frame""",stream_dir,CAQP1_Sony_B.264,output_dir,CAQP1_Sony_B.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,4e8bc46423bcf32c39a2ffad11183bd0,4e8bc46423bcf32c39a2ffad11183bd0,pass,
57,0,cv0.5,,h264,,"""full,field,CAVLC,IBBP,Loop Filter,spatial,Direct 8x8 Inference,Number Slice Groups=1,First only""",stream_dir,cvmp_mot_fld0_full_B.264,output_dir,cvmp_mot_fld0_full_B.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,field not supported
58,,cv0.5,,h264,,"""full,Frame,CAVLC,IBBP,Loop Filter,spatial,Number Slice Groups=1,First only""",stream_dir,cvmp_mot_frm0_full_B.264,output_dir,cvmp_mot_frm0_full_B.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,4cbbafc17857ae327d5adb654f30b641,4cbbafc17857ae327d5adb654f30b641,pass,
59,0,cv0.5,,h264,,"""full,field,CAVLC,IBBP,Loop Filter,spatial,Direct 8x8 Inference,Number Slice Groups=1,First only""",stream_dir,cvmp_mot_mbaff0_full_B.264,output_dir,cvmp_mot_mbaff0_full_B.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,field not supported
60,,cv0.5,,h264,,"""full,Picture AFF,CAVLC,IBBP,Loop Filter,spatial,Direct 8x8 Inference,Number Slice Groups=1,First only""",stream_dir,cvmp_mot_picaff0_full_B.264,output_dir,cvmp_mot_picaff0_full_B.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,field not supported
61,,cv0.5,,h264,,"""Main,CABAC,IPPPP...,Frame only""",stream_dir,CAWP1_TOSHIBA_E.264,output_dir,CAWP1_TOSHIBA_E.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,c2a2e19e2b3879c0aa0280e084155020,c2a2e19e2b3879c0aa0280e084155020,pass,
62,,cv0.5,,h264,,"""Main,CABAC,Slice type=5,Frame-picture,first picture only""",stream_dir,CAWP5_TOSHIBA_E.264,output_dir,CAWP5_TOSHIBA_E.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,c15a214cec00eab27562fa57dd751ad4,c15a214cec00eab27562fa57dd751ad4,pass,
63,,cv0.5,,h264,,"""CIF,Frame,CAVLC,Every I,Constrained Intra Pred""",stream_dir,CI1_FT_B.264,output_dir,CI1_FT_B.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,8fe092403d050b458802d514819dec96,8fe092403d050b458802d514819dec96,pass,
64,,cv0.5,,h264,,"""Baseline,QCIF / 15fps,CAVLC,IPPIPP...,Frame only,Loop filter,Number Slice Groups=1,Num. of ref-frames=4,all-I,Constrained intra""",stream_dir,CI_MW_D.264,output_dir,CI_MW_D.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,031d90db6b60c81365c6107dea401250,031d90db6b60c81365c6107dea401250,pass,
65,,cv0.5,,h264,,"""QCIF,Frame,CAVLC,IPB,Loop Filter,Temporal,Direct 8x8 Inference,Number Slice Groups=1,First frame""",stream_dir,CVBS3_Sony_C.264,output_dir,CVBS3_Sony_C.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,221dea898f625835579d7fcd3224dcab,221dea898f625835579d7fcd3224dcab,pass,
66,,cv0.5,,h264,,"""Video Resolution=720,MB-AFF,CAVLC+CABAC,IP,Temporal,Number Slice Groups=1,First frame""",stream_dir,CVCANLMA2_Sony_C.264,output_dir,CVCANLMA2_Sony_C.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,field not supported
67,,cv0.5,,h264,,"""CIF,Frame,CAVLC,IP,Direct 8x8 Inference,Number Slice Groups=1,First frame""",stream_dir,CVFC1_Sony_C.264,output_dir,CVFC1_Sony_C.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,e6db41534ffa8db17fc48016e90e445d,e6db41534ffa8db17fc48016e90e445d,pass,
68,0,cv0.5,,h264,,"""Video Resolution=720,Field,CAVLC,IP,Loop Filter,Temporal,Number Slice Groups=1,First frame""",stream_dir,CVFI1_Sony_D.264,output_dir,CVFI1_Sony_D.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,field not supported
69,0,cv0.5,,h264,,"""Video Resolution=720,Field,CAVLC,IP,Loop Filter,Number Slice Groups=1,First Frame""",stream_dir,CVFI1_SVA_C.264,output_dir,CVFI1_SVA_C.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,field not supported
70,0,cv0.5,,h264,,"""Video Resolution=720,Field,CAVLC,IPB,Loop Filter,Temporal,Direct 8x8 Inference,Number Slice Groups=1,First frame""",stream_dir,CVFI2_Sony_H.264,output_dir,CVFI2_Sony_H.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,field not supported
71,0,cv0.5,,h264,,"""Video Resolution=720,Field,CAVLC,IPB,Loop Filter,Spatial,Number Slice Groups=1,First Frame""",stream_dir,CVFI2_SVA_C.264,output_dir,CVFI2_SVA_C.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,field not supported
72,,cv0.5,,h264,,"""Video Resolution=720,MB-AFF,CAVLC,I,Loop Filter,Temporal,Number Slice Groups=1,First frame""",stream_dir,CVMA1_Sony_D.264,output_dir,CVMA1_Sony_D.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,field not supported
73,,cv0.5,,h264,,"""Main,CAVLC,Slice type=5,MB-AFF frame,first picture only""",stream_dir,CVMA1_TOSHIBA_B.264,output_dir,CVMA1_TOSHIBA_B.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,
74,,cv0.5,,h264,,"""Main,CAVLC,Slice type=5,MB-AFF frame,first picture only""",stream_dir,CVMANL1_TOSHIBA_B.264,output_dir,CVMANL1_TOSHIBA_B.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,
75,,cv0.5,,h264,,"""Main,CAVLC,Slice type=5,MB-AFF frame,first picture only""",stream_dir,CVMANL2_TOSHIBA_B.264,output_dir,CVMANL2_TOSHIBA_B.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,
76,0,cv0.5,,h264,,"""Video Resolution=720,Field + Frame  MB AFF,CAVLC,IPB,Loop Filter,Direct 8x8 Inference,SEI,Number Slice Groups=1,First frame""",stream_dir,CVMAPAQP3_Sony_E.264,output_dir,CVMAPAQP3_Sony_E.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,field not supported
77,,cv0.5,,h264,,"""Video Resolution=192,Frame  MB AFF,CAVLC,IP,Loop Filter,Direct 8x8 Inference,Number Slice Groups=1,First frame""",stream_dir,CVMAQP2_Sony_G.264,output_dir,CVMAQP2_Sony_G.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,field not supported
78,,cv0.5,,h264,,"""Video Resolution=192,Frame  MB AFF,CAVLC,IPB,Loop Filter,Direct 8x8 Inference,SEI,Number Slice Groups=1,First frame""",stream_dir,CVMAQP3_Sony_D.264,output_dir,CVMAQP3_Sony_D.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,field not supported
79,0,cv0.5,,h264,,"""profile_idc=77,Video Resolution=720,Field,CAVLC,IBBP,Loop Filter,Direct 8x8 Inference,Number Slice Groups=1,First only""",stream_dir,CVMP_MOT_FLD_L30_B.264,output_dir,CVMP_MOT_FLD_L30_B.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,field not supported
80,,cv0.5,,h264,,"""profile_idc=77,Video Resolution=720,Frame,CAVLC,IBBP,Loop Filter,Direct 8x8 Inference,Number Slice Groups=1,First only""",stream_dir,CVMP_MOT_FRM_L31_B.264,output_dir,CVMP_MOT_FRM_L31_B.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,field not supported
81,0,cv0.5,,h264,,"""Video Resolution=720,Field,CAVLC,IP,Number Slice Groups=1,First frame""",stream_dir,CVNLFI1_Sony_C.264,output_dir,CVNLFI1_Sony_C.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,field not supported
82,0,cv0.5,,h264,,"""Video Resolution=720,Field,CAVLC,IPB,Temporal,Direct 8x8 Inference,Number Slice Groups=1,First frame""",stream_dir,CVNLFI2_Sony_H.264,output_dir,CVNLFI2_Sony_H.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,field not supported
83,,cv0.5,,h264,,"""Main,CAVLC,Slice type=5,Picture-AFF,first picture only""",stream_dir,CVPA1_TOSHIBA_B.264,output_dir,CVPA1_TOSHIBA_B.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,
84,,cv0.5,,h264,,"""CIF,Frame,CAVLC,I,Direct 8x8 Inference,Number Slice Groups=1,First Frame""",stream_dir,CVPCMNL1_SVA_C.264,output_dir,CVPCMNL1_SVA_C.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,10fa180008a31931e4dc34b0ba8d5865,10fa180008a31931e4dc34b0ba8d5865,pass,
85,,cv0.5,,h264,,"""Video Resolution=1280,Frame,CAVLC,I,Direct 8x8 Inference,Number Slice Groups=1,First Frame""",stream_dir,CVPCMNL2_SVA_C.264,output_dir,CVPCMNL2_SVA_C.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,9ffa38168fcf615b296b0f5d3fdb09a5,9ffa38168fcf615b296b0f5d3fdb09a5,pass,
86,,cv0.5,,h264,,"""QCIF,Frame,CAVLC,IP,Loop Filter,Temporal,Direct 8x8 Inference,SEI,VUI,Number Slice Groups=1,First frame""",stream_dir,CVSE2_Sony_B.264,output_dir,CVSE2_Sony_B.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,b3bfbe79671a51daf2a24059103cda77,b3bfbe79671a51daf2a24059103cda77,pass,
87,,cv0.5,,h264,,"""QCIF,Frame,CAVLC,IPB,Loop Filter,Temporal,Direct 8x8 Inference,SEI,VUI,Number Slice Groups=1,First frame""",stream_dir,CVSE3_Sony_H.264,output_dir,CVSE3_Sony_H.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,fb9b9c867ca71d1ac68982c3980eb840,fb9b9c867ca71d1ac68982c3980eb840,pass,
88,,cv0.5,,h264,,"""QCIF,Frame,CAVLC,IPB,Loop Filter,Temporal,Direct 8x8 Inference,SEI,VUI,Number Slice Groups=1,First frame""",stream_dir,CVSEFDFT3_Sony_E.264,output_dir,CVSEFDFT3_Sony_E.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,99f2beb72be7bc16e3e02df47e42b4d0,99f2beb72be7bc16e3e02df47e42b4d0,pass,
89,,cv0.5,,h264,,"""Main,CAVLC,IPPPPc,Frame only""",stream_dir,CVWP1_TOSHIBA_E.264,output_dir,CVWP1_TOSHIBA_E.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,76bf51474de9af40637e546ca080ae64,76bf51474de9af40637e546ca080ae64,pass,
90,,cv0.5,,h264,,"""Main,CAVLC,PPIBBPBBP...,Frame only""",stream_dir,CVWP2_TOSHIBA_E.264,output_dir,CVWP2_TOSHIBA_E.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,ca19ac8c11be389497970940cffcf0c6,ca19ac8c11be389497970940cffcf0c6,pass,
91,,cv0.5,,h264,,"""Main,CAVLC,PPIBBPBBP...,Frame only""",stream_dir,CVWP3_TOSHIBA_E.264,output_dir,CVWP3_TOSHIBA_E.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,22ed3b3f41b1ecd2e63338f093a1deaa,22ed3b3f41b1ecd2e63338f093a1deaa,pass,
92,,cv0.5,,h264,,"""Main,CAVLC,Slice type=5,Frame-picture,first picture only""",stream_dir,CVWP5_TOSHIBA_E.264,output_dir,CVWP5_TOSHIBA_E.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,c15a214cec00eab27562fa57dd751ad4,c15a214cec00eab27562fa57dd751ad4,pass,
93,0,cv0.5,,h264,,"""Video Resolution=192,Field,CAVLC,I,Loop Filter,Direct 8x8 Inference,Number Slice Groups=1""",stream_dir,FI1_Sony_E.264,output_dir,FI1_Sony_E.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,field not supported
94,,cv0.5,,h264,,,stream_dir,FM1_BT_B.264,output_dir,FM1_BT_B.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,
95,,cv0.5,,h264,,"""QCIF,Frame,CAVLC,Used (Gradual Recovery Procedure),Every I,Constrained Intra Pred""",stream_dir,FM1_FT_E.264,output_dir,FM1_FT_E.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,baa36b02c252b45853ce56c5863afd6e,baa36b02c252b45853ce56c5863afd6e,pass,
96,,cv0.5,,h264,,"""QCIF,Frame,CAVLC,IP,Loop Filter,Number Slice Groups=3,First Frame""",stream_dir,FM2_SVA_C.264,output_dir,FM2_SVA_C.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,FMO not supported
97,,cv0.5,,h264,,"""Baseline,CIF,Frame,CAVLC,I,P,Hierarchical, GOP Size 16,Loop Filter,Ref Pic List Reorder,Adapt Ref Pic Mark,Direct 8x8 Inference,Number Slice Groups=1""",stream_dir,HCBP1_HHI_A.264,output_dir,HCBP1_HHI_A.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,d3e2b60161458636d84afdacf20a22e1,d3e2b60161458636d84afdacf20a22e1,pass,
98,,cv0.5,,h264,,"""Baseline,CIF,Frame,CAVLC,I,P,Hierarchical, GOP Size 16,Loop Filter,Ref Pic List Reorder,Adapt Ref Pic Mark,Direct 8x8 Inference,Number Slice Groups=1""",stream_dir,HCBP2_HHI_A.264,output_dir,HCBP2_HHI_A.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,21c1514f6218103e39790c6080e52bd3,21c1514f6218103e39790c6080e52bd3,pass,
99,,cv0.5,,h264,,"""Main,CIF,Frame,CABAC,I,P,B,Hierarchical, GOP Size 16,Loop Filter,Ref Pic List Reorder,Adapt Ref Pic Mark,Spatial,Direct 8x8 Inference,Number Slice Groups=1""",stream_dir,HCMP1_HHI_A.264,output_dir,HCMP1_HHI_A.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,571e9a26e5a2b110a88c133a68c6f4c5,571e9a26e5a2b110a88c133a68c6f4c5,pass,
100,,cv0.5,,h264,,"""QCIF,Frame,CAVLC,IP,Loop Filter,Direct 8x8 Inference,Number Slice Groups=1,First Frame""",stream_dir,LS_SVA_D.264,output_dir,LS_SVA_D.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,16ab30e74c196b107f5ffd3b42cb7669,16ab30e74c196b107f5ffd3b42cb7669,pass,
101,,cv0.5,,h264,,"""Baseline,QCIF / 15fps,CAVLC,IPPIPP...,Frame only,Loop filter,Number Slice Groups=1,Num. of ref-frames=4,every 2 Intra""",stream_dir,MIDR_MW_D.264,output_dir,MIDR_MW_D.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,2b96cae56d296d1b5377bd46aa319062,2b96cae56d296d1b5377bd46aa319062,pass,
102,,cv0.5,,h264,,"""Baseline,QCIF / 15fps,CAVLC,IPPIPP...,Frame only,Loop filter,Number Slice Groups=1,Num. of ref-frames=3,all-I""",stream_dir,MPS_MW_A.264,output_dir,MPS_MW_A.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,462826ccabe4770e764b44d9c4107b90,462826ccabe4770e764b44d9c4107b90,pass,
103,,cv0.5,,h264,,,stream_dir,MR1_BT_A.264,output_dir,MR1_BT_A.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,58e177dd1712aa01e4e369ba1513b5e1,58e177dd1712aa01e4e369ba1513b5e1,pass,
104,,cv0.5,,h264,,"""Baseline,QCIF / 15fps,CAVLC,IPPIPP...,Frame only,Loop filter,Ref Pic List Reorder,Number Slice Groups=1,Num. of ref-frames=3,all-I""",stream_dir,MR1_MW_A.264,output_dir,MR1_MW_A.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,1cf1045bda6a6f9637266f81d397ad78,1cf1045bda6a6f9637266f81d397ad78,pass,
105,,cv0.5,,h264,,"""Baseline,QCIF / 15fps,CAVLC,IPPIPP...,Frame only,Loop filter,Number Slice Groups=1,Long-term Ref Frames,MMCO,Num. of ref-frames=3,all-I""",stream_dir,MR2_MW_A.264,output_dir,MR2_MW_A.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,47a8a3718aec6756b391306d40b9c153,47a8a3718aec6756b391306d40b9c153,pass,
106,,cv0.5,,h264,,"""QCIF,Frame,CAVLC,IPPP,Loop Filter,Ref Pic List Reorder,Adapt Ref Pic Mark,Number Slice Groups=1,Long-term Ref Frames,MMCO,First only""",stream_dir,MR2_TANDBERG_E.264,output_dir,MR2_TANDBERG_E.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,f147dd19a1fa3588593184a8136f4049,f147dd19a1fa3588593184a8136f4049,pass,
107,,cv0.5,,h264,,"""QCIF,Frame,CAVLC,IPPP,Loop Filter,Ref Pic List Reorder,Adapt Ref Pic Mark,Number Slice Groups=1,Long-term Ref Frames,MMCO,First only""",stream_dir,MR3_TANDBERG_B.264,output_dir,MR3_TANDBERG_B.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,4d3e6d10b66d91837920cb3bea297bcb,4d3e6d10b66d91837920cb3bea297bcb,pass,
108,,cv0.5,,h264,,"""QCIF,Frame,CAVLC,IPPP,Loop Filter,Ref Pic List Reorder,Adapt Ref Pic Mark,Number Slice Groups=1,Long-term Ref Frames,MMCO,First only""",stream_dir,MR4_TANDBERG_C.264,output_dir,MR4_TANDBERG_C.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,c6dbce07e51b636e17119032d99476ab,c6dbce07e51b636e17119032d99476ab,pass,
109,,cv0.5,,h264,,"""QCIF,Frame,CAVLC,IPPP,Loop Filter,Ref Pic List Reorder,Adapt Ref Pic Mark,Number Slice Groups=1,Long-term Ref Frames,MMCO,First only""",stream_dir,MR5_TANDBERG_C.264,output_dir,MR5_TANDBERG_C.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,c6dbce07e51b636e17119032d99476ab,c6dbce07e51b636e17119032d99476ab,pass,
110,0,cv0.5,,h264,,"""Video Resolution=192,Field,CAVLC,IPPP,Loop Filter,Ref Pic List Reordering,Direct 8x8 Inference,VUI,Number Slice Groups=1,Long-term Ref Frames=2,MMCO,First frame""",stream_dir,MR6_BT_B.264,output_dir,MR6_BT_B.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,field not supported
111,0,cv0.5,,h264,,"""Video Resolution=192,Frame Field Adaptive,CAVLC,IPPP,Loop Filter,Ref Pic List Reordering,Direct 8x8 Inference,VUI,Number Slice Groups=1,Long-term Ref Frames=2,MMCO,First frame""",stream_dir,MR7_BT_B.264,output_dir,MR7_BT_B.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,field not supported
112,0,cv0.5,,h264,,"""Video Resolution=192,Field Coding,CAVLC,IBBP,Loop Filter,Ref Pic List Reordering,Direct Prediction,Direct 8x8 Inference,VUI,Number Slice Groups=1,Long-term Ref Frames=2,MMCO,First frame""",stream_dir,MR8_BT_B.264,output_dir,MR8_BT_B.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,field not supported
113,0,cv0.5,,h264,,"""Video Resolution=192,Macroblock and Picture Frame/Field Adaptive,CABAC,IBBP,Loop Filter,Ref Pic List Reordering,Direct Prediction,Direct 8x8 Inference,VUI,Number Slice Groups=1,Long-term Ref Frames=2,MMCO,First frame""",stream_dir,MR9_BT_B.264,output_dir,MR9_BT_B.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,field not supported
114,,cv0.5,,h264,,,stream_dir,src19td.IBP.264,output_dir,src19td.IBP.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,470adbc981ac397f74972a9f21b384a8,470adbc981ac397f74972a9f21b384a8,pass,
115,,cv0.5,,h264,,"""QCIF,Frame,CAVLC,I,Direct 8x8 Inference,Number Slice Groups=1,First only""",stream_dir,NL1_Sony_D.264,output_dir,NL1_Sony_D.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,3d2575e15180403f8b5b6eb49b9197c7,3d2575e15180403f8b5b6eb49b9197c7,pass,
116,,cv0.5,,h264,,"""QCIF,Frame,CAVLC,IP,Direct 8x8 Inference,Number Slice Groups=1,First only""",stream_dir,NL2_Sony_H.264,output_dir,NL2_Sony_H.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,75afd5d0d670c2be28b3f1de6fc75a6f,75afd5d0d670c2be28b3f1de6fc75a6f,pass,
117,,cv0.5,,h264,,"""QCIF,Frame,CAVLC,IPB,Spatial,Number Slice Groups=1,First Frame""",stream_dir,NL3_SVA_E.264,output_dir,NL3_SVA_E.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,5df2d92792b038c0a1531d34dc0a7ed4,5df2d92792b038c0a1531d34dc0a7ed4,pass,
118,,cv0.5,,h264,,"""QCIF,Frame,CAVLC,I only,Direct 8x8 Inference,Number Slice Groups=1,First only""",stream_dir,NLMQ1_JVC_C.264,output_dir,NLMQ1_JVC_C.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,4ff1e4e950d6235283a4d0ca3efac887,4ff1e4e950d6235283a4d0ca3efac887,pass,
119,,cv0.5,,h264,,"""QCIF,Frame,CAVLC,IP,Direct 8x8 Inference,Number Slice Groups=1,First only""",stream_dir,NLMQ2_JVC_C.264,output_dir,NLMQ2_JVC_C.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,37a8ee10b85010880cf2e4922ee43ea7,37a8ee10b85010880cf2e4922ee43ea7,pass,
120,,cv0.5,,h264,,"""Baseline,QCIF / 15fps,CAVLC,IPPIPP...,Frame only,Loop filter,Number Slice Groups=1,Num. of ref-frames=3,all-I,Non-reference frame use""",stream_dir,NRF_MW_E.264,output_dir,NRF_MW_E.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,dee96a363f981f173452b1b6b75eb138,dee96a363f981f173452b1b6b75eb138,pass,
121,0,cv0.5,,h264,,"""Video Resolution=720,Field,CAVLC,IP,Number Slice Groups=1,First frame""",stream_dir,Sharp_MP_Field_1_B.264,output_dir,Sharp_MP_Field_1_B.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,field not supported
122,0,cv0.5,,h264,,"""Video Resolution=720,Field,CAVLC,IP,Number Slice Groups=1,First frame""",stream_dir,Sharp_MP_Field_2_B.264,output_dir,Sharp_MP_Field_2_B.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,field not supported
123,0,cv0.5,,h264,,"""Video Resolution=720,Field,CAVLC,IP,Direct Prediction,Direct 8x8 Inference,Number Slice Groups=1,First frame""",stream_dir,Sharp_MP_Field_3_B.264,output_dir,Sharp_MP_Field_3_B.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,field not supported
124,0,cv0.5,,h264,,"""Video Resolution=720,Picture-level Adaptive-Frame-Field Coding,CAVLC,IP,Spatial,Direct 8x8 Inference,Number Slice Groups=1,First frame""",stream_dir,Sharp_MP_PAFF_1r2.264,output_dir,Sharp_MP_PAFF_1r2.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,field not supported
125,0,cv0.5,,h264,,"""Video Resolution=720,Picture-level Adaptive-Frame-Field Coding,CABAC,IP,Spatial,Direct 8x8 Inference,Number Slice Groups=1,First frame""",stream_dir,Sharp_MP_PAFF_2.264,output_dir,Sharp_MP_PAFF_2.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,field not supported
126,,cv0.5,,h264,,"""QCIF,Frame,CAVLC,IPB,Loop Filter,Temporal,Number Slice Groups=1,First Frame""",stream_dir,SL1_SVA_B.264,output_dir,SL1_SVA_B.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,548c00397b8de280c1c324228e9e8d43,548c00397b8de280c1c324228e9e8d43,pass,
127,,cv0.5,,h264,,,stream_dir,sp1_bt_a.264,output_dir,sp1_bt_a.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,453e17115eb28fe7b252a86994efa1be,453e17115eb28fe7b252a86994efa1be,pass,
128,,cv0.5,,h264,,,stream_dir,sp2_bt_b.264,output_dir,sp2_bt_b.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,4b268e6d992cf877e85cb70c804ada4d,4b268e6d992cf877e85cb70c804ada4d,pass,
129,,cv0.5,,h264,,"""QCIF,Frame,CAVLC,I,Loop Filter,Direct 8x8 Inference,Number Slice Groups=1,First Frame""",stream_dir,SVA_BA1_B.264,output_dir,SVA_BA1_B.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,75be4cd1861221944796cc77a61e7c54,75be4cd1861221944796cc77a61e7c54,pass,
130,,cv0.5,,h264,,"""QCIF,Frame,CAVLC,IP,Loop Filter,Direct 8x8 Inference,Number Slice Groups=1,First Frame""",stream_dir,SVA_BA2_D.264,output_dir,SVA_BA2_D.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,65920e04073ecb0ba3003af5d29f9dfb,65920e04073ecb0ba3003af5d29f9dfb,pass,
131,,cv0.5,,h264,,"""QCIF,Frame,CAVLC,IP,Loop Filter,Direct 8x8 Inference,Number Slice Groups=1,First Frame,Constrained Intra Pred""",stream_dir,SVA_Base_B.264,output_dir,SVA_Base_B.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,0076c060afd85b48536c69a0328c4682,0076c060afd85b48536c69a0328c4682,pass,
132,,cv0.5,,h264,,"""QCIF,Frame,CAVLC,IP,Direct 8x8 Inference,Number Slice Groups=1,First Frame""",stream_dir,SVA_CL1_E.264,output_dir,SVA_CL1_E.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,cb356feddfc379c3011325e04a41c252,cb356feddfc379c3011325e04a41c252,pass,
133,,cv0.5,,h264,,"""QCIF,Frame,CAVLC,IP,Loop Filter,Direct 8x8 Inference,Number Slice Groups=1,First Frame""",stream_dir,SVA_FM1_E.264,output_dir,SVA_FM1_E.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,ed5f8e6c082596d9c1d643fc4f7e00ca,ed5f8e6c082596d9c1d643fc4f7e00ca,pass,
134,,cv0.5,,h264,,"""QCIF,Frame,CAVLC,IP,Direct 8x8 Inference,Number Slice Groups=1,First Frame""",stream_dir,SVA_NL1_B.264,output_dir,SVA_NL1_B.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,b787416ba47f5f49fd36979bfdf8a9fd,b787416ba47f5f49fd36979bfdf8a9fd,pass,
135,,cv0.5,,h264,,"""QCIF,Frame,CAVLC,IP,Direct 8x8 Inference,Number Slice Groups=1,First Frame""",stream_dir,SVA_NL2_E.264,output_dir,SVA_NL2_E.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,1db099947255752886eb5e20c9877aa2,1db099947255752886eb5e20c9877aa2,pass,
136,,cv0.5,,h264,,"""""",stream_dir,alphaconformanceG.264,output_dir,alphaconformanceG.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,844039b6a86262cb1ad0e923496aec26,844039b6a86262cb1ad0e923496aec26,pass,
137,,cv0.5,,h264,,"""""",stream_dir,test8b43.264,output_dir,test8b43.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,c20cacb9f422c6273637864950b7c6cc,c20cacb9f422c6273637864950b7c6cc,pass,
138,0,cv0.5,,h264,,"""High,CIF,Field Coded,CABAC,Temporal,Direct 8x8 Inference,Number Slice Groups=1,First frame""",stream_dir,freh10.264,output_dir,freh10.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,field not supported
139,0,cv0.5,,h264,,"""High,CIF,Field Coded,CABAC,Loop Filter,Temporal,Direct 8x8 Inference,Number Slice Groups=1,First frame""",stream_dir,freh11.264,output_dir,freh11.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,field not supported
140,,cv0.5,,h264,,"""High,CIF,Frame Coded,CABAC,Loop Filter,Spatial,Direct 8x8 Inference,Number Slice Groups=1,First frame""",stream_dir,freh3.264,output_dir,freh3.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,7e0f346db9a37af1c00aa7c4cff9dd2e,7e0f346db9a37af1c00aa7c4cff9dd2e,pass,
141,0,cv0.5,,h264,,"""High,CIF,Frame/Field Coded,CABAC,Loop Filter,Spatial,Direct 8x8 Inference,Number Slice Groups=1,First frame""",stream_dir,freh4.264,output_dir,freh4.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,field not supported
142,,cv0.5,,h264,,"""High,CIF,MBAFF Coded,CABAC,Loop Filter,Spatial,Direct 8x8 Inference,Number Slice Groups=1,First frame""",stream_dir,freh5.264,output_dir,freh5.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,field not supported
143,0,cv0.5,,h264,,"""High,CIF,Field/MBAFF Coded,CAVLC,Loop Filter,Temporal,Direct 8x8 Inference,Number Slice Groups=1,First frame""",stream_dir,freh6.264,output_dir,freh6.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,field not supported
144,,cv0.5,,h264,,"""High,CIF,Frame Coded,CABAC,Temporal,Direct 8x8 Inference,Number Slice Groups=1,First frame""",stream_dir,freh8.264,output_dir,freh8.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,d7c28131318c31794e0060136274fa7a,d7c28131318c31794e0060136274fa7a,pass,
145,,cv0.5,,h264,,"""High,CIF,Frame Coded,CABAC,Loop Filter,Temporal,Direct 8x8 Inference,Number Slice Groups=1,First frame""",stream_dir,freh9.264,output_dir,freh9.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,60e03bae3f1c893f18a5fac23beaca9e,60e03bae3f1c893f18a5fac23beaca9e,pass,
146,,cv0.5,,h264,,,stream_dir,WB_10bit_QP21_1920x1088.264,output_dir,WB_10bit_QP21_1920x1088.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,d235a388f0d37a8ce0f5db34d2f719e8,d235a388f0d37a8ce0f5db34d2f719e8,pass,
147,,cv0.5,,h264,,"""High,CIF,Frame Coded,CABAC,Loop Filter,Temporal,Number Slice Groups=1,First frame""",stream_dir,Freh12_B.264,output_dir,Freh12_B.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,56d47b6000094b75591004eba67131c3,56d47b6000094b75591004eba67131c3,pass,
148,,cv0.5,,h264,,"""High,CIF,Frame Coded,CAVLC,Loop Filter,Spatial,Number Slice Groups=1,First frame""",stream_dir,Freh1_B.264,output_dir,Freh1_B.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,9f21e857ea369711f0a292b9c4b9d8b9,9f21e857ea369711f0a292b9c4b9d8b9,pass,
149,,cv0.5,,h264,,"""High,CIF,Frame Coded,CABAC,Loop Filter,Temporal,Number Slice Groups=1,First frame""",stream_dir,Freh2_B.264,output_dir,Freh2_B.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,fc7765ca2b289acc9b30471afcf2dd86,fc7765ca2b289acc9b30471afcf2dd86,pass,
150,0,cv0.5,,h264,,"""High,CIF,Field/MBAFF Coded,CABAC,Loop Filter,Temporal,Number Slice Groups=1,First frame""",stream_dir,Freh7_B.264,output_dir,Freh7_B.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,field not supported
151,,cv0.5,,h264,,"""High Profile,CABAC,Loop Filter,IDR Intervals=1""",stream_dir,FREXT01_JVC_D.264,output_dir,FREXT01_JVC_D.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,field not supported
152,,cv0.5,,h264,,"""High Profile,CAVLC,Loop Filter,IDR Intervals=1""",stream_dir,FREXT02_JVC_C.264,output_dir,FREXT02_JVC_C.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,field not supported
153,,cv0.5,,h264,,"""Hi,CAVLC,Loop Filter,All I pictures""",stream_dir,FRExt1_Panasonic.264,output_dir,FRExt1_Panasonic.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,8692e22e67223dec1531cb66ddad474a,8692e22e67223dec1531cb66ddad474a,pass,
154,,cv0.5,,h264,,"""CIF - 4:2:2/8,Frame,CAVLC,IPPPP,Number Slice Groups=1,First only""",stream_dir,FREXT1_TANDBERG_A.264,output_dir,FREXT1_TANDBERG_A.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,422 not supported
155,,cv0.5,,h264,,"""Hi,CAVLC,Loop Filter,MMCO,All I pictures""",stream_dir,FRExt2_Panasonic.264,output_dir,FRExt2_Panasonic.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,field not supported
156,,cv0.5,,h264,,"""CIF - 4:2:2/8,Frame,CAVLC,IPPPP,Loop Filter,Number Slice Groups=1,First only""",stream_dir,FREXT2_TANDBERG_A.264,output_dir,FREXT2_TANDBERG_A.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,422 not supported
157,,cv0.5,,h264,,"""Hi,CABAC,Loop Filter,IDR Intervals=1""",stream_dir,FRExt3_Panasonic.264,output_dir,FRExt3_Panasonic.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,9343a3c64fa18038508b9ee3aecfafbc,9343a3c64fa18038508b9ee3aecfafbc,pass,
158,,cv0.5,,h264,,"""CIF - 4:2:2/8,Frame,CAVLC,IBPBP,Loop Filter,Number Slice Groups=1,First only""",stream_dir,FREXT3_TANDBERG_A.264,output_dir,FREXT3_TANDBERG_A.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,422 not supported
159,,cv0.5,,h264,,"""Hi,CABAC,Loop Filter,IDR Intervals=1""",stream_dir,FRExt4_Panasonic.264,output_dir,FRExt4_Panasonic.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,field not supported
160,,cv0.5,,h264,,"""High Profile,Video Resolution=352,Frame,CABAC,IPB,Loop Filter,Spatial,Number Slice Groups=1,Long-term Ref Frames,MMCO,First frame""",stream_dir,FRExt_MMCO4_Sony_B.264,output_dir,FRExt_MMCO4_Sony_B.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,47c8592c5c547e1d925efb58b8d60de2,47c8592c5c547e1d925efb58b8d60de2,pass,
161,,cv0.5,,h264,,"""Hi,CABAC (adaptive initilaization),Loop Filter,IDR Intervals""",stream_dir,HCAFF1_HHI.264,output_dir,HCAFF1_HHI.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,field not supported
162,,cv0.5,,h264,,"""Hi,CABAC (adaptive initilaization),Loop Filter,IDR Intervals""",stream_dir,HCAFR1_HHI.264,output_dir,HCAFR1_HHI.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,0e6fe9cb6d373f860ad4687841dfa1c2,0e6fe9cb6d373f860ad4687841dfa1c2,pass,
163,,cv0.5,,h264,,"""Hi,CABAC (adaptive initilaization),Loop Filter,IDR Intervals""",stream_dir,HCAFR2_HHI.264,output_dir,HCAFR2_HHI.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,883d5a6e0b22dc45d84c755bae36fc68,883d5a6e0b22dc45d84c755bae36fc68,pass,
164,,cv0.5,,h264,,"""Hi,CABAC (adaptive initilaization),Loop Filter,IDR Intervals""",stream_dir,HCAFR3_HHI.264,output_dir,HCAFR3_HHI.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,9bd37b8f2e945ac3dd9ba28e4b93b911,9bd37b8f2e945ac3dd9ba28e4b93b911,pass,
165,,cv0.5,,h264,,"""Hi,CABAC (adaptive initilaization),Loop Filter,IDR Intervals""",stream_dir,HCAFR4_HHI.264,output_dir,HCAFR4_HHI.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,a4e4264dde393771bc38aea571b3e942,a4e4264dde393771bc38aea571b3e942,pass,
166,,cv0.5,,h264,,"""Hi,CABAC (adaptive initilaization),Loop Filter,IDR Intervals""",stream_dir,HCAMFF1_HHI.264,output_dir,HCAMFF1_HHI.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,field not supported
167,,cv0.5,,h264,,"""High,CIF,Frame,CABAC,I,P,B,Hierarchical, GOP Size 16,Loop Filter,Ref Pic List Reorder,Adapt Ref Pic Mark,Spatial,Direct 8x8 Inference,Number Slice Groups=1""",stream_dir,HCHP2_HHI_A.264,output_dir,HCHP2_HHI_A.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,a68b5e05ad7eabf77d99d52c247f5f3d,a68b5e05ad7eabf77d99d52c247f5f3d,pass,
168,0,cv0.5,,h264,,"""High,SD (720x576),Adaptive Frame/Field,CABAC,I,P,B,Hierarchical, GOP Size 16,Loop Filter,Ref Pic List Reorder,Adapt Ref Pic Mark,Spatial,Direct 8x8 Inference,Number Slice Groups=1""",stream_dir,HCHP3_HHI_A.264,output_dir,HCHP3_HHI_A.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,field not supported
169,,cv0.5,,h264,,"""CIF - 4:2:2,Frame,CABAC,IIIII,Loop Filter,Number Slice Groups=1,IDR=1""",stream_dir,Hi422FR10_SONY_B.264,output_dir,Hi422FR10_SONY_B.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,422 not supported
170,,cv0.5,,h264,,"""CIF - 4:2:2,Frame,CABAC,IPPPP,Loop Filter,Number Slice Groups=1,First only""",stream_dir,Hi422FR11_SONY_B.264,output_dir,Hi422FR11_SONY_B.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,422 not supported
171,,cv0.5,,h264,,"""CIF - 4:2:2,Frame,CABAC,IPBPB,Loop Filter,Number Slice Groups=1,First only""",stream_dir,Hi422FR12_SONY_B.264,output_dir,Hi422FR12_SONY_B.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,422 not supported
172,,cv0.5,,h264,,"""CIF - 4:2:2,Frame,CABAC,IIIII,Loop Filter,Number Slice Groups=1,IDR=1""",stream_dir,Hi422FR13_SONY_B.264,output_dir,Hi422FR13_SONY_B.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,422 not supported
173,,cv0.5,,h264,,"""CIF - 4:2:2,Frame,CABAC,IPPPP,Loop Filter,Number Slice Groups=1,First only""",stream_dir,Hi422FR14_SONY_B.264,output_dir,Hi422FR14_SONY_B.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,422 not supported
174,,cv0.5,,h264,,"""CIF - 4:2:2,Frame,CABAC,IPBPB,Loop Filter,Number Slice Groups=1,First only""",stream_dir,Hi422FR15_SONY_B.264,output_dir,Hi422FR15_SONY_B.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,422 not supported
175,,cv0.5,,h264,,"""CIF - 4:2:2,Frame,CAVLC,IIIII,Number Slice Groups=1,IDR=1""",stream_dir,Hi422FR1_SONY_A.264,output_dir,Hi422FR1_SONY_A.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,422 not supported
176,,cv0.5,,h264,,"""CIF - 4:2:2,Frame,CAVLC,IPPPP,Number Slice Groups=1,First only""",stream_dir,Hi422FR2_SONY_A.264,output_dir,Hi422FR2_SONY_A.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,422 not supported
177,,cv0.5,,h264,,"""CIF - 4:2:2,Frame,CAVLC,IPBPB,Direct 8x8 Inference,Number Slice Groups=1,First only""",stream_dir,Hi422FR3_SONY_A.264,output_dir,Hi422FR3_SONY_A.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,422 not supported
178,,cv0.5,,h264,,"""CIF - 4:2:2,Frame,CAVLC,IPPPP,Loop Filter,Number Slice Groups=1,First only""",stream_dir,Hi422FR4_SONY_A.264,output_dir,Hi422FR4_SONY_A.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,422 not supported
179,,cv0.5,,h264,,"""CIF - 4:2:2,Frame,CAVLC,IIIII,Number Slice Groups=1,IDR=1""",stream_dir,Hi422FR6_SONY_A.264,output_dir,Hi422FR6_SONY_A.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,422 not supported
180,,cv0.5,,h264,,"""CIF - 4:2:2,Frame,CAVLC,IPPPP,Number Slice Groups=1,First only""",stream_dir,Hi422FR7_SONY_A.264,output_dir,Hi422FR7_SONY_A.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,422 not supported
181,,cv0.5,,h264,,"""CIF - 4:2:2,Frame,CAVLC,IPBPB,Direct 8x8 Inference,Number Slice Groups=1,First only""",stream_dir,Hi422FR8_SONY_A.264,output_dir,Hi422FR8_SONY_A.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,422 not supported
182,,cv0.5,,h264,,"""CIF - 4:2:2,Frame,CAVLC,IPPPP,Loop Filter,Number Slice Groups=1,First only""",stream_dir,Hi422FR9_SONY_A.264,output_dir,Hi422FR9_SONY_A.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,422 not supported
183,0,cv0.5,,h264,,"""Video Resolution=1920,Field,CABAC,IPBPB,Loop Filter,implicit,Number Slice Groups=1,First only""",stream_dir,Hi422FREXT16_SONY_A.264,output_dir,Hi422FREXT16_SONY_A.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,field not supported
184,,cv0.5,,h264,,"""Video Resolution=1920,Frame(MBAFF),CABAC,IPBPB,Loop Filter,Number Slice Groups=1,First only""",stream_dir,Hi422FREXT17_SONY_A.264,output_dir,Hi422FREXT17_SONY_A.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,field not supported
185,0,cv0.5,,h264,,"""Video Resolution=720,Field,CABAC,IBBPBBP,Loop Filter,implicit,Number Slice Groups=1,First only""",stream_dir,Hi422FREXT18_SONY_A.264,output_dir,Hi422FREXT18_SONY_A.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,field not supported
186,,cv0.5,,h264,,"""Video Resolution=720,Frame(MBAFF),CABAC,IBBPBBP,Loop Filter,Number Slice Groups=1,First only""",stream_dir,Hi422FREXT19_SONY_A.264,output_dir,Hi422FREXT19_SONY_A.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,field not supported
187,,cv0.5,,h264,,"""High,CIF,Frame Coded,CABAC,Loop Filter,Temporal,Direct 8x8 Inference,Number Slice Groups=1,First frame""",stream_dir,HPCADQ_BRCM_B.264,output_dir,HPCADQ_BRCM_B.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,4758cff70a70df7c89a369cc8219a8cd,4758cff70a70df7c89a369cc8219a8cd,pass,
188,0,cv0.5,,h264,,"""High,CIF,Field Coded,CABAC,Temporal,Direct 8x8 Inference,Number Slice Groups=1,First frame""",stream_dir,HPCAFLNL_BRCM_C.264,output_dir,HPCAFLNL_BRCM_C.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,field not supported
189,0,cv0.5,,h264,,"""High,CIF,Field Coded,CABAC,Loop Filter,Temporal,Direct 8x8 Inference,Number Slice Groups=1,First frame""",stream_dir,HPCAFL_BRCM_C.264,output_dir,HPCAFL_BRCM_C.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,field not supported
190,,cv0.5,,h264,,"""High,CIF,Frame Coded,CABAC,Loop Filter,Temporal,Direct 8x8 Inference,Number Slice Groups=1,First frame""",stream_dir,HPCALQ_BRCM_B.264,output_dir,HPCALQ_BRCM_B.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,4758cff70a70df7c89a369cc8219a8cd,4758cff70a70df7c89a369cc8219a8cd,pass,
191,,cv0.5,,h264,,"""High,CIF,Frame Coded,CABAC,Loop Filter,Temporal,Direct 8x8 Inference,Number Slice Groups=1,First frame""",stream_dir,HPCAMAPALQ_BRCM_B.264,output_dir,HPCAMAPALQ_BRCM_B.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,field not supported
192,,cv0.5,,h264,,"""High,CIF,Frame Coded,CABAC,Loop Filter,Temporal,Direct 8x8 Inference,Number Slice Groups=1,First frame""",stream_dir,HPCAMOLQ_BRCM_B.264,output_dir,HPCAMOLQ_BRCM_B.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,42009cbdf1c9ace63a8a0e637324c71e,42009cbdf1c9ace63a8a0e637324c71e,pass,
193,,cv0.5,,h264,,"""High,CIF,Frame Coded,CABAC,Temporal,Direct 8x8 Inference,Number Slice Groups=1,First frame""",stream_dir,HPCANL_BRCM_C.264,output_dir,HPCANL_BRCM_C.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,4a1bef22f9d4d1d8d9b55f9913cf8e29,4a1bef22f9d4d1d8d9b55f9913cf8e29,pass,
194,,cv0.5,,h264,,"""High,CIF,Frame Coded,CABAC,Loop Filter,Temporal,Direct 8x8 Inference,Number Slice Groups=1,First frame""",stream_dir,HPCAQ2LQ_BRCM_B.264,output_dir,HPCAQ2LQ_BRCM_B.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,2a31d98bbd2659288afcc18b7a6c86f0,2a31d98bbd2659288afcc18b7a6c86f0,pass,
195,,cv0.5,,h264,,"""High,CIF,Frame Coded,CABAC,Loop Filter,Temporal,Direct 8x8 Inference,Number Slice Groups=1,First frame""",stream_dir,HPCA_BRCM_C.264,output_dir,HPCA_BRCM_C.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,b8d8d7dae55f1da794c6f46e8156863f,b8d8d7dae55f1da794c6f46e8156863f,pass,
196,,cv0.5,,h264,,"""High,CIF,Frame Coded,CAVLC,Loop Filter,Temporal,Direct 8x8 Inference,Number Slice Groups=1,First frame""",stream_dir,HPCVFLNL_BRCM_A.264,output_dir,HPCVFLNL_BRCM_A.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,field not supported
197,,cv0.5,,h264,,"""High,CIF,Frame Coded,CAVLC,Loop Filter,Temporal,Direct 8x8 Inference,Number Slice Groups=1,First frame""",stream_dir,HPCVFL_BRCM_A.264,output_dir,HPCVFL_BRCM_A.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,field not supported
198,,cv0.5,,h264,,"""High,CIF,Frame Coded,CAVLC,Loop Filter,Temporal,Direct 8x8 Inference,Number Slice Groups=1,First frame""",stream_dir,HPCVMOLQ_BRCM_B.264,output_dir,HPCVMOLQ_BRCM_B.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,0ec83ab793ab04f09869c7f54f4a7b44,0ec83ab793ab04f09869c7f54f4a7b44,pass,
199,,cv0.5,,h264,,"""High,CIF,Frame Coded,CAVLC,Loop Filter,Temporal,Direct 8x8 Inference,Number Slice Groups=1,First frame""",stream_dir,HPCVNL_BRCM_A.264,output_dir,HPCVNL_BRCM_A.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,4a1bef22f9d4d1d8d9b55f9913cf8e29,4a1bef22f9d4d1d8d9b55f9913cf8e29,pass,
200,,cv0.5,,h264,,"""High,CIF,Frame Coded,CAVLC,Loop Filter,Temporal,Direct 8x8 Inference,Number Slice Groups=1,First frame""",stream_dir,HPCV_BRCM_A.264,output_dir,HPCV_BRCM_A.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,b8d8d7dae55f1da794c6f46e8156863f,b8d8d7dae55f1da794c6f46e8156863f,pass,
201,0,cv0.5,,h264,,"""Video Resolution=720,Field,CAVLC,IBBP,Spatial,Direct 8x8 Inference,Number Slice Groups=1,First only""",stream_dir,HVLCFI0_Sony_B.264,output_dir,HVLCFI0_Sony_B.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,field not supported
202,0,cv0.5,,h264,,"""Video Resolution=720,PAFF+MBAFF,CAVLC,IBBP,Spatial,Direct 8x8 Inference,Number Slice Groups=1,First only""",stream_dir,HVLCMFF0_Sony_B.264,output_dir,HVLCMFF0_Sony_B.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,field not supported
203,0,cv0.5,,h264,,"""Video Resolution=720,PAFF,CAVLC,IBBP,Spatial,Direct 8x8 Inference,Number Slice Groups=1,First only""",stream_dir,HVLCPFF0_Sony_B.264,output_dir,HVLCPFF0_Sony_B.264.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,field not supported
204,,cv0.5,,hevc,,"exercise all AMP modes (2NxnU, 2NxND, nLx2N, nRx2N).",stream_dir,AMP_A_Samsung_7.265,output_dir,AMP_A_Samsung_7.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,0a1b9a45020da6bb57d59c9b024adc43,0a1b9a45020da6bb57d59c9b024adc43,pass,
205,,cv0.5,,hevc,,exercise a constraint for AMP: AMP is only utilized for PUs of which size is lager than minmum CU.,stream_dir,AMP_B_Samsung_7.265,output_dir,AMP_B_Samsung_7.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,dfafb411641239006781aeed29aeb468,dfafb411641239006781aeed29aeb468,pass,
206,,cv0.5,,hevc,,Check all asymetric partition mode enable,stream_dir,AMP_D_Hisilicon.265,output_dir,AMP_D_Hisilicon.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,6569509150805eb1ca8b9a49f540ac21,6569509150805eb1ca8b9a49f540ac21,pass,
207,,cv0.5,,hevc,,Check all asymetric partition mode enable,stream_dir,AMP_E_Hisilicon.265,output_dir,AMP_E_Hisilicon.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,524be782795e7168b5e43b8168a2be66,524be782795e7168b5e43b8168a2be66,pass,
208,,cv0.5,,hevc,,Check all asymetric partition mode enable,stream_dir,AMP_F_Hisilicon_3.265,output_dir,AMP_F_Hisilicon_3.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,04d2d1d0d9f87aa67bdeee4029e491ff,04d2d1d0d9f87aa67bdeee4029e491ff,pass,
209,,cv0.5,,hevc,,,stream_dir,AMVP_A_MTK_4.265,output_dir,AMVP_A_MTK_4.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,0e9e1095eb5c53d1c350a2131dfd5c46,0e9e1095eb5c53d1c350a2131dfd5c46,pass,
210,,cv0.5,,hevc,,"Check whether motion vector prediction candidate generation and signalling perform correctly when neighboring PUs have various partition mode (part_mode), prediction mode (pred_mode_flag), reference index (ref_idx_l0, ref_idx_l1), inter_pred_idc.",stream_dir,AMVP_B_MTK_4.265,output_dir,AMVP_B_MTK_4.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,01c174ecf746c95248c0d9ab4142463c,01c174ecf746c95248c0d9ab4142463c,pass,
211,,cv0.5,,hevc,,exercise AMVP for low delay condition.,stream_dir,AMVP_C_Samsung_7.265,output_dir,AMVP_C_Samsung_7.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,f14b6de432f1dceedaab3e422fc94487,f14b6de432f1dceedaab3e422fc94487,pass,
212,,cv0.5,,hevc,,"tests output order conformance, in particular the bumping process. Four temporal layers are used and IRAP pictures with no_output_of_prior_pics_flag equal to 1 are present in the bitstream.",stream_dir,BUMPING_A_ericsson_1.265,output_dir,BUMPING_A_ericsson_1.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,0f81e8edd7f6b53c5a1478c471a930fe,0f81e8edd7f6b53c5a1478c471a930fe,pass,
213,,cv0.5,,hevc,,To verify whether setting cabac_init_present_flag to 0 disables transmission of cabac_init_flag in slice header referring to the picture parameter set.,stream_dir,CAINIT_A_SHARP_4.265,output_dir,CAINIT_A_SHARP_4.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,83459b67fe365aed07a0b7fe1666b934,83459b67fe365aed07a0b7fe1666b934,pass,
214,,cv0.5,,hevc,,To verify that the CABAC initialization type is correctly determined based on cabac_init_flag as follows:,stream_dir,CAINIT_B_SHARP_4.265,output_dir,CAINIT_B_SHARP_4.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,58ea7da9b9984ee1de343148a0294e95,58ea7da9b9984ee1de343148a0294e95,pass,
215,,cv0.5,,hevc,,To verify that the CABAC initialization type is correctly determined based on cabac_init_flag as follows:,stream_dir,CAINIT_C_SHARP_3.265,output_dir,CAINIT_C_SHARP_3.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,74c8f6d1239558343036a2e683122d61,74c8f6d1239558343036a2e683122d61,pass,
216,,cv0.5,,hevc,,To verify that the CABAC initialization type is correctly determined based on cabac_init_flag as follows:,stream_dir,CAINIT_D_SHARP_3.265,output_dir,CAINIT_D_SHARP_3.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,ae910212f38ef749b20dfb040882196e,ae910212f38ef749b20dfb040882196e,pass,
217,,cv0.5,,hevc,,To verify that the CABAC initialization type is correctly determined based on cabac_init_flag in presence of four tiles as follows:,stream_dir,CAINIT_E_SHARP_3.265,output_dir,CAINIT_E_SHARP_3.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,ce121ee12c260bac5498b9da5bf88fcc,ce121ee12c260bac5498b9da5bf88fcc,pass,
218,,cv0.5,,hevc,,To verify that the CABAC initialization type is correctly determined based on cabac_init_flag in presence of four tiles as follows:,stream_dir,CAINIT_F_SHARP_3.265,output_dir,CAINIT_F_SHARP_3.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,e9a54e677e76e4aed35336f485728378,e9a54e677e76e4aed35336f485728378,pass,
219,,cv0.5,,hevc,,To verify that the CABAC initialization type is correctly determined based on cabac_init_flag in presence of dependent slices as follows:,stream_dir,CAINIT_G_SHARP_3.265,output_dir,CAINIT_G_SHARP_3.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,5d2d684faa744e407f40903d71b732f1,5d2d684faa744e407f40903d71b732f1,pass,
220,,cv0.5,,hevc,,To verify that the CABAC initialization type is correctly determined based on cabac_init_flag in presence of dependent slices as follows:,stream_dir,CAINIT_H_SHARP_3.265,output_dir,CAINIT_H_SHARP_3.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,85c63837df0009e5362eb1ce0e178c05,85c63837df0009e5362eb1ce0e178c05,pass,
221,,cv0.5,,hevc,,exercise the reference sample substitution process for intra sample prediction.,stream_dir,CIP_A_Panasonic_3.265,output_dir,CIP_A_Panasonic_3.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,7b6fb948eb49057ab8b5c173b6730cb5,7b6fb948eb49057ab8b5c173b6730cb5,pass,
222,,cv0.5,,hevc,,Check that decoder can correctly decode slices of coded pictures containing intra PUs with unavailable samples for intra prediction.,stream_dir,cip_B_NEC_3.265,output_dir,cip_B_NEC_3.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,68634da606dbcded97095fd600d6190a,68634da606dbcded97095fd600d6190a,pass,
223,,cv0.5,,hevc,,exercise the reference sample substitution process for intra sample prediction.,stream_dir,CIP_C_Panasonic_2.265,output_dir,CIP_C_Panasonic_2.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,ae6a3e110badf0de0a45abc841165b81,ae6a3e110badf0de0a45abc841165b81,pass,
224,,cv0.5,,hevc,,,stream_dir,CONFWIN_A_Sony_1.265,output_dir,CONFWIN_A_Sony_1.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,820cde7d49d18a6fef49fce45ef07e0f,820cde7d49d18a6fef49fce45ef07e0f,pass,
225,,cv0.5,,hevc,,,stream_dir,DBLK_A_MAIN10_VIXS_4.265,output_dir,DBLK_A_MAIN10_VIXS_4.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,c0d1ebacd945ee605b15b36617ab221e,c0d1ebacd945ee605b15b36617ab221e,pass,
226,,cv0.5,,hevc,,various combination of beta offset and tc offset,stream_dir,DBLK_A_SONY_3.265,output_dir,DBLK_A_SONY_3.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,657679d5f1f3cea9515b4e3b05f59004,657679d5f1f3cea9515b4e3b05f59004,pass,
227,,cv0.5,,hevc,,various combination of chroma QP offset,stream_dir,DBLK_B_SONY_3.265,output_dir,DBLK_B_SONY_3.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,04bc12af6f6885d580959d5174498be7,04bc12af6f6885d580959d5174498be7,pass,
228,,cv0.5,,hevc,,random deblock On/Off,stream_dir,DBLK_C_SONY_3.265,output_dir,DBLK_C_SONY_3.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,90348ccad6ebd13ca5fe16f5fdd40b0d,90348ccad6ebd13ca5fe16f5fdd40b0d,pass,
229,,cv0.5,,hevc,,loop filter cross slice/tile. slice cover tile,stream_dir,DBLK_D_VIXS_2.265,output_dir,DBLK_D_VIXS_2.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,c25735aeff768360456dd8a82808274f,c25735aeff768360456dd8a82808274f,pass,
230,,cv0.5,,hevc,,loop filter cross slice/tile. slice cover tile,stream_dir,DBLK_E_VIXS_2.265,output_dir,DBLK_E_VIXS_2.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,ed4e436fd135c028b5df7b9b905ba85e,ed4e436fd135c028b5df7b9b905ba85e,pass,
231,,cv0.5,,hevc,,,stream_dir,DBLK_F_VIXS_2.265,output_dir,DBLK_F_VIXS_2.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,076d13f5eb68254f665b98a9526b56ed,076d13f5eb68254f665b98a9526b56ed,pass,
232,,cv0.5,,hevc,,loop filter cross slice/tile. tile cover slice,stream_dir,DBLK_G_VIXS_2.265,output_dir,DBLK_G_VIXS_2.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,ed4e436fd135c028b5df7b9b905ba85e,ed4e436fd135c028b5df7b9b905ba85e,pass,
233,,cv0.5,,hevc,,sum SEI messages,stream_dir,DELTAQP_A_BRCM_4.265,output_dir,DELTAQP_A_BRCM_4.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,cdfb8e2a9b34a9486b60d4edd6e744c9,cdfb8e2a9b34a9486b60d4edd6e744c9,pass,
234,,cv0.5,,hevc,,if decoder can handle various combinations of chroma qp offset,stream_dir,DELTAQP_B_SONY_3.265,output_dir,DELTAQP_B_SONY_3.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,a8e03382dad501b168f0693719563f1d,a8e03382dad501b168f0693719563f1d,pass,
235,,cv0.5,,hevc,,if decoder can handle dQP,stream_dir,DELTAQP_C_SONY_3.265,output_dir,DELTAQP_C_SONY_3.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,29814278049dea754cffd3591144a615,29814278049dea754cffd3591144a615,pass,
236,,cv0.5,,hevc,,test correct decoding of independent and dependent slice segments,stream_dir,DSLICE_A_HHI_5.265,output_dir,DSLICE_A_HHI_5.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,86e4c6d16f48b25d20f5243eb781d5ad,86e4c6d16f48b25d20f5243eb781d5ad,pass,
237,,cv0.5,,hevc,,test correct decoding of dependent slice segments in combination with substreams (WPP),stream_dir,DSLICE_B_HHI_5.265,output_dir,DSLICE_B_HHI_5.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,5a85455c50290eaa07e0c07241ace601,5a85455c50290eaa07e0c07241ace601,pass,
238,,cv0.5,,hevc,,test correct decoding dependent slice segments in combination with tiles,stream_dir,DSLICE_C_HHI_5.265,output_dir,DSLICE_C_HHI_5.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,2e47364f4f2085f0486e84fd0baed76a,2e47364f4f2085f0486e84fd0baed76a,pass,
239,,cv0.5,,hevc,,Test entry point signalling in slice header when tile is used and when emulation prevention bytes occur in substream(s),stream_dir,ENTP_A_Qualcomm_1.265,output_dir,ENTP_A_Qualcomm_1.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,59a49c87c0e08f9338033fe1825997e4,59a49c87c0e08f9338033fe1825997e4,pass,
240,,cv0.5,,hevc,,Test entry point signalling in slice header when tile is used and when emulation prevention bytes occur in substream(s),stream_dir,ENTP_B_Qualcomm_1.265,output_dir,ENTP_B_Qualcomm_1.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,1c94c82d5d43b22ec0f12c2a941ef19f,1c94c82d5d43b22ec0f12c2a941ef19f,pass,
241,,cv0.5,,hevc,,Test entry point signalling in slice header when wavefront is used.,stream_dir,ENTP_C_Qualcomm_1.265,output_dir,ENTP_C_Qualcomm_1.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,d9c1d60ef49045257b8b4493d1531155,d9c1d60ef49045257b8b4493d1531155,pass,
242,,cv0.5,,hevc,,"contains extension fields in SPS, PPS, and slice header. Note that this bitstream is not a conforming bitstream but conforming decoders are required to be able to ignore the extensions.",stream_dir,EXT_A_ericsson_4.265,output_dir,EXT_A_ericsson_4.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,6a8b3e919de362d552e1e1a23c3de76c,6a8b3e919de362d552e1e1a23c3de76c,pass,
243,,cv0.5,,hevc,,,stream_dir,FILLER_A_Sony_1.265,output_dir,FILLER_A_Sony_1.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,820cde7d49d18a6fef49fce45ef07e0f,820cde7d49d18a6fef49fce45ef07e0f,pass,
244,,cv0.5,,hevc,,DU-based CPB removal time signalling.,stream_dir,HRD_A_Fujitsu_3.265,output_dir,HRD_A_Fujitsu_3.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,166962316d8dfc5e3a976508e99f12ee,166962316d8dfc5e3a976508e99f12ee,pass,
245,,cv0.5,,hevc,,,stream_dir,INITQP_A_Sony_1.265,output_dir,INITQP_A_Sony_1.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,573d3f10f629beb83146ac48c718a0a1,573d3f10f629beb83146ac48c718a0a1,pass,
246,,cv0.5,,hevc,,,stream_dir,INITQP_B_Main10_Sony_1.265,output_dir,INITQP_B_Main10_Sony_1.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,fbc110e6fc1fba613d91cd127cbb230a,fbc110e6fc1fba613d91cd127cbb230a,pass,
247,,cv0.5,,hevc,,Check that decoder can correctly decode the slice of coded frames containing pcm_flags.,stream_dir,ipcm_A_NEC_3.265,output_dir,ipcm_A_NEC_3.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,714a2f92a0d0ec9770539cbe56308443,714a2f92a0d0ec9770539cbe56308443,pass,
248,,cv0.5,,hevc,,"Check that decoder can correctly decode the slice of the coded frame containing pcm_flags, and pcm_sample_luma and pcm_sample_chroma data.",stream_dir,ipcm_B_NEC_3.265,output_dir,ipcm_B_NEC_3.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,45523d3764756325ff04fe14dee07021,45523d3764756325ff04fe14dee07021,pass,
249,,cv0.5,,hevc,,"Check that decoder can correctly decode the slice of the coded frame containing pcm_flags, and pcm_sample_luma and pcm_sample_chroma data. Check that decoder can skip loop filtering on samples associated with pcm_flag equal to 1.",stream_dir,ipcm_C_NEC_3.265,output_dir,ipcm_C_NEC_3.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,ac2ed6ff5474ba46eb8d4c38fabd117f,ac2ed6ff5474ba46eb8d4c38fabd117f,pass,
250,,cv0.5,,hevc,,"Check that decoder can correctly decode the slice of the coded frame containing pcm_flags, and pcm_sample_luma and pcm_sample_chroma data. Check that decoder can skip loop filtering on samples associated with both cu_transquant_bypass_flag and pcm_flag equal to 1.",stream_dir,ipcm_D_NEC_3.265,output_dir,ipcm_D_NEC_3.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,0fcc01390327b51ff3b3e9965822d7c1,0fcc01390327b51ff3b3e9965822d7c1,pass,
251,,cv0.5,,hevc,,"Check that decoder can correctly decode the slice of the coded frame containing pcm_flags, and pcm_sample_luma and pcm_sample_chroma data with different pcm_sample_bit_depth_luma_minus1 and pcm_sample_bit_depth_chroma_minus1 values.",stream_dir,ipcm_E_NEC_2.265,output_dir,ipcm_E_NEC_2.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,369279bd383dbe84ef0bff0988f126ad,369279bd383dbe84ef0bff0988f126ad,pass,
252,,cv0.5,,hevc,,"exercise all intra prediction modes (35 modes for each of Y32x32, Y16x16, Y8x8, Y4x4, C16x16, C8x8, and C4x4 for a total of 245 modes).",stream_dir,IPRED_A_docomo_2.265,output_dir,IPRED_A_docomo_2.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,6fcd7b6de13fe0b9b82338dbe1521b35,6fcd7b6de13fe0b9b82338dbe1521b35,pass,
253,,cv0.5,,hevc,,sum SEI message,stream_dir,IPRED_B_Nokia_3.265,output_dir,IPRED_B_Nokia_3.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,ca6784a486111dd9f6ac41637adac46a,ca6784a486111dd9f6ac41637adac46a,pass,
254,,cv0.5,,hevc,,sum SEI messages are included.,stream_dir,IPRED_C_Mitsubishi_3.265,output_dir,IPRED_C_Mitsubishi_3.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,50980f762b1b1dcd057eeabdd34fa577,50980f762b1b1dcd057eeabdd34fa577,pass,
255,,cv0.5,,hevc,,- Check that the decoder handles lossless coding.,stream_dir,LS_A_Orange_2.265,output_dir,LS_A_Orange_2.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,634f06a53d3517f19c2b52d5c162333e,634f06a53d3517f19c2b52d5c162333e,pass,
256,,cv0.5,,hevc,,- Check that the decoder handles lossless coding.,stream_dir,LS_B_Orange_4.265,output_dir,LS_B_Orange_4.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,3a505bfca85202888793e57cb846dd89,3a505bfca85202888793e57cb846dd89,pass,
257,,cv0.5,,hevc,,Check whether the decoder can decode slice headers when the long-term reference pictures from the list of candidates in the SPS are referred to.,stream_dir,LTRPSPS_A_Qualcomm_1.265,output_dir,LTRPSPS_A_Qualcomm_1.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,2034fe42caf96961490ea7621e62ce0e,2034fe42caf96961490ea7621e62ce0e,pass,
258,,cv0.5,,hevc,,reach within 95% of the maximum number of bins per LCU based on the 5120 bits per 16x16 LCU constraint (IPCM is enabled).,stream_dir,MAXBINS_A_TI_5.265,output_dir,MAXBINS_A_TI_5.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,ae3b58911e8a9fd5c58c62debd543d4e,ae3b58911e8a9fd5c58c62debd543d4e,pass,
259,,cv0.5,,hevc,,reach within 95% of the maximum number of bins per LCU based on the 5120 bits per 16x16 LCU constraint (IPCM is enabled).,stream_dir,MAXBINS_B_TI_5.265,output_dir,MAXBINS_B_TI_5.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,214c39f28090e0f012ebf23a57bb3933,214c39f28090e0f012ebf23a57bb3933,pass,
260,,cv0.5,,hevc,,reach within 95% of the maximum number of bins per LCU based on the 5120 bits per 16x16 LCU constraint (IPCM is enabled).,stream_dir,MAXBINS_C_TI_5.265,output_dir,MAXBINS_C_TI_5.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,74a77f408a582ac5ab93d0ee0b96170e,74a77f408a582ac5ab93d0ee0b96170e,pass,
261,,cv0.5,,hevc,,"the maximum number of merging canidates permitted by the standard (i.e. 1, 2, 3, 4, 5)",stream_dir,MERGE_A_TI_3.265,output_dir,MERGE_A_TI_3.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,787de7ff827b4028c5544eadefc4a37f,787de7ff827b4028c5544eadefc4a37f,pass,
262,,cv0.5,,hevc,,"the maximum number of merging canidates permitted by the standard (i.e. 1, 2, 3, 4, 5)",stream_dir,MERGE_B_TI_3.265,output_dir,MERGE_B_TI_3.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,048489e4b9a104821a9ef684d274dd36,048489e4b9a104821a9ef684d274dd36,pass,
263,,cv0.5,,hevc,,"the maximum number of merging canidates permitted by the standard (i.e. 1, 2, 3, 4, 5)",stream_dir,MERGE_C_TI_3.265,output_dir,MERGE_C_TI_3.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,6557cd9471012b92f9d2e8dcdeb602cb,6557cd9471012b92f9d2e8dcdeb602cb,pass,
264,,cv0.5,,hevc,,"the maximum number of merging canidates permitted by the standard (i.e. 1, 2, 3, 4, 5)",stream_dir,MERGE_D_TI_3.265,output_dir,MERGE_D_TI_3.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,fd01adf9ca255051266e49f33d932c99,fd01adf9ca255051266e49f33d932c99,pass,
265,,cv0.5,,hevc,,"the maximum number of merging canidates permitted by the standard (i.e. 1, 2, 3, 4, 5)",stream_dir,MERGE_E_TI_3.265,output_dir,MERGE_E_TI_3.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,aa6ddbcc37ff6d8ece14581fbcb2b1a7,aa6ddbcc37ff6d8ece14581fbcb2b1a7,pass,
266,,cv0.5,,hevc,,,stream_dir,MERGE_F_MTK_4.265,output_dir,MERGE_F_MTK_4.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,88136dc4ffe3dff4cf12a0b55a1adafc,88136dc4ffe3dff4cf12a0b55a1adafc,pass,
267,,cv0.5,,hevc,,,stream_dir,MERGE_G_HHI_4.265,output_dir,MERGE_G_HHI_4.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,3e7d9d983a33d24edc06aedd0bf58a07,3e7d9d983a33d24edc06aedd0bf58a07,pass,
268,,cv0.5,,hevc,,clipping of MVP and Merge candidate motion vectors to 16-bit values. Clipped MVP and merge candidates are selected as MVP and Merge motion vectors.,stream_dir,MVCLIP_A_qualcomm_3.265,output_dir,MVCLIP_A_qualcomm_3.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,0aef728ef36e3015d5dbb9852f9b94d7,0aef728ef36e3015d5dbb9852f9b94d7,pass,
269,,cv0.5,,hevc,,exercise the parsing of list 1 motion vecotor difference for bi-prediction according to mvd_l1_zero_flag in slice header (randamized on/off switching of mvd_l1_zero_flag for multiple B slices).,stream_dir,MVDL1ZERO_A_docomo_4.265,output_dir,MVDL1ZERO_A_docomo_4.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,908d5cbcddf2db93591d2398f7cde2ba,908d5cbcddf2db93591d2398f7cde2ba,pass,
270,,cv0.5,,hevc,,motion vectors pointing to the padded edge regions in a picture.,stream_dir,MVEDGE_A_qualcomm_3.265,output_dir,MVEDGE_A_qualcomm_3.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,b793aa31a4337d955ecb262bb617a1b2,b793aa31a4337d955ecb262bb617a1b2,pass,
271,,cv0.5,,hevc,,Test no rasl output flag and no output pic prior flag when CRA follows and end of sequence.,stream_dir,NoOutPrior_A_Qualcomm_1.265,output_dir,NoOutPrior_A_Qualcomm_1.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,201140c44c3c518b1270e93d5218ef1a,201140c44c3c518b1270e93d5218ef1a,pass,
272,,cv0.5,,hevc,,Test no output pic prior flag at IDR.,stream_dir,NoOutPrior_B_Qualcomm_1.265,output_dir,NoOutPrior_B_Qualcomm_1.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,b14bd3ab84e346be290051439f63a83b,b14bd3ab84e346be290051439f63a83b,pass,
273,,cv0.5,,hevc,,"contains the video coding layer (VLC) NAL unit types TRAIL_N, TRAIL_R, TSA_N, TSA_R, STSA_N, STSA_R, RADL_R, RASL_R, RASL_N, RADL_N, BLA_W_LP, BLA_W_DLP, BLA_N_LP, IDR_W_DLP, IDR_N_LP, and CRA.",stream_dir,NUT_A_ericsson_5.265,output_dir,NUT_A_ericsson_5.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,924db36ca6473a9fabe5de39d8fd73dd,924db36ca6473a9fabe5de39d8fd73dd,pass,
274,,cv0.5,,hevc,,Check whether the decoder can decode slice headers containing pic_output_flag.,stream_dir,OPFLAG_A_Qualcomm_1.265,output_dir,OPFLAG_A_Qualcomm_1.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,e89e95df0c0b9aef65a94720df6f0c5c,e89e95df0c0b9aef65a94720df6f0c5c,pass,
275,,cv0.5,,hevc,,Test picture output flag indication,stream_dir,OPFLAG_B_Qualcomm_1.265,output_dir,OPFLAG_B_Qualcomm_1.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,3732cc402450f2b1e326657632449e8a,3732cc402450f2b1e326657632449e8a,pass,
276,,cv0.5,,hevc,,Test picture output flag indication,stream_dir,OPFLAG_C_Qualcomm_1.265,output_dir,OPFLAG_C_Qualcomm_1.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,7f8b9d1a4ae9401c6a9894b1e0876c80,7f8b9d1a4ae9401c6a9894b1e0876c80,pass,
277,,cv0.5,,hevc,,maximum height for level 5.1,stream_dir,PICSIZE_A_Bossen_1.265,output_dir,PICSIZE_A_Bossen_1.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,
278,,cv0.5,,hevc,,maximum height for level 5.1,stream_dir,PICSIZE_B_Bossen_1.265,output_dir,PICSIZE_B_Bossen_1.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,
279,,cv0.5,,hevc,,maximum height for level 4.1,stream_dir,PICSIZE_C_Bossen_1.265,output_dir,PICSIZE_C_Bossen_1.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,8bd26a35da8ba0b393b367c7e4fc168b,8bd26a35da8ba0b393b367c7e4fc168b,pass,
280,,cv0.5,,hevc,,maximum height for level 4.1,stream_dir,PICSIZE_D_Bossen_1.265,output_dir,PICSIZE_D_Bossen_1.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,12be30720681928698fd41159babb01e,12be30720681928698fd41159babb01e,pass,
281,,cv0.5,,hevc,,"the parallel merge level permitted by the standard (i.e. 2, 3, 4, 5, 6 for CTB size 64x64)",stream_dir,PMERGE_A_TI_3.265,output_dir,PMERGE_A_TI_3.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,aa6ddbcc37ff6d8ece14581fbcb2b1a7,aa6ddbcc37ff6d8ece14581fbcb2b1a7,pass,
282,,cv0.5,,hevc,,"the maximum number of merging canidates permitted by the standard (i.e. 1, 2, 3, 4, 5)",stream_dir,PMERGE_B_TI_3.265,output_dir,PMERGE_B_TI_3.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,2794f3b861afa06f971f298322ee0372,2794f3b861afa06f971f298322ee0372,pass,
283,,cv0.5,,hevc,,"the parallel merge level permitted by the standard (i.e. 2, 3, 4, 5, 6 for CTB size 64x64)",stream_dir,PMERGE_C_TI_3.265,output_dir,PMERGE_C_TI_3.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,e0e4e669adc9f047607c609797805809,e0e4e669adc9f047607c609797805809,pass,
284,,cv0.5,,hevc,,"the parallel merge level permitted by the standard (i.e. 2, 3, 4, 5, 6 for CTB size 64x64)",stream_dir,PMERGE_D_TI_3.265,output_dir,PMERGE_D_TI_3.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,c2c2feac9d40a022f43ebbf1aeb5e3cf,c2c2feac9d40a022f43ebbf1aeb5e3cf,pass,
285,,cv0.5,,hevc,,"the parallel merge level permitted by the standard (i.e. 2, 3, 4, 5, 6 for CTB size 64x64)",stream_dir,PMERGE_E_TI_3.265,output_dir,PMERGE_E_TI_3.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,8f07b8bc7fe3d79a05777e660f1bb415,8f07b8bc7fe3d79a05777e660f1bb415,pass,
286,,cv0.5,,hevc,,some rules related to POC derivation,stream_dir,POC_A_Bossen_3.265,output_dir,POC_A_Bossen_3.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,cb161eec94dce5d212c96e82057d438c,cb161eec94dce5d212c96e82057d438c,pass,
287,,cv0.5,,hevc,,"use of multiple PPS signalling with random PPS parameters (constrained intra, transform skip, tile configurations, WP, loop filter, etc.) that get randomly selected by coded pictures.",stream_dir,PPS_A_qualcomm_7.265,output_dir,PPS_A_qualcomm_7.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,d7370b65bbafe27b97a7ae0354cfe40a,d7370b65bbafe27b97a7ae0354cfe40a,pass,
288,,cv0.5,,hevc,,The bitstream is for testing bitstreams with stuffed extensions.,stream_dir,PS_B_VIDYO_3.265,output_dir,PS_B_VIDYO_3.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,fda5017b9fba93e4209abab429ba682a,fda5017b9fba93e4209abab429ba682a,pass,
289,,cv0.5,,hevc,,"exercise the decoding of a conforming bitstream where the CRA is the first picture in the bitstream and is followed by 7 RASL pictures that are not decodable. There are two subsequent CRA pictures with RASL pictures, following the first CRA picture in this bitstream. These subsequent RASL pictures should be decodable since the associated CRA is not the first CRA picture in the bitstream.",stream_dir,RAP_A_docomo_6.265,output_dir,RAP_A_docomo_6.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,dcf527e5e79f4c535560936c68b1c633,dcf527e5e79f4c535560936c68b1c633,pass,
290,,cv0.5,,hevc,,- Test switching/repeating of VPS/SPS/PPS,stream_dir,RAP_B_Bossen_2.265,output_dir,RAP_B_Bossen_2.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,f13653147826c7d727ddacfaf7203e37,f13653147826c7d727ddacfaf7203e37,pass,
291,,cv0.5,,hevc,,random reference picture list modification with varying list sizes.,stream_dir,RPLM_A_qualcomm_4.265,output_dir,RPLM_A_qualcomm_4.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,e352672a91d6204c60a565c76a664907,e352672a91d6204c60a565c76a664907,pass,
292,,cv0.5,,hevc,,random reference picture list modification with varying list sizes under multiple slice condition.,stream_dir,RPLM_B_qualcomm_4.265,output_dir,RPLM_B_qualcomm_4.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,211bc25a518640d4d620e99efce689a6,211bc25a518640d4d620e99efce689a6,pass,
293,,cv0.5,,hevc,,exercise the inter-RPS prediction method for sending the RPS for short-term pictures in the slice header. The last 3 frames of this 44 frames sequence contain Slice header RPS using the inter-RPS in addition to the RPS sent in the PPS.,stream_dir,RPS_A_docomo_5.265,output_dir,RPS_A_docomo_5.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,fcf52011ed4792dd2968eb40b1c1d761,fcf52011ed4792dd2968eb40b1c1d761,pass,
294,,cv0.5,,hevc,,random RPS signaling in slice header along with random picture coding order within a GOP.,stream_dir,RPS_B_qualcomm_5.265,output_dir,RPS_B_qualcomm_5.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,634034d1178576834b53ad1ebe22f19f,634034d1178576834b53ad1ebe22f19f,pass,
295,,cv0.5,,hevc,,"excercises the three RPS subsets for short-term reference pictures: RefPicSetStCurrBefore, RefPicSetStCurrAfter and RefPicSetStFoll. Reference pictures are moved between these three sets, there is a mix of RPS from the slice header and SPS. NumPocTotalCurr is 8 for one picture and DPB size is 16 for another. The bitstream contains 64 SPS RPSes.",stream_dir,RPS_C_ericsson_5.265,output_dir,RPS_C_ericsson_5.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,4f8d05220d8801e57f624e8bc2bf3ef6,4f8d05220d8801e57f624e8bc2bf3ef6,pass,
296,,cv0.5,,hevc,,exercises long-term and short-term reference pictures. Reference pictures are moved between all five RPS subsets and there is a mix of RPS from the slice header and SPS. MSB cycles on/off is tested.,stream_dir,RPS_D_ericsson_6.265,output_dir,RPS_D_ericsson_6.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,102f4353d839095064aa3f28d8c8c588,102f4353d839095064aa3f28d8c8c588,pass,
297,,cv0.5,,hevc,,random RPS signaling with LTRPs in slice header along with random picture coding order within a GOP.,stream_dir,RPS_E_qualcomm_5.265,output_dir,RPS_E_qualcomm_5.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,f6bc59f964951d988d3a179eb6c9b203,f6bc59f964951d988d3a179eb6c9b203,pass,
298,,cv0.5,,hevc,,exercise the inter-RPS prediction method for sending the RPS for short-term pictures in the slice header. The inter-RPS prediction signals some RPS entries that are not used by the current picture. (used_by_curr_pic_flag[ j ] equal to 0 and use_delta_flag[ j ] equal to 1).,stream_dir,RPS_F_docomo_2.265,output_dir,RPS_F_docomo_2.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,a13790b066834ce6dbc3ce3e100470c5,a13790b066834ce6dbc3ce3e100470c5,pass,
299,,cv0.5,,hevc,,test RQT with intra and inter depth 0:,stream_dir,RQT_A_HHI_4.265,output_dir,RQT_A_HHI_4.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,426c2ff72057b3f00030af333fdef353,426c2ff72057b3f00030af333fdef353,pass,
300,,cv0.5,,hevc,,test RQT with intra and inter depth 1:,stream_dir,RQT_B_HHI_4.265,output_dir,RQT_B_HHI_4.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,4409fae5e4609b1e80ba650a0fa42970,4409fae5e4609b1e80ba650a0fa42970,pass,
301,,cv0.5,,hevc,,test RQT with intra and inter depth 2:,stream_dir,RQT_C_HHI_4.265,output_dir,RQT_C_HHI_4.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,3e7d9d983a33d24edc06aedd0bf58a07,3e7d9d983a33d24edc06aedd0bf58a07,pass,
302,,cv0.5,,hevc,,test RQT with intra and inter depth 3:,stream_dir,RQT_D_HHI_4.265,output_dir,RQT_D_HHI_4.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,e6d90c26425147b5650ba580e1313441,e6d90c26425147b5650ba580e1313441,pass,
303,,cv0.5,,hevc,,test RQT with intra and inter depth 4:,stream_dir,RQT_E_HHI_4.265,output_dir,RQT_E_HHI_4.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,ce82fe960ee241a0f52405bf46b340aa,ce82fe960ee241a0f52405bf46b340aa,pass,
304,,cv0.5,,hevc,,test RQT with intra depth 0 and inter depth 2:,stream_dir,RQT_F_HHI_4.265,output_dir,RQT_F_HHI_4.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,0b52c52244e961426e5cdb58d708fba0,0b52c52244e961426e5cdb58d708fba0,pass,
305,,cv0.5,,hevc,,test RQT with intra depth 2 and inter depth 0:,stream_dir,RQT_G_HHI_4.265,output_dir,RQT_G_HHI_4.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,cc12caa78b5ad9c8511a973389f1de4e,cc12caa78b5ad9c8511a973389f1de4e,pass,
306,,cv0.5,,hevc,,"The unique features of these streams are 1) non-rectangular shape of slices and 2) the minimum size of CTU, which for chroma planes takes the value of 8x8 and exactly coincides with a block that is used for deblocking. These unique features together with SAO enabled impose a lot of challenges in multithreaded decoding for correct implementation of loop filtering on slice borders and especially slice convex and concave corners.",stream_dir,SAODBLK_A_MainConcept_4.265,output_dir,SAODBLK_A_MainConcept_4.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,6f1c100d0132f7a904b4982ee2a9609d,6f1c100d0132f7a904b4982ee2a9609d,pass,
307,,cv0.5,,hevc,,"The unique features of these streams are 1) non-rectangular shape of slices and 2) the minimum size of CTU, which for chroma planes takes the value of 8x8 and exactly coincides with a block that is used for deblocking. These unique features together with SAO enabled impose a lot of challenges in multithreaded decoding for correct implementation of loop filtering on slice borders and especially slice convex and concave corners.",stream_dir,SAODBLK_B_MainConcept_4.265,output_dir,SAODBLK_B_MainConcept_4.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,b4d136f3d9ae6cca593a214d84e39dbb,b4d136f3d9ae6cca593a214d84e39dbb,pass,
308,,cv0.5,,hevc,,exercise random SAO merge left/up flag values,stream_dir,SAO_A_MediaTek_4.265,output_dir,SAO_A_MediaTek_4.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,f5c83f9bb98ee6f15409135805d9f5f5,f5c83f9bb98ee6f15409135805d9f5f5,pass,
309,,cv0.5,,hevc,,exercise tiles / randomly enabling SAO Y and/or SAO UV per slice,stream_dir,SAO_B_MediaTek_5.265,output_dir,SAO_B_MediaTek_5.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,f0ab678b4456dc7aa0651dc19c6062a3,f0ab678b4456dc7aa0651dc19c6062a3,pass,
310,,cv0.5,,hevc,,exercise extreme SAO offsets values,stream_dir,SAO_C_Samsung_5.265,output_dir,SAO_C_Samsung_5.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,d8ddd09f26afb1dcb665b0520d89223a,d8ddd09f26afb1dcb665b0520d89223a,pass,
311,,cv0.5,,hevc,,exercise random SAO offsets values,stream_dir,SAO_D_Samsung_5.265,output_dir,SAO_D_Samsung_5.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,1811e3629ae933a666fad36451440b4d,1811e3629ae933a666fad36451440b4d,pass,
312,,cv0.5,,hevc,,Check that decoder can properly decode slices of coded frames with full SAO information.,stream_dir,SAO_E_Canon_4.265,output_dir,SAO_E_Canon_4.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,f9d3d676506382cbbb658e20e514895e,f9d3d676506382cbbb658e20e514895e,pass,
313,,cv0.5,,hevc,,Check that decoder can properly decode slices of coded frames with full SAO information.,stream_dir,SAO_F_Canon_3.265,output_dir,SAO_F_Canon_3.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,a2d4f5f814c1060194059cd0c0925ea4,a2d4f5f814c1060194059cd0c0925ea4,pass,
314,,cv0.5,,hevc,,Check that decoder can properly decode slices of coded frames with full SAO information.,stream_dir,SAO_G_Canon_3.265,output_dir,SAO_G_Canon_3.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,1e172096299e3cad5600a266e97dd2bc,1e172096299e3cad5600a266e97dd2bc,pass,
315,,cv0.5,,hevc,,"Check that, when slice_loop_filter_across_slices_enabled_flag is set to zero, the decoder can properly apply the SAO filter in CTU corners whether diagonally neighbouring CTUs are available or not.",stream_dir,SAO_H_Parabola_1.265,output_dir,SAO_H_Parabola_1.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,64a2328878d0b5191c18d085f675c720,64a2328878d0b5191c18d085f675c720,pass,
316,,cv0.5,,hevc,,"Test SDH sign inference, and test SDH in conjunction with cu_transquant_bypass_flag.",stream_dir,SDH_A_Orange_4.265,output_dir,SDH_A_Orange_4.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,e0b2c909fda1efd29bc6eca59d2f13bd,e0b2c909fda1efd29bc6eca59d2f13bd,pass,
317,,cv0.5,,hevc,,decoding of pictures comprising slices of different types.,stream_dir,SLICES_A_Rovi_3.265,output_dir,SLICES_A_Rovi_3.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,4fe121af3135a0a95f1cd639f503dccc,4fe121af3135a0a95f1cd639f503dccc,pass,
318,,cv0.5,,hevc,,the switching of scaling list in SPS and PPS,stream_dir,SLIST_A_Sony_5.265,output_dir,SLIST_A_Sony_5.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,0bcc8b527ed1ff643eb9ea6beb53abe1,0bcc8b527ed1ff643eb9ea6beb53abe1,pass,
319,,cv0.5,,hevc,,"various combination of scaling list off, default scaling list and scaling list data in PS",stream_dir,SLIST_B_Sony_9.265,output_dir,SLIST_B_Sony_9.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,a37feb929564c343ee1e24c50fe1ee73,a37feb929564c343ee1e24c50fe1ee73,pass,
320,,cv0.5,,hevc,,the switching of default scaling list and scaling list in PS,stream_dir,SLIST_C_Sony_4.265,output_dir,SLIST_C_Sony_4.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,38fc3705855520a2316ae6529d3b0c8f,38fc3705855520a2316ae6529d3b0c8f,pass,
321,,cv0.5,,hevc,,"""QCIF,Frame,CABAC,IP,Number Slice Groups=1,First frame""",stream_dir,str.265,output_dir,str.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,724410ada8a4d541e25af36e9fcbe23e,724410ada8a4d541e25af36e9fcbe23e,pass,
322,,cv0.5,,hevc,,The bitstream is testing sub_layer_profile_present_flag and sub_layer_level_present_flag. The bitstream contains 3 temporal layers (two sublayers),stream_dir,SLPPLP_A_VIDYO_2.265,output_dir,SLPPLP_A_VIDYO_2.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,296bcf7009d0042cbb2ed69f7c7a7cf2,296bcf7009d0042cbb2ed69f7c7a7cf2,pass,
323,,cv0.5,,hevc,,exercise various CTU and Minimum CU size.,stream_dir,STRUCT_A_Samsung_7.265,output_dir,STRUCT_A_Samsung_7.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,ee6848b88d1b62292d54b2e2fabaca24,ee6848b88d1b62292d54b2e2fabaca24,pass,
324,,cv0.5,,hevc,,exercise various CTU and Minimum CU size.,stream_dir,STRUCT_B_Samsung_7.265,output_dir,STRUCT_B_Samsung_7.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,184f9f2f419ef9899ade775e502a384c,184f9f2f419ef9899ade775e502a384c,pass,
325,,cv0.5,,hevc,,Test random non-uniform tile spacing with maximum number of tiles and disabling/enabling of deblocking filter across tile boundaries.,stream_dir,TILES_A_Cisco_2.265,output_dir,TILES_A_Cisco_2.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,3b11133ffeb8e4102e8f333521ae3255,3b11133ffeb8e4102e8f333521ae3255,pass,
326,,cv0.5,,hevc,,Test random non-uniform tile spacing with maximum number of tiles and randomly disabling/enabling the deblocking filter across tile/slice boundaries.,stream_dir,TILES_B_Cisco_1.265,output_dir,TILES_B_Cisco_1.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,f92d0f70507468157b375744c30b09e7,f92d0f70507468157b375744c30b09e7,pass,
327,,cv0.5,,hevc,,Check if slice_temporal_mvp_enable_flag is parsed and temporal motion vector predictor is generated correctly.,stream_dir,TMVP_A_MS_3.265,output_dir,TMVP_A_MS_3.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,d4e8e27a8f82d82fa4102ea8398732f3,d4e8e27a8f82d82fa4102ea8398732f3,pass,
328,,cv0.5,,hevc,,The bitstream is for testing temporal scalability.,stream_dir,TSCL_A_VIDYO_5.265,output_dir,TSCL_A_VIDYO_5.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,2e8b0258852bb5f89c9593e143265776,2e8b0258852bb5f89c9593e143265776,pass,
329,,cv0.5,,hevc,,The bitstream is for testing temporal scalability.,stream_dir,TSCL_B_VIDYO_4.265,output_dir,TSCL_B_VIDYO_4.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,740cdf5467a55d5f6df5825dafda3435,740cdf5467a55d5f6df5825dafda3435,pass,
330,,cv0.5,,hevc,,Check if transform_skip_enabled_flag is parsed and transform skipping functions correctly.,stream_dir,TSKIP_A_MS_3.265,output_dir,TSKIP_A_MS_3.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,26a3f38d552674700e53c25e3d90496d,26a3f38d552674700e53c25e3d90496d,pass,
331,,cv0.5,,hevc,,"Check Transform Skip intra and inter with unequal bitdepth (luma: 10-bit, chroma: 9-bit)",stream_dir,TSUNEQBD_A_MAIN10_Technicolor_2.265,output_dir,TSUNEQBD_A_MAIN10_Technicolor_2.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,3ce7ab86dbac5c9413025d057b915429,3ce7ab86dbac5c9413025d057b915429,pass,
332,,cv0.5,,hevc,,Check if decoder properly decodes slices with RQT with minimum transfrom size different from 4x4 (default).,stream_dir,TUSIZE_A_Samsung_1.265,output_dir,TUSIZE_A_Samsung_1.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,281ce924c77751677d8c8dbe831e3198,281ce924c77751677d8c8dbe831e3198,pass,
333,,cv0.5,,hevc,,"The bitstream is testing vps_video_parameter_set_id. This bitstream contains two vps's. The correct one has the value ""4"". The bitstream has 3 temporal layers and the correct VPS has the temporal_nesting_flag turned off.",stream_dir,VPSID_A_VIDYO_2.265,output_dir,VPSID_A_VIDYO_2.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,296bcf7009d0042cbb2ed69f7c7a7cf2,296bcf7009d0042cbb2ed69f7c7a7cf2,pass,
334,,cv0.5,,hevc,,"The stream excercises conformance with SPS, VPS, and PPS. The stream contains only 6 I frames, each of them with a different resolution. Each frame has parameters sets with a unique ID:",stream_dir,VPSSPSPPS_A_MainConcept_1.265,output_dir,VPSSPSPPS_A_MainConcept_1.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,86a5e9ba070a029064bd2ae036339dfe,86a5e9ba070a029064bd2ae036339dfe,pass,
335,,cv0.5,,hevc,,The bitstream checks that a decoder can correctly perform entropy coding synchronisation with and without different types of slice. It checks that a decoder can correctly derive QP predictors when entropy_coding_sync_enabled_flag is set to 1. It can also be used to check that a decoder can correctly handle entry points when slice header extensions are used.,stream_dir,WPP_A_ericsson_MAIN10_2.265,output_dir,WPP_A_ericsson_MAIN10_2.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,c13e6efe007325d013d044489ffe273c,c13e6efe007325d013d044489ffe273c,pass,
336,,cv0.5,,hevc,,The bitstream checks that a decoder can correctly perform entropy coding synchronisation with and without different types of slice. It checks that a decoder can correctly derive QP predictors when entropy_coding_sync_enabled_flag is set to 1. It can also be used to check that a decoder can correctly handle entry points when slice header extensions are used.,stream_dir,WPP_A_ericsson_MAIN_2.265,output_dir,WPP_A_ericsson_MAIN_2.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,6a956c6e5ea2e26159ba1034092dbb9e,6a956c6e5ea2e26159ba1034092dbb9e,pass,
337,,cv0.5,,hevc,,The bitstream checks that a decoder can correctly perform entropy coding synchronisation with and without different types of slice. It checks that a decoder can correctly derive QP predictors when entropy_coding_sync_enabled_flag is set to 1. It can also be used to check that a decoder can correctly handle entry points when slice header extensions are used.,stream_dir,WPP_B_ericsson_MAIN10_2.265,output_dir,WPP_B_ericsson_MAIN10_2.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,b525b053abc3e586009fdeb785568765,b525b053abc3e586009fdeb785568765,pass,
338,,cv0.5,,hevc,,The bitstream checks that a decoder can correctly perform entropy coding synchronisation with and without different types of slice. It checks that a decoder can correctly derive QP predictors when entropy_coding_sync_enabled_flag is set to 1. It can also be used to check that a decoder can correctly handle entry points when slice header extensions are used.,stream_dir,WPP_B_ericsson_MAIN_2.265,output_dir,WPP_B_ericsson_MAIN_2.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,00dfb06e49e8bb70a3a714b26a81da57,00dfb06e49e8bb70a3a714b26a81da57,pass,
339,,cv0.5,,hevc,,The bitstream checks that a decoder can correctly perform entropy coding synchronisation with and without different types of slice. It checks that a decoder can correctly derive QP predictors when entropy_coding_sync_enabled_flag is set to 1. It can also be used to check that a decoder can correctly handle entry points when slice header extensions are used.,stream_dir,WPP_C_ericsson_MAIN10_2.265,output_dir,WPP_C_ericsson_MAIN10_2.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,737fb4d7c47234927f4aa76fbc2abf1c,737fb4d7c47234927f4aa76fbc2abf1c,pass,
340,,cv0.5,,hevc,,The bitstream checks that a decoder can correctly perform entropy coding synchronisation with and without different types of slice. It checks that a decoder can correctly derive QP predictors when entropy_coding_sync_enabled_flag is set to 1. It can also be used to check that a decoder can correctly handle entry points when slice header extensions are used.,stream_dir,WPP_C_ericsson_MAIN_2.265,output_dir,WPP_C_ericsson_MAIN_2.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,84af7c0aa496e40a796714a89d373e40,84af7c0aa496e40a796714a89d373e40,pass,
341,,cv0.5,,hevc,,The bitstream checks that a decoder can correctly perform entropy coding synchronisation when a picture is 1 CTU wide. It checks that a decoder can correctly derive QP predictors when entropy_coding_sync_enabled_flag is set to 1. It can also be used to check that a decoder can correctly handle entry points when slice header extensions are used.,stream_dir,WPP_D_ericsson_MAIN10_2.265,output_dir,WPP_D_ericsson_MAIN10_2.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,
342,,cv0.5,,hevc,,The bitstream checks that a decoder can correctly perform entropy coding synchronisation when a picture is 1 CTU wide. It checks that a decoder can correctly derive QP predictors when entropy_coding_sync_enabled_flag is set to 1. It can also be used to check that a decoder can correctly handle entry points when slice header extensions are used.,stream_dir,WPP_D_ericsson_MAIN_2.265,output_dir,WPP_D_ericsson_MAIN_2.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,
343,,cv0.5,,hevc,,The bitstream checks that a decoder can correctly perform entropy coding synchronisation when a picture is 2 CTUs wide. It checks that a decoder can correctly derive QP predictors when entropy_coding_sync_enabled_flag is set to 1. It can also be used to check that a decoder can correctly handle entry points when slice header extensions are used.,stream_dir,WPP_E_ericsson_MAIN10_2.265,output_dir,WPP_E_ericsson_MAIN10_2.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,5ecb9283f16d348f69c9968215b841a1,5ecb9283f16d348f69c9968215b841a1,pass,
344,,cv0.5,,hevc,,The bitstream checks that a decoder can correctly perform entropy coding synchronisation when a picture is 2 CTUs wide. It checks that a decoder can correctly derive QP predictors when entropy_coding_sync_enabled_flag is set to 1. It can also be used to check that a decoder can correctly handle entry points when slice header extensions are used.,stream_dir,WPP_E_ericsson_MAIN_2.265,output_dir,WPP_E_ericsson_MAIN_2.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,92c6eb432672922e98e11917cdb36f0c,92c6eb432672922e98e11917cdb36f0c,pass,
345,,cv0.5,,hevc,,The bitstream checks that a decoder can correctly perform entropy coding synchronisation when a picture is 3 CTUs wide. It checks that a decoder can correctly derive QP predictors when entropy_coding_sync_enabled_flag is set to 1. It can also be used to check that a decoder can correctly handle entry points when slice header extensions are used.,stream_dir,WPP_F_ericsson_MAIN10_2.265,output_dir,WPP_F_ericsson_MAIN10_2.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,1e6ab78a43a8785f8ab630da4655061b,1e6ab78a43a8785f8ab630da4655061b,pass,
346,,cv0.5,,hevc,,The bitstream checks that a decoder can correctly perform entropy coding synchronisation when a picture is 3 CTUs wide. It checks that a decoder can correctly derive QP predictors when entropy_coding_sync_enabled_flag is set to 1. It can also be used to check that a decoder can correctly handle entry points when slice header extensions are used.,stream_dir,WPP_F_ericsson_MAIN_2.265,output_dir,WPP_F_ericsson_MAIN_2.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,3e4bfb8ce4fc64a5f33d76a3f5a9f73c,3e4bfb8ce4fc64a5f33d76a3f5a9f73c,pass,
347,,cv0.5,,hevc,,weighted sample prediction for P slices with plural reference indices,stream_dir,WP_A_MAIN10_Toshiba_3.265,output_dir,WP_A_MAIN10_Toshiba_3.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,c01224863095f0efaa7dc9bfe80a4d9c,c01224863095f0efaa7dc9bfe80a4d9c,pass,
348,,cv0.5,,hevc,,weighted sample prediction for P slices with plural reference indices,stream_dir,WP_A_Toshiba_3.265,output_dir,WP_A_Toshiba_3.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,eb5e7c0642bf4566df014e20315d09e7,eb5e7c0642bf4566df014e20315d09e7,pass,
349,,cv0.5,,hevc,,weighted sample prediction for P and B slices with plural reference indices.,stream_dir,WP_B_Toshiba_3.265,output_dir,WP_B_Toshiba_3.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,e93bc97a7af5e8233c83fb700f3a8052,e93bc97a7af5e8233c83fb700f3a8052,pass,
350,,cv0.5,,hevc,,weighted sample prediction for P and B slices with plural reference indices.,stream_dir,WP_MAIN10_B_Toshiba_3.265,output_dir,WP_MAIN10_B_Toshiba_3.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,0cda3bc702261a4cb315f11f1be1953a,0cda3bc702261a4cb315f11f1be1953a,pass,
351,,cv0.5,,hevc,,sum SEI messages are included.,stream_dir,ADJUST_IPRED_ANGLE_A_RExt_Mitsubishi_2.265,output_dir,ADJUST_IPRED_ANGLE_A_RExt_Mitsubishi_2.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,
352,,cv0.5,,hevc,,Test unequal luma and chroma bitdepth setting. The luma bitdepth is higher than the chroma bitdepth.,stream_dir,Bitdepth_A_RExt_Sony_1.265,output_dir,Bitdepth_A_RExt_Sony_1.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,
353,,cv0.5,,hevc,,Test unequal luma and chroma bitdepth setting. The chroma bitdepth is higher than the luma bitdepth.,stream_dir,Bitdepth_B_RExt_Sony_1.265,output_dir,Bitdepth_B_RExt_Sony_1.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,
354,,cv0.5,,hevc,,Test cross-component prediction,stream_dir,CCP_10bit_RExt_QCOM.265,output_dir,CCP_10bit_RExt_QCOM.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,6bf7d2f8551316329b2d49b18d9435e1,6bf7d2f8551316329b2d49b18d9435e1,pass,
355,,cv0.5,,hevc,,Test cross-component prediction,stream_dir,CCP_12bit_RExt_QCOM.265,output_dir,CCP_12bit_RExt_QCOM.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,12bit not supported
356,,cv0.5,,hevc,,Test cross-component prediction,stream_dir,CCP_8bit_RExt_QCOM.265,output_dir,CCP_8bit_RExt_QCOM.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,20c34addfc28c862d1f1041acca2f8f9,20c34addfc28c862d1f1041acca2f8f9,pass,
357,,cv0.5,,hevc,,Test for explicit RDPCM where on even numbered TU's RDPCM mode is set to RDPCM_OFF while on odd numbered TU's is decided via prediction error minimisation.,stream_dir,ExplicitRdpcm_A_BBC_1.265,output_dir,ExplicitRdpcm_A_BBC_1.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,545563c9efeb591540f9794249bbf71b,545563c9efeb591540f9794249bbf71b,pass,
358,,cv0.5,,hevc,,Test for explicit RDPCM where on even numbered TU's RDPCM mode is set to RDPCM_OFF while on odd numbered TU's is decided via prediction error minimisation.,stream_dir,ExplicitRdpcm_B_BBC_2.265,output_dir,ExplicitRdpcm_B_BBC_2.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,
359,,cv0.5,,hevc,,"exercise the extended precision processing function,",stream_dir,EXTPREC_HIGHTHROUGHPUT_444_16_INTRA_10BIT_RExt_Sony_1.265,output_dir,EXTPREC_HIGHTHROUGHPUT_444_16_INTRA_10BIT_RExt_Sony_1.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,e24c7b4bb8dd6aa4509fb9d48cd5a6f8,e24c7b4bb8dd6aa4509fb9d48cd5a6f8,pass,
360,,cv0.5,,hevc,,"exercise the extended precision processing function,",stream_dir,EXTPREC_HIGHTHROUGHPUT_444_16_INTRA_12BIT_RExt_Sony_1.265,output_dir,EXTPREC_HIGHTHROUGHPUT_444_16_INTRA_12BIT_RExt_Sony_1.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,444 not supported
361,,cv0.5,,hevc,,"exercise the extended precision processing function,",stream_dir,EXTPREC_HIGHTHROUGHPUT_444_16_INTRA_16BIT_RExt_Sony_1.265,output_dir,EXTPREC_HIGHTHROUGHPUT_444_16_INTRA_16BIT_RExt_Sony_1.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,444 not supported
362,,cv0.5,,hevc,,"exercise the extended precision processing function,",stream_dir,EXTPREC_HIGHTHROUGHPUT_444_16_INTRA_8BIT_RExt_Sony_1.265,output_dir,EXTPREC_HIGHTHROUGHPUT_444_16_INTRA_8BIT_RExt_Sony_1.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,984b166445b30b2f6e9845b46596f03e,984b166445b30b2f6e9845b46596f03e,pass,
363,,cv0.5,,hevc,,"exercise the extended precision processing function,",stream_dir,EXTPREC_MAIN_444_16_INTRA_10BIT_RExt_Sony_1.265,output_dir,EXTPREC_MAIN_444_16_INTRA_10BIT_RExt_Sony_1.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,abe3f82307e507089cd4e045950bd996,abe3f82307e507089cd4e045950bd996,pass,
364,,cv0.5,,hevc,,"exercise the extended precision processing function,",stream_dir,EXTPREC_MAIN_444_16_INTRA_12BIT_RExt_Sony_1.265,output_dir,EXTPREC_MAIN_444_16_INTRA_12BIT_RExt_Sony_1.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,444 not supported
365,,cv0.5,,hevc,,"exercise the extended precision processing function,",stream_dir,EXTPREC_MAIN_444_16_INTRA_16BIT_RExt_Sony_1.265,output_dir,EXTPREC_MAIN_444_16_INTRA_16BIT_RExt_Sony_1.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,444 not supported
366,,cv0.5,,hevc,,"exercise the extended precision processing function,",stream_dir,EXTPREC_MAIN_444_16_INTRA_8BIT_RExt_Sony_1.265,output_dir,EXTPREC_MAIN_444_16_INTRA_8BIT_RExt_Sony_1.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,b1d02236dde8945179000f62e3b815ed,b1d02236dde8945179000f62e3b815ed,pass,
367,,cv0.5,,hevc,,exercise some combinations of RExt tools in the specified profile / format,stream_dir,GENERAL_10b_420_RExt_Sony_1.265,output_dir,GENERAL_10b_420_RExt_Sony_1.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,365a0b91b91e3f01a252530d9fdb7274,365a0b91b91e3f01a252530d9fdb7274,pass,
368,,cv0.5,,hevc,,exercise some combinations of RExt tools in the specified profile / format,stream_dir,GENERAL_10b_422_RExt_Sony_1.265,output_dir,GENERAL_10b_422_RExt_Sony_1.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,422 not supported
369,,cv0.5,,hevc,,exercise some combinations of RExt tools in the specified profile / format,stream_dir,GENERAL_10b_444_RExt_Sony_2.265,output_dir,GENERAL_10b_444_RExt_Sony_2.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,a4d4447a5132717f1e5f30d9b84e1fd2,a4d4447a5132717f1e5f30d9b84e1fd2,pass,
370,,cv0.5,,hevc,,exercise some combinations of RExt tools in the specified profile / format,stream_dir,GENERAL_12b_400_RExt_Sony_1.265,output_dir,GENERAL_12b_400_RExt_Sony_1.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,12bit not supported
371,,cv0.5,,hevc,,exercise some combinations of RExt tools in the specified profile / format,stream_dir,GENERAL_12b_420_RExt_Sony_1.265,output_dir,GENERAL_12b_420_RExt_Sony_1.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,12bit not supported
372,,cv0.5,,hevc,,exercise some combinations of RExt tools in the specified profile / format,stream_dir,GENERAL_12b_422_RExt_Sony_1.265,output_dir,GENERAL_12b_422_RExt_Sony_1.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,12bit/422 not supported
373,,cv0.5,,hevc,,exercise some combinations of RExt tools in the specified profile / format,stream_dir,GENERAL_12b_444_RExt_Sony_2.265,output_dir,GENERAL_12b_444_RExt_Sony_2.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,12bit/444 not supported
374,,cv0.5,,hevc,,exercise some combinations of RExt tools in the specified profile / format,stream_dir,GENERAL_16b_400_RExt_Sony_1.265,output_dir,GENERAL_16b_400_RExt_Sony_1.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,16bit not supported
375,,cv0.5,,hevc,,exercise some combinations of RExt tools in the specified profile / format,stream_dir,GENERAL_16b_444_highThroughput_RExt_Sony_2.265,output_dir,GENERAL_16b_444_highThroughput_RExt_Sony_2.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,16bit not supported
376,,cv0.5,,hevc,,exercise some combinations of RExt tools in the specified profile / format,stream_dir,GENERAL_16b_444_RExt_Sony_2.265,output_dir,GENERAL_16b_444_RExt_Sony_2.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,16bit not supported
377,,cv0.5,,hevc,,exercise some combinations of RExt tools in the specified profile / format,stream_dir,GENERAL_8b_400_RExt_Sony_1.265,output_dir,GENERAL_8b_400_RExt_Sony_1.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,528cf0f66ceb66e15dc5949b386dccdb,528cf0f66ceb66e15dc5949b386dccdb,pass,
378,,cv0.5,,hevc,,exercise some combinations of RExt tools in the specified profile / format,stream_dir,GENERAL_8b_420_RExt_Sony_1.265,output_dir,GENERAL_8b_420_RExt_Sony_1.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,2097806835986494b59b24df05f86b81,2097806835986494b59b24df05f86b81,pass,
379,,cv0.5,,hevc,,exercise some combinations of RExt tools in the specified profile / format,stream_dir,GENERAL_8b_444_RExt_Sony_2.265,output_dir,GENERAL_8b_444_RExt_Sony_2.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,c72a835d046444d7390c6715efc22feb,c72a835d046444d7390c6715efc22feb,pass,
380,,cv0.5,,hevc,,Check that decoder can correctly decode the slice of coded frames containing pcm_flags.,stream_dir,IPCM_A_RExt_NEC_2.265,output_dir,IPCM_A_RExt_NEC_2.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,
381,,cv0.5,,hevc,,Check that decoder can correctly decode the slice of coded frames containing pcm_flags.,stream_dir,IPCM_B_RExt_NEC.265,output_dir,IPCM_B_RExt_NEC.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,
382,,cv0.5,,hevc,,Test the verious features in Main 4:2:2 10 profile.,stream_dir,Main_422_10_A_RExt_Sony_2.265,output_dir,Main_422_10_A_RExt_Sony_2.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,422 not supported
383,,cv0.5,,hevc,,Test the verious features in Main 4:2:2 10 profile.,stream_dir,Main_422_10_B_RExt_Sony_2.265,output_dir,Main_422_10_B_RExt_Sony_2.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,422 not supported
384,,cv0.5,,hevc,,"exercise the incrementing and decrementing of all 4 of the persistent Golomb Rice parameters, and the enabling/disabling of the tool.",stream_dir,PERSIST_RPARAM_A_RExt_Sony_3.265,output_dir,PERSIST_RPARAM_A_RExt_Sony_3.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,
385,,cv0.5,,hevc,,Test various scaling list data with and without transform skip for all transform block sizes over different QPs,stream_dir,QMATRIX_A_RExt_Sony_1.265,output_dir,QMATRIX_A_RExt_Sony_1.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,69b605ff69cbd12f41063e183f984fe5,69b605ff69cbd12f41063e183f984fe5,pass,
386,,cv0.5,,hevc,,the specified PPS bit shift parameters for scaling up the SAO offset values (range extensions profile),stream_dir,SAO_A_RExt_MediaTek_1.265,output_dir,SAO_A_RExt_MediaTek_1.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,
387,,cv0.5,,hevc,,Test transform skip context,stream_dir,TSCTX_10bit_I_RExt_SHARP_1.265,output_dir,TSCTX_10bit_I_RExt_SHARP_1.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,
388,,cv0.5,,hevc,,Test transform skip context,stream_dir,TSCTX_10bit_RExt_SHARP_1.265,output_dir,TSCTX_10bit_RExt_SHARP_1.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,48cb5b9eb296d9e5d5154cb19875aae3,48cb5b9eb296d9e5d5154cb19875aae3,pass,
389,,cv0.5,,hevc,,Test transform skip context,stream_dir,TSCTX_12bit_I_RExt_SHARP_1.265,output_dir,TSCTX_12bit_I_RExt_SHARP_1.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,12bit not supported
390,,cv0.5,,hevc,,Test transform skip context,stream_dir,TSCTX_12bit_RExt_SHARP_1.265,output_dir,TSCTX_12bit_RExt_SHARP_1.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,12bit not supported
391,,cv0.5,,hevc,,Test transform skip context,stream_dir,TSCTX_8bit_I_RExt_SHARP_1.265,output_dir,TSCTX_8bit_I_RExt_SHARP_1.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,
392,,cv0.5,,hevc,,Test transform skip context,stream_dir,TSCTX_8bit_RExt_SHARP_1.265,output_dir,TSCTX_8bit_RExt_SHARP_1.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,
393,,cv0.5,,hevc,,exercise the combination of simultaneously,stream_dir,WAVETILES_RExt_Sony_2.265,output_dir,WAVETILES_RExt_Sony_2.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,
394,,cv0.5,,hevc,,_HIGHTHROUGHPUT_444_10BIT_RExt_Apple_2.bit,stream_dir,WPP_AND_TILE_10Bit422Test_HIGH_TP_444_10BIT_RExt_Apple_2.265,output_dir,WPP_AND_TILE_10Bit422Test_HIGH_TP_444_10BIT_RExt_Apple_2.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,,,,422 not supported
395,,cv0.5,,hevc,,exercise some combinations of RExt,stream_dir,WPP_AND_TILE_AND_CABAC_BYPASS_ALIGN_0_HIGH_TP_444_14BIT_RExt_Apple_2.265,output_dir,WPP_AND_TILE_AND_CABAC_BYPASS_ALIGN_0_HIGH_TP_444_14BIT_RExt_Apple_2.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,b2b8b546637c23cc4a49b77c7bb392f4,b2b8b546637c23cc4a49b77c7bb392f4,pass,
396,,cv0.5,,hevc,,exercise some combinations of RExt,stream_dir,WPP_AND_TILE_AND_CABAC_BYPASS_ALIGN_1_HIGH_TP_444_14BIT_RExt_Apple_2.265,output_dir,WPP_AND_TILE_AND_CABAC_BYPASS_ALIGN_1_HIGH_TP_444_14BIT_RExt_Apple_2.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,88289a15d75a09ca98bcb8a142cee47f,88289a15d75a09ca98bcb8a142cee47f,pass,
397,,cv0.5,,hevc,,exercise some combinations of RExt,stream_dir,WPP_AND_TILE_AND_CABAC_EXT_PREC_1_HIGH_TP_444_14BIT_RExt_Apple_2.265,output_dir,WPP_AND_TILE_AND_CABAC_EXT_PREC_1_HIGH_TP_444_14BIT_RExt_Apple_2.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,65136a3818feac964c876df5ad1934e2,65136a3818feac964c876df5ad1934e2,pass,
398,,cv0.5,,hevc,,sum SEI messages are included,stream_dir,WPP_AND_TILE_HIGH_TP_444_8BIT_RExt_Apple_2.265,output_dir,WPP_AND_TILE_HIGH_TP_444_8BIT_RExt_Apple_2.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,b2b8b546637c23cc4a49b77c7bb392f4,b2b8b546637c23cc4a49b77c7bb392f4,pass,
399,,cv0.5,,hevc,,exercise some combinations of RExt,stream_dir,WPP_HIGH_TP_444_8BIT_RExt_Apple_2.265,output_dir,WPP_HIGH_TP_444_8BIT_RExt_Apple_2.265.yuv,,,,,,,,,,,,,,,,,,,md5,,,e1d0bccda9f67c911285fd7f058723d0,e1d0bccda9f67c911285fd7f058723d0,pass,
