import os
import sys
import csv
import re

def read_failed_cases(csv_file):
    """从CSV文件中读取非pass和非空的case"""
    failed_cases = []
    try:
        with open(csv_file, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                # 检查case_id和result是否存在且不为空
                if row['case_id'] and row['result']:
                    # 如果result不是pass，则添加到失败列表
                    if row['result'].lower() != 'pass':
                        failed_cases.append({
                            'case_id': row['case_id'],
                            'result': row['result'],
                            'codec': row['codec'],
                            'category': row['category']
                        })
        return failed_cases
    except Exception as e:
        print(f"读取CSV文件时出错: {str(e)}")
        return []

def extract_case_command(sh_file, case_id):
    """从sh文件中提取指定case_id的命令"""
    try:
        with open(sh_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # 查找包含output/{case_id}/的命令行
        pattern = rf'.*output/{case_id}/.*'
        for line in content.split('\n'):
            if re.search(pattern, line):
                return line.strip()
        return None
    except Exception as e:
        print(f"提取命令时出错: {str(e)}")
        return None

def main():
    if len(sys.argv) != 4:
        print("用法: python3 extract_failed_cases.py <csv_file> <sh_file> <output_sh>")
        print("示例: python3 extract_failed_cases.py ./vc9000d_md5_function.csv ./vdec_umd_0414_1124.sh ./failed_cases.sh")
        return
        
    csv_file = sys.argv[1]
    sh_file = sys.argv[2]
    output_sh = sys.argv[3]
    
    # 读取失败的cases
    print("正在读取失败的cases...")
    failed_cases = read_failed_cases(csv_file)
    
    if not failed_cases:
        print("未找到失败的cases")
        return
        
    print(f"找到 {len(failed_cases)} 个失败的cases")
    
    # 创建输出文件
    try:
        with open(output_sh, 'w', encoding='utf-8') as f:
            f.write("#!/bin/bash\n\n")
            f.write("# 从原始脚本提取的失败cases命令\n\n")
            
            # 提取每个case的命令
            for case in failed_cases:
                case_id = case['case_id']
                print(f"\n处理 case {case_id}...")
                
                command = extract_case_command(sh_file, case_id)
                if command:
                    f.write(f"# case {case_id} - {case['codec']} - {case['category']} - Result: {case['result']}\n")
                    f.write(f"{command}\n\n")
                    print(f"已提取命令")
                else:
                    print(f"警告: 未找到case {case_id}的命令")
            
        print(f"\n处理完成，结果已保存到: {output_sh}")
        
    except Exception as e:
        print(f"创建输出文件时出错: {str(e)}")

if __name__ == "__main__":
    main() 