#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
查找CSV文件中result为空的case，检查输出文件和日志验证情况
"""

import os
import sys
import csv
import argparse
import re
from pathlib import Path


def find_empty_results(csv_file):
    """
    查找CSV文件中result为空的case，并提取case_id和out_file
    
    Args:
        csv_file: CSV文件路径
    
    Returns:
        empty_cases: 包含result为空的case信息的列表，每个元素是一个字典
    """
    empty_cases = []
    
    try:
        with open(csv_file, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row_num, row in enumerate(reader, start=2):  # 从2开始计数，因为第1行是标题
                # 检查result字段是否为空
                if 'result' in row and not row['result']:
                    case_info = {
                        'row': row_num,
                        'case_id': row.get('case_id', 'N/A'),
                        'out_file': row.get('out_file', ''),
                        'coverage': row.get('coverage', 'N/A')
                    }
                    empty_cases.append(case_info)
    except Exception as e:
        print(f"处理文件 {csv_file} 时出错: {e}")
    
    return empty_cases


def check_output_file(output_dir, case_id, out_file):
    """
    检查输出文件是否存在
    """
    if not out_file:
        return False
    output_path = os.path.join(output_dir, str(case_id), out_file)
    return os.path.exists(output_path)


def extract_log_section(log_lines, case_id, next_case_id):
    """
    提取日志文件中output/{case_id}到output/{next_case_id}之间的内容
    """
    start_pattern = re.compile(rf'output/{case_id}\b')
    end_pattern = re.compile(rf'output/{next_case_id}\b') if next_case_id else None
    in_section = False
    section = []
    for line in log_lines:
        if not in_section and start_pattern.search(line):
            in_section = True
            section.append(line)
            continue
        if in_section:
            if end_pattern and end_pattern.search(line):
                break
            section.append(line)
    return section


def analyze_log_section(section):
    """
    分析日志区间内容，返回验证结果
    """
    # 1. 检查是否有[数字.数字]格式的报错行
    error_pattern = re.compile(r'\[\d+\.\d+\]')
    for line in section:
        if error_pattern.search(line):
            return '验证失败（报错）'
    # 2. 检查phase/PHASE successful
    phase_success_pattern = re.compile(r'(phase|PHASE)\s*(\d)\D+successful', re.IGNORECASE)
    phases = set()
    for line in section:
        m = phase_success_pattern.search(line)
        if m:
            phases.add(m.group(2))
    if len(phases) >= 6:
        return '验证成功'
    else:
        return '验证失败（phase未全部成功）'


def main():
    parser = argparse.ArgumentParser(description='查找CSV文件中result为空的case，检查输出文件和日志验证情况')
    parser.add_argument('--csv', required=True, help='CSV文件路径')
    parser.add_argument('--output_dir', required=True, help='输出文件根目录')
    parser.add_argument('--log', required=True, help='日志文件路径')
    args = parser.parse_args()

    # 读取result为空的case
    empty_cases = find_empty_results(args.csv)
    if not empty_cases:
        print('未找到result为空的case')
        return

    # 读取日志文件
    try:
        with open(args.log, 'r', encoding='utf-8') as f:
            log_lines = f.readlines()
    except Exception as e:
        print(f'读取日志文件失败: {e}')
        return

    # 处理每个case
    print('CaseID,输出文件是否存在,日志分析结果,coverage')
    for idx, case in enumerate(empty_cases):
        case_id = case['case_id']
        out_file = case['out_file']
        coverage = case['coverage']
        # 下一个case_id用于日志区间分割
        next_case_id = empty_cases[idx+1]['case_id'] if idx+1 < len(empty_cases) else None
        # 检查输出文件
        has_output = check_output_file(args.output_dir, case_id, out_file)
        if has_output:
            result = '有输出文件'
        else:
            # 日志分析
            section = extract_log_section(log_lines, case_id, next_case_id)
            if not section:
                result = '无日志区间'
            else:
                result = analyze_log_section(section)
        print(f'{case_id},{"是" if has_output else "否"},{result},{coverage}')


if __name__ == "__main__":
    main()
