#!/bin/bash

cmodel_dir=/root/workspace/data/CV0.5/venc_scenario_0515/venc_cmodel_0515_1322/output
umd_dir=/root/workspace/data/CV0.5/venc_scenario_0515/venc_umd_0515_1305/output
csv_dir=/root/workspace/data/CV0.5/venc_scenario_0515.csv

csv_ref_column=md5_ref
csv_md5_column=md5_dut


python3 import_case_md5.py $cmodel_dir $csv_dir $csv_ref_column || exit 1
python3 import_case_md5.py $umd_dir $csv_dir $csv_md5_column || exit 1
python3 update_case_status.py $csv_dir || exit 1 