import os
import sys
import csv
import hashlib

def calculate_md5(file_path):
    """计算文件的MD5值"""
    md5_hash = hashlib.md5()
    try:
        with open(file_path, "rb") as f:
            # 读取文件块
            for byte_block in iter(lambda: f.read(4096), b""):
                md5_hash.update(byte_block)
        return md5_hash.hexdigest()
    except Exception as e:
        print(f"计算MD5时出错: {str(e)}")
        return None

def find_yuv_file(directory, base_name):
    """在目录中查找匹配的YUV文件"""
    if not os.path.exists(directory):
        return None
        
    # 获取基本文件名（不含扩展名）
    base_without_ext = os.path.splitext(base_name)[0]
    
    for file in os.listdir(directory):
        # 检查文件是否以基本文件名开头且以.yuv结尾
        if file.startswith(base_without_ext) and file.endswith('.yuv'):
            return os.path.join(directory, file)
    return None

def check_yuv_file(output_dir, case_id, out_file):
    """检查yuv文件是否存在，支持多种路径格式和文件名变体"""
    # 检查直接路径
    direct_path = os.path.join(output_dir, f"{case_id}", out_file)
    if os.path.exists(direct_path):
        return direct_path
        
    # 检查case_id目录下的变体文件名
    case_dir = os.path.join(output_dir, f"{case_id}")
    variant_file = find_yuv_file(case_dir, out_file)
    if variant_file:
        return variant_file
        
    # 检查不带case_id的路径
    alt_path = os.path.join(output_dir, out_file)
    if os.path.exists(alt_path):
        return alt_path
        
    # 检查stream_dir下的路径
    stream_path = os.path.join(output_dir, "stream_dir", out_file)
    if os.path.exists(stream_path):
        return stream_path
        
    # 检查stream_dir下的变体文件名
    stream_dir = os.path.join(output_dir, "stream_dir")
    stream_variant = find_yuv_file(stream_dir, out_file)
    if stream_variant:
        return stream_variant
        
    return None

def update_csv_md5(csv_file, output_dir):
    """更新CSV文件中no output条目的md5值"""
    if not os.path.exists(csv_file):
        print(f"错误: CSV文件 {csv_file} 不存在")
        return
        
    if not os.path.exists(output_dir):
        print(f"错误: 输出目录 {output_dir} 不存在")
        return
        
    # 读取CSV文件
    rows = []
    updated_count = 0
    no_output_count = 0
    try:
        with open(csv_file, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            fieldnames = reader.fieldnames
            rows = list(reader)
            
        print("开始处理CSV文件...")
        
        # 处理每一行
        for row in rows:
            if row['result'] == 'no output':
                no_output_count += 1
                case_id = row['case_id']
                out_file = row['out_file']
                
                print(f"\n检查 case_id {case_id}:")
                print(f"  输出文件: {out_file}")
                
                # 查找yuv文件
                yuv_path = check_yuv_file(output_dir, case_id, out_file)
                
                if yuv_path:
                    print(f"  找到文件: {yuv_path}")
                    # 计算MD5值
                    md5_value = calculate_md5(yuv_path)
                    if md5_value:
                        row['md5_dut'] = md5_value
                        if row['md5_ref']:
                            row['result'] = 'pass' if row['md5_ref'].lower() == md5_value.lower() else 'fail'
                        else:
                            row['result'] = 'no ref'
                        updated_count += 1
                        print(f"  已更新MD5值: {md5_value}")
                        print(f"  更新结果: {row['result']}")
                    else:
                        print(f"  警告: 无法计算MD5值")
                else:
                    print(f"  未找到文件")
                    # 尝试列出目录内容
                    case_dir = os.path.join(output_dir, f"{case_id}")
                    if os.path.exists(case_dir):
                        print(f"  目录 {case_dir} 内容:")
                        for f in os.listdir(case_dir):
                            print(f"    - {f}")
                
        # 写回CSV文件
        with open(csv_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(rows)
            
        print(f"\n处理完成:")
        print(f"- 发现 {no_output_count} 个标记为 'no output' 的条目")
        print(f"- 成功更新了 {updated_count} 个条目的MD5值")
        print(f"- {no_output_count - updated_count} 个条目未能更新")
        
    except Exception as e:
        print(f"处理CSV文件时出错: {str(e)}")
        import traceback
        traceback.print_exc()

def main():
    if len(sys.argv) != 3:
        print("用法: python3 update_md5_no_output.py <csv_file> <output_dir>")
        print("示例: python3 update_md5_no_output.py ./vc9000d_md5_function.csv /path/to/output")
        return
        
    csv_file = sys.argv[1]
    output_dir = sys.argv[2]
    
    update_csv_md5(csv_file, output_dir)

if __name__ == "__main__":
    main() 