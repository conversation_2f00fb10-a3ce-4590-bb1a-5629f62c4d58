#!/usr/bin/env python3
"""
脚本功能：计算指定文件夹下所有子文件夹中文件的MD5值，并将结果保存到同名的.txt文件中
用法：python3 venc_md5_generator.py [target_directory]
"""

import os
import hashlib
import sys
import argparse
from pathlib import Path

def calculate_md5(file_path):
    """计算文件的MD5值"""
    md5_hash = hashlib.md5()
    with open(file_path, "rb") as f:
        # 读取文件块并更新哈希
        for byte_block in iter(lambda: f.read(4096), b""):
            md5_hash.update(byte_block)
    return md5_hash.hexdigest()

def process_directory(directory):
    """处理目录中的所有文件，计算MD5并保存到.txt文件"""
    base_dir = Path(directory)

    # 确保目录存在
    if not base_dir.exists() or not base_dir.is_dir():
        print(f"错误：目录 {directory} 不存在或不是一个目录")
        return

    # 获取所有子目录
    subdirs = [d for d in base_dir.iterdir() if d.is_dir()]
    total_subdirs = len(subdirs)

    print(f"找到 {total_subdirs} 个子目录")

    # 处理计数器
    processed_dirs = 0
    processed_files = 0

    # 遍历每个子目录
    for subdir in subdirs:
        # 获取子目录中的所有文件
        files = [f for f in subdir.iterdir() if f.is_file()]

        # 处理每个文件
        for file_path in files:
            # 跳过已经生成的.txt文件
            if file_path.suffix.lower() == '.txt':
                continue

            try:
                # 计算MD5值
                md5_value = calculate_md5(file_path)

                # 创建同名的.txt文件
                txt_file_path = file_path.with_suffix('.txt')

                # 将MD5值写入.txt文件
                with open(txt_file_path, 'w') as txt_file:
                    txt_file.write(md5_value)

                processed_files += 1
                print(f"已处理: {file_path} -> {txt_file_path}")
            except Exception as e:
                print(f"处理文件 {file_path} 时出错: {e}")

        processed_dirs += 1
        # 显示进度
        if processed_dirs % 10 == 0 or processed_dirs == total_subdirs:
            print(f"进度: {processed_dirs}/{total_subdirs} 子目录 ({processed_dirs/total_subdirs*100:.1f}%)")

    print(f"\n处理完成! 共处理 {processed_files} 个文件，{processed_dirs} 个子目录")

def main():
    # 创建命令行参数解析器
    parser = argparse.ArgumentParser(
        description='计算指定文件夹下所有子文件夹中文件的MD5值，并将结果保存到同名的.txt文件中',
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )

    # 添加目标目录参数
    parser.add_argument('target_dir',
                        help='要处理的目标目录路径',
                        nargs='?',  # 可选参数
                        default='/root/workspace/data/md5_status_data/venc_0417_1121/venc_umd_04171121/output')

    # 解析命令行参数
    args = parser.parse_args()
    target_dir = args.target_dir

    print(f"开始处理目录: {target_dir}")
    process_directory(target_dir)

if __name__ == "__main__":
    main()
