import os
import csv
import argparse
import sys

def update_status(row, md5ref_col, md5_col, status_col):
    md5ref = row[md5ref_col].strip()
    md5 = row[md5_col].strip()
    
    if md5ref and md5:
        if md5ref == md5:
            row[status_col] = "pass"
        else:
            row[status_col] = "fail"
    elif not md5ref and md5:
        row[status_col] = "no ref"
    elif md5ref and not md5:
        row[status_col] = "no output"
    else:
        row[status_col] = ""

def update_csv(csv_file, case_id):
    try:
        with open(csv_file, 'r', newline='') as f:
            reader = csv.reader(f)
            rows = list(reader)
            
            try:
                header = rows[0]
                case_id_col = header.index('case_id')
                md5ref_col = header.index('MD5Ref')
                md5_col = header.index('MD5')
                status_col = header.index('status')
            except ValueError as e:
                print(f"Error: Required column not found - {str(e)}", file=sys.stderr)
                sys.exit(1)
            
            case_found = False
            for row in rows[1:]:
                if row[case_id_col] == case_id:
                    update_status(row, md5ref_col, md5_col, status_col)
                    case_found = True
                    break
            
            if not case_found:
                print(f"Error: Case ID {case_id} not found in CSV file", file=sys.stderr)
                sys.exit(1)
        
        with open(csv_file, 'w', newline='') as f:
            writer = csv.writer(f)
            writer.writerows(rows)
            
    except FileNotFoundError:
        print(f"Error: CSV file {csv_file} not found", file=sys.stderr)
        sys.exit(1)
    except Exception as e:
        print(f"Error processing CSV: {str(e)}", file=sys.stderr)
        sys.exit(1)

def main():
    parser = argparse.ArgumentParser(
        description='Update status column for a specific case in CSV file based on MD5Ref and MD5 values',
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    parser.add_argument('csv_file', help='Path to target CSV file')
    parser.add_argument('case_id', help='Specific case ID to process')
    
    args = parser.parse_args()
    
    update_csv(args.csv_file, args.case_id)
    print(f"Successfully updated status column for case {args.case_id} in {args.csv_file}")

if __name__ == '__main__':
    main() 