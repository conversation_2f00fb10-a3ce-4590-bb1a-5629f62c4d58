#!/bin/bash

cmodel_dir=/root/workspace/code/venc_function_video_0521/venc_cmodel_video_0521/output
umd_dir=/root/workspace/code/venc_function_video_0521/venc_umd_video_0521/output
csv_dir=/root/workspace/code/venc_function_video_0521/vc9000e_cv0.5_function_video.csv

csv_ref_column=md5_ref
csv_md5_column=md5

python3 venc_md5_generator.py $cmodel_dir || exit 1
python3 venc_md5_generator.py $umd_dir || exit 1
python3 import_case_md5_from_txt.py $cmodel_dir $csv_dir $csv_ref_column || exit 1
python3 import_case_md5_from_txt.py $umd_dir $csv_dir $csv_md5_column || exit 1
python3 update_case_status.py $csv_dir || exit 1