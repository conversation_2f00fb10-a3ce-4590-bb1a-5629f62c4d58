import os
import csv
import argparse
import sys

def get_case_md5(results_dir, case_id):
    case_dir = os.path.join(results_dir, case_id)
    
    if not os.path.isdir(case_dir):
        print(f"Error: Case directory {case_dir} not found", file=sys.stderr)
        sys.exit(1)
    
    yuv_files = [f for f in os.listdir(case_dir) if f.endswith('.yuv')]
    if not yuv_files:
        print(f"Error: No YUV files found in {case_dir}", file=sys.stderr)
        sys.exit(1)
    
    yuv_path = os.path.join(case_dir, yuv_files[0])
    try:
        with open(yuv_path, 'r') as f:
            first_line = f.readline().strip()
            if not first_line:
                print(f"Error: Empty first line in {yuv_path}", file=sys.stderr)
                sys.exit(1)
            
            parts = first_line.split()
            if not parts:
                print(f"Error: No MD5 found in {yuv_path}", file=sys.stderr)
                sys.exit(1)
            
            return parts[0]
    except Exception as e:
        print(f"Error processing {yuv_path}: {str(e)}", file=sys.stderr)
        sys.exit(1)

def update_csv(csv_file, target_column, case_id, md5_value):
    try:
        with open(csv_file, 'r', newline='') as f:
            reader = csv.reader(f)
            rows = list(reader)
            
            try:
                header = rows[0]
                case_id_col = header.index('case_id')
                target_col = header.index(target_column)
            except ValueError as e:
                print(f"Error: Required column not found - {str(e)}", file=sys.stderr)
                sys.exit(1)
            
            case_found = False
            for row in rows[1:]:
                if row[case_id_col] == case_id:
                    row[target_col] = md5_value
                    case_found = True
                    break
            
            if not case_found:
                print(f"Error: Case ID {case_id} not found in CSV file", file=sys.stderr)
                sys.exit(1)
        
        with open(csv_file, 'w', newline='') as f:
            writer = csv.writer(f)
            writer.writerows(rows)
            
    except FileNotFoundError:
        print(f"Error: CSV file {csv_file} not found", file=sys.stderr)
        sys.exit(1)
    except Exception as e:
        print(f"Error processing CSV: {str(e)}", file=sys.stderr)
        sys.exit(1)

def main():
    parser = argparse.ArgumentParser(
        description='Import MD5 value for a specific case from test results to CSV column',
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    parser.add_argument('results_dir', help='Path to test results directory')
    parser.add_argument('csv_file', help='Path to target CSV file')
    parser.add_argument('case_id', help='Specific case ID to process')
    parser.add_argument('column', help='Target column name in CSV')
    
    args = parser.parse_args()
    
    if not os.path.isdir(args.results_dir):
        print(f"Error: Results directory {args.results_dir} not found", file=sys.stderr)
        sys.exit(1)
    
    md5_value = get_case_md5(args.results_dir, args.case_id)
    update_csv(args.csv_file, args.column, args.case_id, md5_value)
    
    print(f"Successfully updated {args.column} column for case {args.case_id} in {args.csv_file}")

if __name__ == '__main__':
    main() 