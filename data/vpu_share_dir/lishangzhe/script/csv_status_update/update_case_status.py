import os
import csv
import argparse
import sys
from collections import Counter

def update_status(row, md5ref_col, md5_col, status_col):
    md5ref = row[md5ref_col].strip()
    md5 = row[md5_col].strip()
    
    if md5ref and md5:
        if md5ref == md5:
            row[status_col] = "pass"
        else:
            row[status_col] = "fail"
    elif not md5ref and md5:
        row[status_col] = "no ref"
    elif md5ref and not md5:
        row[status_col] = "no output"
    else:
        row[status_col] = ""
    
    return row[status_col]

def update_csv(csv_file):
    try:
        with open(csv_file, 'r', newline='') as f:
            reader = csv.reader(f)
            rows = list(reader)
            
            try:
                header = rows[0]
                md5ref_col = header.index('md5_ref')
                md5_col = header.index('md5')
                status_col = header.index('result')
            except ValueError as e:
                print(f"Error: Required column not found - {str(e)}", file=sys.stderr)
                sys.exit(1)
            
            # 使用Counter统计状态
            status_counter = Counter()
            
            for row in rows[1:]:
                status = update_status(row, md5ref_col, md5_col, status_col)
                status_counter[status] += 1
        
        with open(csv_file, 'w', newline='') as f:
            writer = csv.writer(f)
            writer.writerows(rows)
            
        # 定义所有可能的状态
        all_statuses = ["pass", "fail", "no ref", "no output", ""]
        
        # 输出统计信息
        print("\nstatus统计:")
        print("-" * 20)
        total = 0
        for status in all_statuses:
            count = status_counter[status]
            total += count
            status_name = "空" if status == "" else status
            print(f"{status_name}: {count}")
        print("-" * 20)
        print(f"总数: {total}")
            
    except FileNotFoundError:
        print(f"Error: CSV file {csv_file} not found", file=sys.stderr)
        sys.exit(1)
    except Exception as e:
        print(f"Error processing CSV: {str(e)}", file=sys.stderr)
        sys.exit(1)

def main():
    parser = argparse.ArgumentParser(
        description='Update status column in CSV file based on MD5Ref and MD5 values',
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    parser.add_argument('csv_file', help='Path to target CSV file')
    
    args = parser.parse_args()
    
    update_csv(args.csv_file)
    print(f"Successfully updated status column in {args.csv_file}")

if __name__ == '__main__':
    main() 