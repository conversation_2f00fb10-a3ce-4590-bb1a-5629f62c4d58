import os
import csv
import argparse
import sys

def collect_case_md5(results_dir):
    case_md5 = {}
    for case_id in os.listdir(results_dir):
        case_dir = os.path.join(results_dir, case_id)
        
        if not os.path.isdir(case_dir):
            continue
        
        yuv_files = [f for f in os.listdir(case_dir) if f.endswith('.yuv')]
        if not yuv_files:
            print(f"Warning: No YUV files found in {case_dir}", file=sys.stderr)
            continue
        
        yuv_path = os.path.join(case_dir, yuv_files[0])
        try:
            with open(yuv_path, 'r') as f:
                first_line = f.readline().strip()
                if not first_line:
                    print(f"Warning: Empty first line in {yuv_path}", file=sys.stderr)
                    continue
                
                parts = first_line.split()
                if not parts:
                    print(f"Warning: No MD5 found in {yuv_path}", file=sys.stderr)
                    continue
                
                case_md5[case_id] = parts[0]
        except Exception as e:
            print(f"Error processing {yuv_path}: {str(e)}", file=sys.stderr)
    
    return case_md5

def update_csv(csv_file, target_column, case_md5_mapping):
    try:
        with open(csv_file, 'r', newline='') as f:
            reader = csv.reader(f)
            rows = list(reader)
            
            try:
                header = rows[0]
                case_id_col = header.index('case_id')
                target_col = header.index(target_column)
            except ValueError as e:
                print(f"Error: Required column not found - {str(e)}", file=sys.stderr)
                sys.exit(1)
            
            for row in rows[1:]:
                case_id = row[case_id_col]
                if case_id in case_md5_mapping:
                    row[target_col] = case_md5_mapping[case_id]
        
        with open(csv_file, 'w', newline='') as f:
            writer = csv.writer(f)
            writer.writerows(rows)
            
    except FileNotFoundError:
        print(f"Error: CSV file {csv_file} not found", file=sys.stderr)
        sys.exit(1)
    except Exception as e:
        print(f"Error processing CSV: {str(e)}", file=sys.stderr)
        sys.exit(1)

def main():
    parser = argparse.ArgumentParser(
        description='Import MD5 values from test results to CSV column',
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    parser.add_argument('results_dir', help='Path to test results directory')
    parser.add_argument('csv_file', help='Path to target CSV file')
    parser.add_argument('column', help='Target column name in CSV')
    
    args = parser.parse_args()
    
    if not os.path.isdir(args.results_dir):
        print(f"Error: Results directory {args.results_dir} not found", file=sys.stderr)
        sys.exit(1)
    
    case_md5 = collect_case_md5(args.results_dir)
    update_csv(args.csv_file, args.column, case_md5)
    
    print(f"Successfully updated {args.column} column in {args.csv_file}")

if __name__ == '__main__':
    main()            