#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
从测试结果目录中提取特定命名模式文件的MD5值，并更新到CSV文件中
"""

import os
import csv
import argparse
import sys
import re

def collect_specific_md5(results_dir, pattern):
    """
    从测试结果目录中收集特定命名模式文件的MD5值
    
    参数:
        results_dir: 测试结果目录
        pattern: 文件名匹配模式，例如 '_8b_0', '_10b_0' 等
    
    返回:
        字典，键为case_id，值为MD5值
    """
    case_md5 = {}
    pattern_regex = re.compile(pattern)
    
    for case_id in os.listdir(results_dir):
        case_dir = os.path.join(results_dir, case_id)
        
        if not os.path.isdir(case_dir):
            continue
        
        # 查找匹配模式的YUV文件
        matching_files = []
        for f in os.listdir(case_dir):
            if f.endswith('.yuv') and pattern_regex.search(f):
                matching_files.append(f)
        
        if not matching_files:
            # 如果没有找到匹配的文件，尝试查找所有YUV文件
            yuv_files = [f for f in os.listdir(case_dir) if f.endswith('.yuv')]
            if not yuv_files:
                print(f"Warning: No YUV files found in {case_dir}", file=sys.stderr)
                continue
            
            # 检查是否有文件名包含位深度信息
            bit_depth_files = []
            for f in yuv_files:
                if re.search(r'_\d+b_', f):
                    bit_depth_files.append(f)
            
            # 如果有包含位深度信息的文件，使用它们
            if bit_depth_files:
                matching_files = bit_depth_files
            else:
                # 否则使用所有YUV文件
                matching_files = yuv_files
        
        # 处理找到的文件
        for yuv_file in matching_files:
            yuv_path = os.path.join(case_dir, yuv_file)
            try:
                with open(yuv_path, 'r') as f:
                    first_line = f.readline().strip()
                    if not first_line:
                        print(f"Warning: Empty first line in {yuv_path}", file=sys.stderr)
                        continue
                    
                    parts = first_line.split()
                    if not parts:
                        print(f"Warning: No MD5 found in {yuv_path}", file=sys.stderr)
                        continue
                    
                    # 提取MD5值
                    md5 = parts[0]
                    
                    # 如果已经有这个case_id的MD5值，添加文件名信息
                    if case_id in case_md5:
                        case_md5[f"{case_id}_{yuv_file}"] = md5
                    else:
                        case_md5[case_id] = md5
                    
                    print(f"Found MD5 for case {case_id}, file {yuv_file}: {md5}")
            except Exception as e:
                print(f"Error processing {yuv_path}: {str(e)}", file=sys.stderr)
    
    return case_md5

def update_csv(csv_file, target_column, case_md5_mapping):
    """
    更新CSV文件中的MD5值
    
    参数:
        csv_file: CSV文件路径
        target_column: 目标列名
        case_md5_mapping: case_id到MD5值的映射
    """
    try:
        with open(csv_file, 'r', newline='') as f:
            reader = csv.reader(f)
            rows = list(reader)
            
            try:
                header = rows[0]
                case_id_col = header.index('case_id')
                target_col = header.index(target_column)
            except ValueError as e:
                print(f"Error: Required column not found - {str(e)}", file=sys.stderr)
                sys.exit(1)
            
            # 更新CSV行
            updated_count = 0
            for row in rows[1:]:
                case_id = row[case_id_col]
                if case_id in case_md5_mapping:
                    row[target_col] = case_md5_mapping[case_id]
                    updated_count += 1
                else:
                    # 检查是否有带文件名的case_id
                    matching_keys = [k for k in case_md5_mapping.keys() if k.startswith(f"{case_id}_")]
                    if matching_keys:
                        # 使用第一个匹配的MD5值
                        row[target_col] = case_md5_mapping[matching_keys[0]]
                        updated_count += 1
        
        with open(csv_file, 'w', newline='') as f:
            writer = csv.writer(f)
            writer.writerows(rows)
        
        return updated_count
            
    except FileNotFoundError:
        print(f"Error: CSV file {csv_file} not found", file=sys.stderr)
        sys.exit(1)
    except Exception as e:
        print(f"Error processing CSV: {str(e)}", file=sys.stderr)
        sys.exit(1)

def main():
    parser = argparse.ArgumentParser(
        description='Import specific MD5 values from test results to CSV column',
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    parser.add_argument('results_dir', help='Path to test results directory')
    parser.add_argument('csv_file', help='Path to target CSV file')
    parser.add_argument('column', help='Target column name in CSV')
    parser.add_argument('--pattern', default=r'_\d+b_\d+', 
                        help='Regex pattern to match file names, default is "_\\d+b_\\d+" (e.g. _8b_0, _10b_0)')
    
    args = parser.parse_args()
    
    if not os.path.isdir(args.results_dir):
        print(f"Error: Results directory {args.results_dir} not found", file=sys.stderr)
        sys.exit(1)
    
    # 收集特定命名模式文件的MD5值
    case_md5 = collect_specific_md5(args.results_dir, args.pattern)
    
    if not case_md5:
        print(f"Warning: No matching files found with pattern '{args.pattern}'", file=sys.stderr)
        sys.exit(1)
    
    # 更新CSV文件
    updated_count = update_csv(args.csv_file, args.column, case_md5)
    
    print(f"Successfully updated {updated_count} entries in {args.column} column in {args.csv_file}")

if __name__ == '__main__':
    main()
