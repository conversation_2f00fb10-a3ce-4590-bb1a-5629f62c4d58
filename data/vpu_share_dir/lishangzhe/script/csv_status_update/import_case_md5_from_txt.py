#!/usr/bin/env python3
"""
脚本功能：从测试结果目录中的.txt文件导入MD5值到CSV文件的指定列
用法：python3 import_case_md5_from_txt.py <results_dir> <csv_file> <column>
"""

import os
import csv
import argparse
import sys

def collect_case_md5(results_dir):
    """
    从测试结果目录中收集每个case的MD5值
    从每个case目录下的.txt文件中读取MD5值
    """
    case_md5 = {}
    for case_id in os.listdir(results_dir):
        case_dir = os.path.join(results_dir, case_id)
        
        if not os.path.isdir(case_dir):
            continue
        
        # 查找所有.txt文件
        txt_files = [f for f in os.listdir(case_dir) if f.endswith('.txt')]
        if not txt_files:
            print(f"Warning: No TXT files found in {case_dir}", file=sys.stderr)
            continue
        
        txt_path = os.path.join(case_dir, txt_files[0])
        try:
            with open(txt_path, 'r') as f:
                md5_value = f.readline().strip()
                if not md5_value:
                    print(f"Warning: Empty content in {txt_path}", file=sys.stderr)
                    continue
                
                # MD5值通常是32个字符的十六进制字符串
                if len(md5_value) != 32 or not all(c in '0123456789abcdefABCDEF' for c in md5_value):
                    print(f"Warning: Invalid MD5 format in {txt_path}: {md5_value}", file=sys.stderr)
                    continue
                
                case_md5[case_id] = md5_value
        except Exception as e:
            print(f"Error processing {txt_path}: {str(e)}", file=sys.stderr)
    
    return case_md5

def update_csv(csv_file, target_column, case_md5_mapping):
    """
    更新CSV文件中的目标列，使用case_id对应的MD5值
    """
    try:
        with open(csv_file, 'r', newline='') as f:
            reader = csv.reader(f)
            rows = list(reader)
            
            try:
                header = rows[0]
                case_id_col = header.index('case_id')
                target_col = header.index(target_column)
            except ValueError as e:
                print(f"Error: Required column not found - {str(e)}", file=sys.stderr)
                sys.exit(1)
            
            updated_count = 0
            for row in rows[1:]:
                if len(row) <= max(case_id_col, target_col):
                    print(f"Warning: Row has insufficient columns: {row}", file=sys.stderr)
                    continue
                
                case_id = row[case_id_col]
                if case_id in case_md5_mapping:
                    row[target_col] = case_md5_mapping[case_id]
                    updated_count += 1
        
        with open(csv_file, 'w', newline='') as f:
            writer = csv.writer(f)
            writer.writerows(rows)
        
        return updated_count
            
    except FileNotFoundError:
        print(f"Error: CSV file {csv_file} not found", file=sys.stderr)
        sys.exit(1)
    except Exception as e:
        print(f"Error processing CSV: {str(e)}", file=sys.stderr)
        sys.exit(1)

def main():
    parser = argparse.ArgumentParser(
        description='Import MD5 values from .txt files in test results to CSV column',
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    parser.add_argument('results_dir', help='Path to test results directory')
    parser.add_argument('csv_file', help='Path to target CSV file')
    parser.add_argument('column', help='Target column name in CSV')
    
    args = parser.parse_args()
    
    if not os.path.isdir(args.results_dir):
        print(f"Error: Results directory {args.results_dir} not found", file=sys.stderr)
        sys.exit(1)
    
    print(f"Collecting MD5 values from .txt files in {args.results_dir}...")
    case_md5 = collect_case_md5(args.results_dir)
    print(f"Found MD5 values for {len(case_md5)} cases")
    
    print(f"Updating {args.column} column in {args.csv_file}...")
    updated_count = update_csv(args.csv_file, args.column, case_md5)
    
    print(f"Successfully updated {updated_count} rows in {args.column} column of {args.csv_file}")

if __name__ == '__main__':
    main()
