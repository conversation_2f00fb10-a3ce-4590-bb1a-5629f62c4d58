# 视频处理测试脚本工具集

本目录包含一系列用于视频处理测试的脚本工具，主要用于管理测试用例、处理测试结果和分析测试覆盖率。

## 1. 导入测试结果 (Import Case)

### import_case_md5.py
将测试结果中的MD5值导入到CSV文件中。该脚本会遍历指定的输出目录，查找所有测试用例的输出文件，计算其MD5值，并将结果更新到CSV文件的指定列中。这对于批量处理大量测试结果非常有用，可以快速生成参考MD5值或验证当前测试结果。

**功能特点：**
- 自动遍历目录结构查找输出文件
- 支持多种输出文件格式（YUV、RGB等）
- 自动计算文件MD5值并更新到CSV
- 处理过程中显示进度信息

使用方法：
```bash
python3 import_case_md5.py <output_dir> <csv_file> <column>
```
参数说明：
- `output_dir`: 输出目录路径，包含测试结果文件的根目录
- `csv_file`: CSV文件路径，用于记录测试结果
- `column`: 要更新的列名，如"MD5"或"MD5Ref"

### import_single_case_md5.py
将测试结果中的单个case的MD5值导入到CSV文件中。与批量导入脚本不同，此脚本专注于处理单个测试用例，适用于需要单独验证或更新特定测试用例的情况。

**功能特点：**
- 针对单个测试用例进行处理
- 提供详细的处理日志
- 可以单独更新特定测试用例的MD5值
- 支持指定特定的输出文件

使用方法：
```bash
python3 import_single_case_md5.py <output_dir> <csv_file> <case_id> <column>
```
参数说明：
- `output_dir`: 输出目录路径，包含测试结果文件的根目录
- `csv_file`: CSV文件路径，用于记录测试结果
- `case_id`: 测试用例ID，指定要处理的特定测试用例
- `column`: 要更新的列名，如"MD5"或"MD5Ref"

### sample_test
测试导入CSV脚本用例的示例程序，用于演示如何使用import_case_md5.py和import_single_case_md5.py脚本。包含了一些预设的测试数据和预期结果，可以帮助用户理解脚本的工作原理。

使用方法：
```bash
python3 test_import_md5.py
```

## 2. 更新测试状态 (Update Status)

### update_case_status.py
比对CSV文件中的MD5Ref和MD5列，自动更新整个CSV文件的status栏。该脚本通过比较参考MD5值和当前测试生成的MD5值，判断测试是否通过，并更新测试状态。

**功能特点：**
- 自动比对MD5Ref和MD5值
- 支持多种状态标记（Pass、Fail等）
- 可以处理整个CSV文件中的所有测试用例
- 生成测试统计报告（通过率、失败率等）
- 支持忽略特定的测试用例

使用方法：
```bash
python3 update_case_status.py <csv_file>
```
参数说明：
- `csv_file`: CSV文件路径，包含测试用例的MD5和MD5Ref值

### update_single_case_status.py
比对CSV文件中的MD5Ref和MD5，更新指定CSV文件中特定case的status栏。当只需要更新单个测试用例的状态时，使用此脚本可以更加高效。

**功能特点：**
- 只处理指定的测试用例
- 提供详细的比对日志
- 支持自定义状态更新规则
- 可以处理特殊情况（如MD5值缺失）

使用方法：
```bash
python3 update_single_case_status.py <csv_file> <case_id>
```
参数说明：
- `csv_file`: CSV文件路径，包含测试用例的MD5和MD5Ref值
- `case_id`: 测试用例ID，指定要更新状态的特定测试用例

### sample_test
测试更新status脚本用例的示例程序，演示如何使用update_case_status.py和update_single_case_status.py脚本。包含了一些预设的测试数据和预期结果，帮助用户理解状态更新的逻辑。

使用方法：
```bash
python3 test_update_status.py
```

## 3. 获取和修改测试覆盖率 (Coverage)

### get_coverage.py
寻找readme文件，将符合要求的参数更新到指定CSV的coverage栏。该脚本通过分析测试流目录中的readme文件，提取测试用例的功能覆盖信息，并更新到CSV文件中，帮助团队了解测试覆盖的功能点。

**功能特点：**
- 自动扫描目录查找readme文件
- 支持多种readme文件格式和编码
- 使用正则表达式提取关键信息
- 智能匹配测试用例和readme文件
- 支持多种编解码器格式（H.264、MPEG等）

使用方法：
```bash
python3 get_coverage.py <csv_file> <stream_dir>
```
参数说明：
- `csv_file`: CSV文件路径，用于更新coverage信息
- `stream_dir`: 流文件目录路径，包含测试流和readme文件

### get_coverage_hevc.py
专门为HEVC编解码器设计的覆盖率提取工具，从readme文件中提取purpose描述并更新到CSV文件。由于HEVC格式的特殊性，此脚本包含了针对HEVC特有功能点的提取逻辑。

**功能特点：**
- 专门针对HEVC格式优化
- 支持提取HEVC特有的功能点（如Tiles、WPP等）
- 处理特殊的HEVC readme格式
- 支持多种文件命名规则
- 提供详细的提取日志和统计信息

使用方法：
```bash
python3 get_coverage_hevc.py <csv_file> <stream_dir>
```
参数说明：
- `csv_file`: CSV文件路径，用于更新HEVC测试用例的coverage信息
- `stream_dir`: HEVC流文件目录路径，包含HEVC测试流和readme文件

### modify_coverage.py
用户通过YAML文件自定义配置case的feature写入到对应CSV的coverage中。当自动提取的覆盖率信息不准确或需要手动调整时，此脚本允许用户通过配置文件批量修改覆盖率信息。

**功能特点：**
- 支持通过YAML配置文件批量修改
- 可以选择性地修改部分测试用例
- 支持复杂的case_id选择语法（范围、列表等）
- 提供修改前后的对比报告
- 支持多种feature格式和值

使用方法：
```bash
python3 modify_coverage.py <csv_file> <stream_dir> <yaml_file>
```
参数说明：
- `csv_file`: CSV文件路径，用于更新coverage信息
- `stream_dir`: 流文件目录路径，用于验证feature信息
- `yaml_file`: YAML配置文件路径，包含要修改的feature信息

#### 配置文件格式
```yaml
# all/selected，设置对所有case修改还是选择部分case修改
mode: 'selected'
# selected模式下需要配置
# 选择单个：1；连续选择：1-50；非连续选择：1-30,40-50
case_id: '1-3,5-10,100'
# 配置需要调整的feature，case没有的feature会配置失败
features:
  Loop Filter: 'Off'
  Adapt Ref Pic Mark: 'ON'
  Ref Pic List Reorder: 'Yes'
  Redundant Slices: 'No'
  Number Slice Groups: '2'
  Access unit: 'Off'
  Access unit test: 'On'
```

### update_coverage_values.py
基于modify_coverage.py和get_coverage.py创建的新脚本，允许用户通过YAML文件指定要刷入到CSV文件coverage栏的内容。此脚本根据值的类型决定如何处理键和值，提供了更灵活的coverage内容管理。

**功能特点：**
- 支持通过YAML配置文件批量更新coverage值
- 支持三种YAML配置格式：字典格式（键值对）、列表格式（只有特征名称）和分离的key/value格式
- 对于肯定布尔值（yes/on/y 或类似格式，不区分大小写），只添加键到coverage栏，不添加值
- 对于否定布尔值（no/off/n 或类似格式），不添加该特征到coverage栏
- 对于数字值或包含数字的复杂格式（如"30 ( = 2 sec.)"），以"键=值"格式添加到coverage栏
- 对于其他值，只添加值而不添加键
- 当使用列表格式或key部分时，自动从readme文件中查找特征，并使用readme中的值
- 当使用value部分时，在readme中查找匹配的值，并且只有当readme中的值与YAML中指定的值匹配时，才将该特征添加到coverage栏中
- 每次运行脚本时，会清除CSV文件中现有的coverage值，重新生成
- 增强的特征提取能力，可以处理各种格式的readme文件
- 特征匹配完全不区分大小写，提高匹配成功率
- 支持特殊匹配规则，处理包含"/"的特征名称
- 输出到CSV的coverage值顺序与YAML文件中定义的特征顺序一致
- 特殊处理TOSHIBA文件：对于包含"TOSHIBA"的文件，使用param.csv文件而不是readme文件
- 支持选择性处理特定测试用例
- 自动查找并解析readme文件以验证特征信息
- 避免重复添加已存在的值
- 提供详细的处理日志和统计信息

使用方法：
```bash
python3 update_coverage_values.py <csv_file> <stream_dir> <yaml_file>
```
参数说明：
- `csv_file`: CSV文件路径，用于更新coverage信息
- `stream_dir`: 流文件目录路径，用于查找readme文件
- `yaml_file`: YAML配置文件路径，包含要添加的值信息

#### 处理规则
1. 当值为肯定布尔值（"yes"、"on"、"y"或类似格式，如"On (default)"）时（不区分大小写），只添加键到coverage栏，不添加值
2. 当值为否定布尔值（"no"、"off"、"n"或类似格式）时，不添加该特征到coverage栏
3. 当值为数字或包含数字的复杂格式（如"30 ( = 2 sec.)"）时，以"键=值"格式添加到coverage栏
4. 对于其他值，只将值添加到coverage栏，不添加键
5. 每次运行脚本时，会清除CSV文件中现有的coverage值，重新生成
6. 输出到CSV的coverage值顺序与YAML文件中定义的特征顺序一致

#### 特征匹配规则
1. 所有匹配过程均不区分大小写
2. 当YAML中出现包含"/"的特征（如"Frame/Field Coding"）时，会尝试匹配"/"左右两侧的任一部分
3. 当readme文件中出现包含"/"的特征（如"Video Resolution/FPS"）时，只要YAML中的特征与其中一部分匹配，就视为匹配成功
4. 如果上述匹配都失败，还会尝试部分匹配（检查包含关系）

#### 特殊文件处理
1. 对于包含"TOSHIBA"的文件，脚本会查找对应的param.csv文件（如CABA3_TOSHIBA_E_param.csv）
2. 从param.csv文件中提取特征和值，而不是从readme文件中提取
3. 其他处理规则保持不变

#### 配置文件格式

1. 字典格式（键值对）:
```yaml
# 配置模式: 'all' 处理所有case, 'selected' 只处理指定的case
mode: 'selected'

# 当mode为'selected'时，指定要处理的case_id
# 支持单个数字、连续范围和非连续多个格式
case_id: '1-5,10,15-20'

# 要更新的特征和值
features:
  # 布尔值特性 - 只添加键，不添加值
  Loop Filter: 'Off'          # 将只添加 "Loop Filter"
  Adapt Ref Pic Mark: 'ON'    # 将只添加 "Adapt Ref Pic Mark"
  Weighted Prediction: 'y'    # 将只添加 "Weighted Prediction"

  # 数字值特性 - 以"键:值"格式添加
  Number Slice Groups: '2'    # 将添加为 "Number Slice Groups:2"
  QP: '32'                    # 将添加为 "QP:32"

  # 其他非数字、非布尔值特性 - 只添加值
  Profile: 'High'             # 将只添加 "High"
  Resolution: '1920x1080'     # 将只添加 "1920x1080"
```

2. 列表格式（只有特征名称）:
```yaml
# 配置模式: 'all' 处理所有case, 'selected' 只处理指定的case
mode: 'selected'

# 当mode为'selected'时，指定要处理的case_id
# 支持单个数字、连续范围和非连续多个格式
case_id: '1-5,7-8,100'

# 要更新的特征列表
# 只需提供特征名称，脚本会在readme文件中查找这些特征，并使用readme中的值
features:
  - Loop Filter
  - Adapt Ref Pic Mark
  - Ref Pic List Reorder
  - Redundant Slices
  - Number Slice Groups
  - Access unit
  - Access unit test
  - entropy coding
```

3. 分离的key和value格式:
```yaml
# 配置模式: 'all' 处理所有case, 'selected' 只处理指定的case
mode: 'selected'

# 当mode为'selected'时，指定要处理的case_id
# 支持单个数字、连续范围和非连续多个格式
case_id: '1-5,7-8,100'

# key部分 - 脚本会在readme文件中查找这些特征，并使用readme中的值
key:
  - Loop Filter
  - Adapt Ref Pic Mark
  - Ref Pic List Reorder
  - Number Slice Groups
  - Frame/Field Coding

# value部分 - 脚本会在readme中查找匹配的值，并根据值的类型应用相应的处理规则
value:
  # 布尔值 - 如果readme中有匹配的值，添加对应的特征
  - On
  - Yes

  # 数字值 - 如果readme中有匹配的值，添加"特征=值"格式
  - 30
  - 1080

  # 其他值 - 如果readme中有匹配的值，添加值本身
  - CABAC
  - High
  - field
```

#### 示例配置文件
脚本附带了三个示例配置文件：
- `coverage_values_example.yaml`：字典格式（键值对）的示例
- `coverage_values_list_example.yaml`：列表格式（只有特征名称）的示例
- `coverage_values_key_value_example.yaml`：分离的key和value格式的示例

这些示例文件详细说明了不同类型值的处理方式，可以作为创建自定义配置的参考。

## 4. 获取MD5值 (MD5 Value)

### update_md5_no_output.py
寻找output文件夹下的YUV文件，将其转为MD5值并写入CSV。当测试过程中没有明确输出MD5值，但需要根据输出文件生成MD5值时，此脚本非常有用。

**功能特点：**
- 自动查找输出目录中的YUV文件
- 支持多种YUV格式（YUV420、YUV422、YUV444等）
- 自动计算文件MD5值并更新到CSV
- 处理文件名与case_id的映射关系
- 支持处理大文件，优化内存使用
- 提供详细的处理日志和统计信息

使用方法：
```bash
python3 update_md5_no_output.py <csv_file> <output_dir>
```
参数说明：
- `csv_file`: CSV文件路径，用于更新MD5值
- `output_dir`: 输出目录路径，包含YUV输出文件

## 5. 处理输入文件列表 (Input File List)

### process_input_list.py
处理inputFileList.txt文件，自动添加-w -h -o参数。该脚本用于预处理测试输入文件列表，自动为每个输入文件添加必要的参数，简化测试脚本的编写过程。

**功能特点：**
- 自动解析输入文件的分辨率信息
- 为每个输入文件添加宽度(-w)和高度(-h)参数
- 自动生成输出文件路径(-o)参数
- 支持多种输入文件格式（YUV、H.264、H.265等）
- 处理特殊格式的文件名
- 生成可直接使用的命令行参数

使用方法：
```bash
python3 process_input_list.py [可选:inputFileList.txt的路径]
```
如果不提供路径参数，脚本将在当前目录查找inputFileList.txt文件。

## 6. 提取失败用例 (Extract Failed Cases)

### extract_failed_cases.py
从CSV文件中提取失败的测试用例，并从原始脚本中提取对应的命令行。该脚本帮助开发人员快速定位和重现失败的测试用例，便于调试和修复问题。

**功能特点：**
- 自动从CSV文件中识别非pass状态的测试用例
- 从原始测试脚本中提取对应的命令行
- 生成可直接执行的失败用例脚本
- 保留原始命令行的所有参数和选项
- 添加失败原因和相关信息的注释
- 支持按照编解码器类型和类别分类失败用例

使用方法：
```bash
python3 extract_failed_cases.py <csv_file> <sh_file> <output_sh>
```
参数说明：
- `csv_file`: CSV文件路径，包含测试结果信息
- `sh_file`: 原始脚本文件路径，包含所有测试用例的命令行
- `output_sh`: 输出脚本文件路径，将包含所有失败用例的命令行

## 7. 查找空结果 (Find Empty Results)

### find_empty_results.py
查找CSV文件中result为空的case，检查输出文件和日志验证情况。该脚本用于处理测试过程中未能正确记录结果的情况，通过分析日志和输出文件，尝试确定测试的实际状态。

**功能特点：**
- 自动识别CSV文件中result字段为空的测试用例
- 检查对应的输出文件是否存在
- 分析测试日志，提取相关的验证信息
- 根据日志内容判断测试是否成功
- 支持多种验证模式（phase验证、错误检测等）
- 生成详细的分析报告，帮助确定测试状态

使用方法：
```bash
python3 find_empty_results.py --csv <csv_file> --output_dir <output_dir> --log <log_file>
```
参数说明：
- `--csv`: CSV文件路径，包含测试用例信息
- `--output_dir`: 输出文件根目录，包含测试生成的输出文件
- `--log`: 日志文件路径，包含测试执行的详细日志

## 8. 支持的视频格式和测试数据

本测试框架支持多种视频编解码格式和分辨率，为视频处理和编解码测试提供全面的支持。

### 支持的编解码格式

#### H.264 (AVC)
- 支持Baseline、Main和High Profile
- 支持多种级别（Level 1到Level 5.2）
- 支持各种编码工具（CABAC、CAVLC、环路滤波等）
- 支持多参考帧、B帧、IDR帧等特性
- 支持多种切片类型和编码模式
- 支持SEI和VUI信息处理

#### H.265 (HEVC)
- 支持Main、Main 10和Main Still Picture Profile
- 支持多种级别（Level 1到Level 6.2）
- 支持Tiles和WPP并行处理
- 支持SAO（Sample Adaptive Offset）
- 支持多种变换单元大小和预测模式
- 支持高效的运动补偿和熵编码

#### JPEG
- 支持Baseline和Progressive模式
- 支持多种色彩格式（YUV444、YUV422、YUV420等）
- 支持多种量化表和Huffman表
- 支持EXIF元数据处理
- 支持多种采样因子和编码选项

### 支持的分辨率和格式

#### 标准分辨率
- 640x360 (nHD)
- 768x432
- 960x540 (qHD)
- 1280x720 (HD/720p)
- 1920x1080 (FHD/1080p)
- 3840x2160 (UHD/4K)
- 7680x4320 (8K)

#### 特殊分辨率
- 4096x4096
- 8192x8192
- 其他非标准分辨率

#### 色彩格式
- YUV420 (平面和半平面NV12/NV21)
- YUV422 (平面和交错YUYV/UYVY)
- YUV444 (平面)
- RGB (RGB24, RGB32)
- P010 (10位YUV)
- 其他高位深格式

### 测试数据组织和管理

#### 测试数据目录结构
测试数据通常按照以下目录结构组织：
```
/root/workspace/code/vc9000d_input_data/
├── h264/                  # H.264编码的测试视频
│   ├── input_640x360.264  # 不同分辨率的H.264测试流
│   ├── input_768x432.264
│   ├── input_960x540.264
│   ├── input_1280x720.264
│   ├── input_4096x4096.264
│   └── input_8192x8192.264
│
├── h265/                  # H.265编码的测试视频
│   ├── input_640x360.265  # 不同分辨率的H.265测试流
│   ├── input_768x432.265
│   ├── input_960x540.265
│   ├── input_1280x720.265
│   ├── input_4096x4096.265
│   └── input_8192x8192.265
│
└── jpeg/                  # JPEG格式的测试图像
    ├── input_640x360.jpg  # 不同分辨率的JPEG测试图像
    ├── input_768x432.jpg
    ├── input_960x540.jpg
    ├── input_1280x720.jpg
    ├── input_4096x4096.jpg
    └── input_8192x8192.jpg
```

#### 测试流特性
测试流通常包含以下特性的组合：
- 不同的比特率和质量级别
- 不同的GOP结构和参考帧配置
- 各种编码工具和特性的组合
- 特殊场景（高动态范围、复杂纹理等）
- 边界条件和极端情况测试

### 视频处理工具和命令

#### 视频解码工具
对于视频解码任务，推荐使用g2dec解码器：
```bash
# H.264解码示例
/root/workspace/code/vpu_testsuit/input/vc9000d/app/cmodel/g2dec -i input.264 -o output.yuv

# H.265解码示例
/root/workspace/code/vpu_testsuit/input/vc9000d/app/cmodel/g2dec -i input.265 -o output.yuv --format=hevc
```

#### JPEG处理工具
对于JPEG转换任务，推荐使用jpegdec工具，支持多种色彩格式转换：
```bash
# 转换为YUV420格式
jpegdec --pp=0 --pp-ycbcr=0 -i input.jpg -o output.yuv

# 转换为YUV422格式
jpegdec --pp=0 --pp-ycbcr=1 -i input.jpg -o output.yuv

# 转换为YUV444格式
jpegdec --pp=0 --pp-ycbcr=2 -i input.jpg -o output.yuv

# 转换为YUYV格式
jpegdec --pp=0 --pp-ycbcr=3 -i input.jpg -o output.yuv

# 转换为UYVY格式
jpegdec --pp=0 --pp-ycbcr=4 -i input.jpg -o output.yuv
```

#### 视频格式转换
使用ffmpeg进行各种视频格式和分辨率的转换：
```bash
# 生成不同分辨率的H.264文件
ffmpeg -i input.mp4 -c:v libx264 -vf "scale=1280:720" -preset medium -crf 23 output_1280x720.264

# 生成不同分辨率的H.265文件
ffmpeg -i input.mp4 -c:v libx265 -vf "scale=1280:720" -preset medium -crf 23 output_1280x720.265

# 生成不同分辨率的JPEG文件
ffmpeg -i input.mp4 -vf "scale=1280:720" -q:v 2 output_1280x720.jpg
```

### 测试结果分析和验证

#### MD5验证
使用MD5值比对验证解码结果的正确性：
```bash
# 计算文件MD5值
md5sum output.yuv > output.md5

# 比对MD5值
diff reference.md5 output.md5
```

#### 视觉质量评估
除了MD5验证外，还可以使用以下指标评估视频质量：
- PSNR (峰值信噪比)
- SSIM (结构相似性)
- VQM (视频质量测量)
- 主观质量评估