import os
import csv
import argparse
import sys
import re
import glob

def find_readme_in_folder(folder_path, base_name):
    """在指定文件夹中查找匹配的readme文件"""
    # 查找所有readme文件
    readme_files = []
    
    # 遍历文件夹中的所有文件
    for file in os.listdir(folder_path):
        if 'readme' in file.lower():
            readme_files.append(os.path.join(folder_path, file))
    
    # 如果找到readme文件，返回第一个
    return readme_files[0] if readme_files else None

def find_matching_folder(root_folder, target_name):
    """递归查找匹配的文件夹"""
    # 移除可能的文件扩展名
    target_name = os.path.splitext(target_name)[0]
    
    def search_in_folder(current_folder):
        """在当前文件夹中搜索匹配的文件夹"""
        # 检查当前目录名是否匹配
        if os.path.basename(current_folder) == target_name:
            # 检查是否包含readme文件
            for file in os.listdir(current_folder):
                if 'readme' in file.lower():
                    return current_folder
            # 如果没有readme文件，继续搜索子文件夹
            
        # 检查子目录名是否匹配
        for dir_name in os.listdir(current_folder):
            dir_path = os.path.join(current_folder, dir_name)
            if os.path.isdir(dir_path):
                if dir_name == target_name:
                    # 检查是否包含readme文件
                    for file in os.listdir(dir_path):
                        if 'readme' in file.lower():
                            return dir_path
                    # 如果没有readme文件，继续搜索子文件夹
                # 递归搜索子文件夹
                result = search_in_folder(dir_path)
                if result:
                    return result
                    
        # 检查文件是否匹配
        for file in os.listdir(current_folder):
            if os.path.splitext(file)[0] == target_name:
                return current_folder
                
        return None
    
    # 开始递归搜索
    return search_in_folder(root_folder)

def parse_readme_content(file_path):
    """解析readme文件内容，提取特征信息"""
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
            
        features = []
        debug_info = []
        debug_info.append(f"Parsing readme file: {file_path}")
        
        # 查找Entropy Coding
        entropy_patterns = [
            r"Entropy coding\s*:\s*([^\n]+)",
            r"Entropy coding\s+([^\n]+)",
            r"Entropy coding\s*:\s*([^\n]+)"
        ]
        for pattern in entropy_patterns:
            match = re.search(pattern, content, re.IGNORECASE)
            if match:
                features.append("CAVLC" if "CAVLC" in match.group(1) else "CABAC")
                debug_info.append(f"  Found Entropy Coding: {'CAVLC' if 'CAVLC' in match.group(1) else 'CABAC'}")
                break
        
        # 提取特征名称 - 匹配不同的格式
        
        # 格式1：特征名: 值 (ON/YES)
        feature_pattern1 = r"^([^:]+?)\s*:\s*(On|YES)\b"
        matches = re.finditer(feature_pattern1, content, re.IGNORECASE | re.MULTILINE)
        for match in matches:
            feature_name = match.group(1).strip()
            # 不再对Loop filter做特殊处理
            features.append(feature_name)
            debug_info.append(f"  Found feature (格式1): {feature_name}")
                
        # 格式2：特征名多个空格后跟值 (ON/YES)
        feature_pattern2 = r"^([A-Za-z][^:0-9\n]*?)\s{2,}(ON|YES)\b"
        matches = re.finditer(feature_pattern2, content, re.IGNORECASE | re.MULTILINE)
        for match in matches:
            feature_name = match.group(1).strip()
            feature_value = match.group(2).upper()  # 获取实际值
            debug_info.append(f"  Matched pattern2: '{feature_name}' with value '{feature_value}'")
            
            # 不再对Loop filter做特殊处理
            features.append(feature_name)
            debug_info.append(f"  Found feature (格式2): {feature_name}")
        
        # 对特征进行大小写不敏感的去重，保留第一次出现的形式
        seen = set()
        unique_features = []
        
        for feature in features:
            if feature.lower() not in seen:
                seen.add(feature.lower())
                unique_features.append(feature)
                
        # 打印原始和去重后的特征列表（调试用）
        debug_info.append(f"  Raw features: {', '.join(features)}")
        debug_info.append(f"  Unique features: {', '.join(unique_features)}")
        
        # 将去重后的特征排序
        result = ','.join(sorted(unique_features)) if unique_features else ""
        debug_info.append(f"  Final sorted features: {result}")
        
        # 打印调试信息
        for line in debug_info:
            print(line)
            
        return result
        
    except Exception as e:
        print(f"Error reading {file_path}: {str(e)}", file=sys.stderr)
        return ""

def get_coverage_info(csv_file, stream_root):
    """处理CSV文件并获取coverage信息"""
    try:
        # 处理CSV文件
        with open(csv_file, 'r', newline='') as f:
            reader = csv.reader(f)
            rows = list(reader)
            
            # 获取列索引
            header = rows[0]
            try:
                input_bs_col = header.index('INPUT_BS')
                coverage_col = header.index('coverage')
            except ValueError as e:
                print(f"Error: Required column not found - {str(e)}", file=sys.stderr)
                sys.exit(1)
            
            # 处理每一行
            for row in rows[1:]:
                input_bs = row[input_bs_col].strip()
                # 获取输入文件名（去掉后缀）
                base_name = os.path.splitext(input_bs)[0]
                print(f"\nProcessing CSV row:")
                print(f"  Input BS: {input_bs}")
                print(f"  Base Name: {base_name}")
                
                # 查找匹配的文件夹
                matching_folder = find_matching_folder(stream_root, base_name)
                if matching_folder:
                    print(f"  Found matching folder: {matching_folder}")
                    
                    # 在匹配的文件夹中查找readme文件
                    readme_file = find_readme_in_folder(matching_folder, base_name)
                    if readme_file:
                        print(f"  Found readme file: {readme_file}")
                        features = parse_readme_content(readme_file)
                        if features:
                            row[coverage_col] = f'"{features}"'
                            print(f"  Found features: {features}")
                        else:
                            row[coverage_col] = ""
                            print("  No features found in readme")
                    else:
                        # 如果在当前文件夹没找到，尝试在父文件夹中查找
                        parent_folder = os.path.dirname(matching_folder)
                        if parent_folder != matching_folder:
                            readme_file = find_readme_in_folder(parent_folder, base_name)
                            if readme_file:
                                print(f"  Found readme file in parent folder: {readme_file}")
                                features = parse_readme_content(readme_file)
                                if features:
                                    row[coverage_col] = f'"{features}"'
                                    print(f"  Found features: {features}")
                                else:
                                    row[coverage_col] = ""
                                    print("  No features found in readme")
                            else:
                                row[coverage_col] = ""
                                print("  No readme file found in parent folder")
                        else:
                            row[coverage_col] = ""
                            print("  No readme file found in matching folder")
                else:
                    row[coverage_col] = ""
                    print("  No matching folder found")
        
        # 写回CSV文件
        with open(csv_file, 'w', newline='') as f:
            writer = csv.writer(f)
            writer.writerows(rows)
            
    except Exception as e:
        print(f"Error processing CSV: {str(e)}", file=sys.stderr)
        sys.exit(1)

def main():
    parser = argparse.ArgumentParser(
        description='Process CSV file and update coverage information from readme files using folder and file name matching',
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    parser.add_argument('csv_file', help='Path to CSV file')
    parser.add_argument('stream_root', help='Root directory containing stream folders')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.csv_file):
        print(f"Error: CSV file {args.csv_file} not found", file=sys.stderr)
        sys.exit(1)
    
    if not os.path.exists(args.stream_root):
        print(f"Error: Stream root directory {args.stream_root} not found", file=sys.stderr)
        sys.exit(1)
    
    get_coverage_info(args.csv_file, args.stream_root)
    print(f"Successfully processed {args.csv_file}")

if __name__ == '__main__':
    main() 