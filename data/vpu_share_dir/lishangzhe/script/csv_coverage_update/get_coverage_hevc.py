import os
import re
import csv
import sys
from typing import List, Dict

def extract_purpose(file_path: str) -> str:
    """从txt文件中提取purpose描述"""
    encodings = ['utf-8', 'gbk', 'gb2312', 'gb18030', 'big5', 'latin1', 'iso-8859-1', 'cp1252']
    content = None
    
    # 尝试不同的编码格式
    for encoding in encodings:
        try:
            with open(file_path, 'r', encoding=encoding) as f:
                content = f.read()
                break
        except UnicodeDecodeError:
            continue
        except Exception as e:
            print(f"Error reading {file_path} with encoding {encoding}: {str(e)}")
            continue
    
    if content is None:
        print(f"Warning: Could not read {file_path} with any supported encoding")
        return ""
    
    # 特别处理*_Descriptions.txt文件
    if "_Descriptions.txt" in file_path:
        match = re.search(r'Purpose\s*:\s*\n(.*?)(?=\n\n|\n[A-Z]|\Z)', content, re.IGNORECASE | re.DOTALL | re.MULTILINE)
        if match:
            purpose = match.group(1).strip()
            # 移除多余的空格和换行符
            purpose = ' '.join(purpose.split())
            return purpose
            
    # 查找不同格式的purpose描述
    patterns = [
        # purpose格式
        r'Purpose:\s*(.*?)(?=\n|$)',
        r'purpose:\s*(.*?)(?=\n|$)',
        r'The purpose of the stream is to\s*(.*?)(?=\n|$)',
        r'Purpose\s*:\s*\n\s*(.*?)(?=\n|$)',
        r'Purpose\s*:\s*\n\s*-\s*(.*?)(?=\n|$)',
        r'Purpose\s*:\s*\n\s*(.*?)(?=\n\n|\Z)',
        
        # Bitstream格式
        r'Bitstream features:\s*(.*?)(?=\n|$)',
        r'Bitstream features:\s*\n\s*(.*?)(?=\n\n|\Z)',
        r'^Bitstream exercises\s*(.*?)(?=\n|$)',
        r'Feature:\s*(.*?)(?=\n|$)',
        r'features:\s*(.*?)(?=\n|$)',
        r'features:\s*\n\s*(.*?)(?=\n\n|\Z)',
        r'features:\s*\n\s*(.*?)(?=\n\n|\Z)',

        # 其他特定格式
        r'This stream\s*(.*?)(?=\n|$)',
        r'Test\s*(.*?)(?=\n|$)',
                
        # Check格式
        r'Check\s*(.*?)(?=\n|$)',
        r'^Check\s*(.*?)(?=\n|$)',
        r'^check\s*(.*?)(?=\n|$)',
        r'^\s*Check\s*(.*?)(?=\n|$)'
    ]
    
    for pattern in patterns:
        match = re.search(pattern, content, re.IGNORECASE | re.DOTALL | re.MULTILINE)
        if match:
            purpose = match.group(1).strip()
            # 移除多余的空格和换行符
            purpose = ' '.join(purpose.split())
            return purpose
            
    return ""

def process_txt_file(file_path: str) -> Dict[str, str]:
    """
    处理单个txt文件，返回文件名和提取的目的描述
    """
    try:
        purpose = extract_purpose(file_path)
        if purpose:
            return {
                'file': os.path.basename(file_path),
                'purpose': purpose
            }
    except Exception as e:
        print(f"Error processing file {file_path}: {str(e)}")
    return None

def scan_directory(directory: str) -> List[Dict[str, str]]:
    """
    递归扫描目录下的所有txt文件
    """
    results = []
    for root, _, files in os.walk(directory):
        # 首先查找*_Descriptions.txt文件
        descriptions_found = False
        for file in files:
            if file.lower().endswith('_descriptions.txt'):
                file_path = os.path.join(root, file)
                result = process_txt_file(file_path)
                if result:
                    # 从MERGE_F_MTK_4_Descriptions.txt提取MERGE_F_MTK_4
                    base_name = file[:-16]  # 去掉"_Descriptions.txt"
                    result['file'] = base_name
                    results.append(result)
                    descriptions_found = True
                    break
        
        # 然后查找readme.txt或Readme_XXX.txt文件
        if not descriptions_found:
            readme_found = False
            for file in files:
                if file.lower() == 'readme.txt' or file.lower().startswith('readme_'):
                    file_path = os.path.join(root, file)
                    result = process_txt_file(file_path)
                    if result:
                        # 处理文件名
                        if file.lower().startswith('readme_'):
                            # 从Readme_XXX.txt提取XXX作为文件名
                            base_name = file[7:-4]  # 去掉"Readme_"和".txt"
                            result['file'] = base_name
                        else:
                            # 对于其他readme文件，使用目录名
                            result['file'] = os.path.basename(root)
                        
                        results.append(result)
                        readme_found = True
                        break
            
            # 如果没有找到特定格式的txt文件，则查找其他txt文件
            if not readme_found:
                for file in files:
                    if file.lower().endswith('.txt'):
                        # 跳过特殊格式的txt文件
                        if file.lower() == 'readme.txt' or file.lower().startswith('readme_') or file.lower().endswith('_descriptions.txt'):
                            continue
                        file_path = os.path.join(root, file)
                        result = process_txt_file(file_path)
                        if result:
                            results.append(result)
    
    return results

def convert_filename(txt_filename):
    """将.txt文件名转换为.265文件名，处理各种可能的格式"""
    # 移除路径（如果有）
    base_name = os.path.basename(txt_filename)
    
    # 如果是从Readme_XXX.txt提取的XXX，直接使用
    if not base_name.lower().endswith('.txt'):
        base_name = base_name  # 已经是正确的格式
    else:
        # 移除扩展名
        base_name = os.path.splitext(base_name)[0]
        
    # 移除任何额外的扩展名（如.h265, .265等）
    base_name = re.sub(r'\.(h265|265)$', '', base_name)
    # 处理版本号（如_1, _2等）
    base_name = re.sub(r'_\d+$', '', base_name)
    # 处理大小写
    base_name = base_name.upper()
    return f"{base_name}.265"

def update_csv_with_purposes(csv_file: str, purpose_map: Dict[str, str]) -> None:
    """更新CSV文件中的coverage列"""
    try:
        # 读取CSV文件
        with open(csv_file, 'r', newline='', encoding='utf-8') as f:
            reader = csv.reader(f)
            rows = list(reader)
            
        # 检查必要的列是否存在
        header = rows[0]
        if 'codec' not in header or 'coverage' not in header or 'INPUT_BS' not in header:
            print(f"Error: Required columns not found in CSV header: {header}")
            return
            
        # 获取列索引
        codec_idx = header.index('codec')
        coverage_idx = header.index('coverage')
        input_bs_idx = header.index('INPUT_BS')
        
        # 更新coverage列
        updated_rows = 0
        not_found = []
        for i, row in enumerate(rows[1:], 1):
            if len(row) > codec_idx and row[codec_idx].lower() == 'hevc':
                if len(row) <= input_bs_idx:
                    print(f"Warning: Row {i} does not have enough columns")
                    continue
                    
                # 获取文件名（不包含路径和扩展名）
                input_bs = row[input_bs_idx]
                base_name = os.path.splitext(os.path.basename(input_bs))[0]
                
                # 尝试在purpose_map中查找
                found = False
                for key, purpose in purpose_map.items():
                    key_base = os.path.splitext(os.path.basename(key))[0]
                    
                    # 移除版本号进行比较
                    key_clean = re.sub(r'_\d+$', '', key_base)
                    base_clean = re.sub(r'_\d+$', '', base_name)
                    
                    # 处理特殊格式如MERGE_A_TI_3.265
                    # 从MERGE_A_TI_3提取MERGE_A
                    base_prefix = re.match(r'(MERGE_[A-Z]|PMERGE_[A-Z])', base_clean)
                    if base_prefix:
                        base_clean = base_prefix.group(0)
                    
                    # 统一转换为大写进行比较
                    if key_clean.upper() == base_clean.upper() or key_clean.upper() in base_clean.upper():
                        # 确保coverage列存在
                        while len(row) <= coverage_idx:
                            row.append('')
                        row[coverage_idx] = purpose
                        updated_rows += 1
                        found = True
                        break
                        
                if not found:
                    not_found.append(input_bs)
                    
        # 写回CSV文件
        with open(csv_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerows(rows)
            
        print(f"\nUpdated {updated_rows} rows in CSV")
        if not_found:
            print("\nNo purpose found for the following files:")
            for file in not_found:
                print(f"  - {file}")
        
    except Exception as e:
        print(f"Error updating CSV: {str(e)}")

def main():
    # 检查命令行参数
    if len(sys.argv) != 3:
        print("Usage: python3 get_coverage_hevc.py <csv_file> <stream_dir>")
        print("Example: python3 get_coverage_hevc.py ./vc9000d_md5.csv /path/to/h265/streams")
        return

    # 从命令行参数获取CSV文件和目标目录
    csv_file = sys.argv[1]
    target_dir = sys.argv[2]
    
    if not os.path.exists(target_dir):
        print(f"Error: Directory {target_dir} does not exist")
        return
    
    if not os.path.exists(csv_file):
        print(f"Error: CSV file {csv_file} does not exist")
        return
    
    print(f"Scanning directory: {target_dir}")
    results = scan_directory(target_dir)
    
    # 输出结果
    print("\nFound purposes:")
    print("-" * 80)
    for result in results:
        print(f"File: {result['file']}")
        print(f"Purpose: {result['purpose']}")
        print("-" * 80)
    
    # 更新CSV文件
    purpose_map = {convert_filename(p['file']): p['purpose'] for p in results}
    update_csv_with_purposes(csv_file, purpose_map)

if __name__ == "__main__":
    main() 