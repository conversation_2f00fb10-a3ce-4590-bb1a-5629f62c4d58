# 配置模式: 'all' 处理所有case, 'selected' 只处理指定的case
mode: 'all'

# 当mode为'selected'时，指定要处理的case_id
# 支持单个数字、连续范围和非连续多个格式
# 例如: '1,3,5-10,15-20'
case_id: ''

# 要更新的特征列表
# 只需提供特征名称，脚本会在readme文件中查找这些特征，并使用readme中的值
# 处理规则:
# 1. 当值为 "yes"、"no"、"on"、"off"、"y"、"n" 或类似格式（如"On (default)"）时（不区分大小写），只添加键到coverage栏，不添加值
# 2. 当值为数字时，以"键:值"格式添加到coverage栏
# 3. 对于其他值，只将值添加到coverage栏，不添加键
# 4. 每次运行脚本时，会清除CSV文件中现有的coverage值，重新生成
key:
  - Profile
  - Video Resolution/FPS
  - Frame/Field Coding
  - Entropy coding
  - Slice type
  - Picture structure
  - Loop filter
  - Ref Pic List Reorder
  - Adapt Ref Pic Mark
  - Weighted Pred (P)
  - Weighted Bipred (B)
  - Direct Prediction
  - Direct 8x8 Inference
  - SEI
  - VUI
  - Number Slice Groups
  - Long-term Ref Frames 
  - MMCO
  - Arbitrary Slice Order
  - Redundant Slices
  - Num. of ref-frames
  - IDR
  - Constrained intra
  - Non-reference frame use
  - Access unit delimiter 
