# 配置模式: 'all' 处理所有case, 'selected' 只处理指定的case
mode: 'selected'

# 当mode为'selected'时，指定要处理的case_id
# 支持单个数字、连续范围和非连续多个格式
# 例如: '1,3,5-10,15-20'
case_id: '1-5,7-8,100'

# 处理规则:
# 1. 当值为肯定布尔值（"yes"、"on"、"y"或类似格式，如"On (default)"）时（不区分大小写），只添加键到coverage栏，不添加值
# 2. 当值为否定布尔值（"no"、"off"、"n"或类似格式）时，不添加该特征到coverage栏
# 3. 当值为数字或包含数字的复杂格式（如"30 ( = 2 sec.)"）时，以"键=值"格式添加到coverage栏
# 4. 对于其他值，只将值添加到coverage栏，不添加键
# 5. 每次运行脚本时，会清除CSV文件中现有的coverage值，重新生成
# 6. 输出到CSV的coverage值顺序与YAML文件中定义的特征顺序一致
#
# 特征匹配规则:
# 1. 所有匹配过程均不区分大小写
# 2. 当YAML中出现包含"/"的特征（如"Frame/Field Coding"）时，会尝试匹配"/"左右两侧的任一部分
# 3. 当readme文件中出现包含"/"的特征（如"Video Resolution/FPS"）时，只要YAML中的特征与其中一部分匹配，就视为匹配成功
# 4. 如果上述匹配都失败，还会尝试部分匹配（检查包含关系）

# key部分 - 脚本会在readme文件中查找这些特征，并使用readme中的值
key:
  # 肯定布尔值特性 - 只添加键，不添加值
  - Loop Filter
  - Adapt Ref Pic Mark
  - Ref Pic List Reorder

  # 否定布尔值特性 - 不添加到coverage栏
  - Redundant Slices

  # 数字值特性 - 以"键=值"格式添加
  - Number Slice Groups
  - QP

  # 包含"/"的特征 - 特殊匹配规则
  - Frame/Field Coding
  - Video Resolution

# value部分 - 脚本会在readme中查找匹配的值，并根据值的类型应用相应的处理规则
value:
  # 布尔值 - 如果readme中有匹配的值，添加对应的特征
  - On
  - Yes

  # 数字值 - 如果readme中有匹配的值，添加"特征=值"格式
  - 30
  - 1080

  # 其他值 - 如果readme中有匹配的值，添加值本身
  - CABAC
  - High
  - field
