import os
import csv
import argparse
import sys
import yaml
import re
from typing import Dict, Set, List

def find_readme_file(input_bs: str, stream_root: str) -> str:
    """根据input_bs文件名查找对应的readme文件"""
    # 移除可能的文件扩展名
    input_bs_base = os.path.splitext(input_bs)[0]
    
    # 首先尝试直接查找对应的readme文件
    potential_dirs = []
    
    # 方法1: 直接搜索包含input_bs基本名称的目录
    for root, dirs, files in os.walk(stream_root):
        # 检查目录名是否匹配
        if input_bs_base in os.path.basename(root):
            potential_dirs.append(root)
        
        # 检查目录中的文件
        for file in files:
            # 1. 查找名称匹配的readme文件
            if 'readme' in file.lower() and input_bs_base in file:
                return os.path.join(root, file)
            
            # 2. 如果找到匹配的bs文件，查找同目录下的readme
            if file == input_bs:
                readme_files = [f for f in files if 'readme' in f.lower()]
                if readme_files:
                    return os.path.join(root, readme_files[0])
    
    # 从潜在目录中查找任何readme文件
    for dir_path in potential_dirs:
        for root, _, files in os.walk(dir_path):
            readme_files = [f for f in files if 'readme' in f.lower()]
            if readme_files:
                return os.path.join(root, readme_files[0])
    
    # 最后尝试在可能的bs文件所在目录查找
    for root, _, files in os.walk(stream_root):
        if input_bs in files:
            readme_files = [f for f in files if 'readme' in f.lower()]
            if readme_files:
                return os.path.join(root, readme_files[0])
    
    # 尝试查找内容包含input_bs的readme文件
    for root, _, files in os.walk(stream_root):
        for file in files:
            if 'readme' in file.lower():
                readme_path = os.path.join(root, file)
                try:
                    with open(readme_path, 'r', encoding='utf-8', errors='ignore') as f:
                        content = f.read()
                        if input_bs_base in content:
                            return readme_path
                except Exception:
                    continue
    
    return None

def get_readme_features(readme_file: str) -> Dict[str, str]:
    """从readme文件中提取所有feature及其值"""
    features = {}
    try:
        with open(readme_file, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
            
        # 从上下文内容示例看，文件中的格式可能有以下几种:
        # 1. Loop Filter:             On (default)
        # 2. Loop filter                         ON
        
        # 定义模式来匹配这些格式
        patterns = [
            # 格式1：参数名: 值
            r"^([^:]+?)\s*:\s*(\S[^\n]*?)\s*$",
            # 格式2：参数名后跟多个空格然后是值
            r"^(\S[^0-9\n]+?)\s{2,}(\S[^\n]*?)\s*$"
        ]
        
        for pattern in patterns:
            matches = re.finditer(pattern, content, re.IGNORECASE | re.MULTILINE)
            for match in matches:
                feature_name = match.group(1).strip()
                feature_value = match.group(2).strip()
                
                # 记录所有找到的feature和值
                features[feature_name] = feature_value
        
        # 打印readme中找到的所有特征
        print(f"All features found in {readme_file}:")
        for feature, value in features.items():
            print(f"  - '{feature}': '{value}'")
            
    except Exception as e:
        print(f"Error reading readme file {readme_file}: {str(e)}", file=sys.stderr)
    
    return features

def parse_case_ids(case_id_str: str) -> Set[int]:
    """解析case_id字符串，支持单个、连续范围和非连续多个格式"""
    case_ids = set()
    
    # 处理空字符串
    if not case_id_str or not case_id_str.strip():
        return case_ids
        
    # 分割非连续部分 (如 "1-5, 7-8, 100")
    parts = [p.strip() for p in case_id_str.split(',')]
    
    for part in parts:
        if '-' in part:
            # 处理范围 (如 "1-5")
            try:
                start, end = map(int, part.split('-'))
                case_ids.update(range(start, end + 1))
            except ValueError:
                print(f"Warning: Invalid range format '{part}', skipping")
        else:
            # 处理单个数字
            try:
                case_ids.add(int(part))
            except ValueError:
                print(f"Warning: Invalid case_id '{part}', skipping")
    
    return case_ids

def modify_coverage(csv_file: str, yaml_file: str, stream_root: str):
    """根据YAML配置文件修改coverage信息"""
    try:
        # 读取YAML配置
        with open(yaml_file, 'r') as f:
            config = yaml.safe_load(f)
        
        # 读取CSV文件
        with open(csv_file, 'r', newline='') as f:
            reader = csv.reader(f)
            rows = list(reader)
            
        # 获取列索引
        header = rows[0]
        try:
            case_id_col = header.index('case_id')
            input_bs_col = header.index('INPUT_BS')
            coverage_col = header.index('coverage')
        except ValueError as e:
            print(f"Error: Required column not found - {str(e)}", file=sys.stderr)
            sys.exit(1)
        
        # 解析要处理的case_id
        target_case_ids = set()
        mode = config.get('mode', 'all')
        
        if mode == 'all':
            print("Processing all cases")
        elif mode == 'selected':
            case_id_str = config.get('case_id', '')
            target_case_ids = parse_case_ids(case_id_str)
            if not target_case_ids:
                print("Error: No valid case_ids specified for selected mode")
                sys.exit(1)
            print(f"Processing selected cases: {sorted(target_case_ids)}")
        else:
            print(f"Error: Unknown mode '{mode}'")
            sys.exit(1)
        
        # 计算处理的行数
        processed_rows = 0
        
        # 处理每一行
        for row in rows[1:]:
            if not row[input_bs_col]:
                continue  # 跳过没有输入文件的行
                
            # 获取case_id和input_bs
            case_id = row[case_id_col].strip()
            input_bs = row[input_bs_col].strip()
            
            # 根据模式决定是否处理当前行
            if mode == 'selected' and not case_id.isdigit():
                print(f"Warning: Non-numeric case_id '{case_id}', skipping")
                continue
                
            if mode == 'selected' and int(case_id) not in target_case_ids:
                continue
            
            print(f"Processing case_id: {case_id}, input_bs: {input_bs}")
            
            # 查找对应的readme文件
            readme_file = find_readme_file(input_bs, stream_root)
            if not readme_file:
                print(f"Warning: No readme file found for {input_bs}", file=sys.stderr)
                continue
            
            print(f"Found readme file: {readme_file}")
                
            # 获取readme中的feature列表和值
            readme_features = get_readme_features(readme_file)
            
            # 获取当前的coverage信息
            current_coverage = set()
            if row[coverage_col]:
                # 移除引号并分割
                coverage_str = row[coverage_col].strip('"')
                if coverage_str:  # 确保不是空字符串
                    current_coverage = set(coverage_str.split(','))
            
            # 打印YAML配置中的feature和值
            print(f"YAML config features:")
            for feature, value in config.get('features', {}).items():
                print(f"  - '{feature}': '{value}'")
            
            # 根据配置修改coverage
            for feature, value in config.get('features', {}).items():
                # 跳过非布尔值的参数，忽略大小写
                if not isinstance(value, str) or value.upper() not in ['ON', 'OFF', 'YES', 'NO']:
                    print(f"Skipping non-boolean feature: '{feature}' with value: '{value}'")
                    continue
                
                # 将value统一为大写以便比较
                value_upper = value.upper()
                
                # 在readme中查找匹配的feature
                # 1. 直接尝试精确匹配
                if feature in readme_features:
                    print(f"Exact match found: '{feature}'")
                    matching_feature = feature
                # 2. 忽略大小写匹配
                else:
                    matching_feature = None
                    for readme_feature in readme_features:
                        if feature.lower() == readme_feature.lower():
                            matching_feature = readme_feature
                            print(f"Case-insensitive match found: '{feature}' -> '{matching_feature}'")
                            break
                
                if matching_feature:
                    # 查看readme中的值
                    readme_value = readme_features[matching_feature]
                    print(f"Feature '{matching_feature}' in readme has value: '{readme_value}'")
                    
                    # 检查是否已存在相同值的参数（不区分大小写）
                    feature_exists = False
                    feature_upper = matching_feature.upper()
                    for existing in list(current_coverage):  # 使用list复制以避免在迭代时修改集合
                        existing_upper = existing.upper()
                        if existing_upper == feature_upper or existing_upper.startswith(f"{feature_upper}:"):
                            # 如果存在，先删除
                            current_coverage.remove(existing)
                            feature_exists = True
                            print(f"Removed existing feature: '{existing}'")
                            break
                    
                    # 根据值类型添加或删除参数，忽略大小写
                    if value_upper in ['ON', 'YES']:
                        current_coverage.add(matching_feature)
                        print(f"Added feature: '{matching_feature}'")
                    elif value_upper in ['OFF', 'NO']:
                        # OFF/NO值不需要添加，因为已经在上面的循环中删除了
                        print(f"Feature '{matching_feature}' set to {value_upper}, not adding to coverage")
                    else:
                        # 不应该走到这里，因为前面已经过滤了非布尔值
                        print(f"Unexpected value: '{value}' for feature: '{feature}'")
                else:
                    print(f"Feature '{feature}' not found in readme for {input_bs}, skipping")
            
            # 更新coverage列
            row[coverage_col] = f'"{",".join(sorted(current_coverage))}"'
            processed_rows += 1
            
            # 打印更新后的coverage
            print(f"Updated coverage: {sorted(current_coverage)}")
        
        # 写回CSV文件
        with open(csv_file, 'w', newline='') as f:
            writer = csv.writer(f)
            writer.writerows(rows)
        
        print(f"Successfully processed {processed_rows} rows in {csv_file}")
            
    except Exception as e:
        print(f"Error processing files: {str(e)}", file=sys.stderr)
        sys.exit(1)

def main():
    parser = argparse.ArgumentParser(
        description='Modify coverage information in CSV file using YAML configuration',
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    parser.add_argument('csv_file', help='Path to CSV file')
    parser.add_argument('stream_root', help='Root directory containing stream folders')
    parser.add_argument('yaml_file', help='Path to YAML configuration file')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.csv_file):
        print(f"Error: CSV file {args.csv_file} not found", file=sys.stderr)
        sys.exit(1)
    
    if not os.path.exists(args.stream_root):
        print(f"Error: Stream root directory {args.stream_root} not found", file=sys.stderr)
        sys.exit(1)
    
    if not os.path.exists(args.yaml_file):
        print(f"Error: Config file {args.yaml_file} not found", file=sys.stderr)
        sys.exit(1)
    
    # 显示当前配置文件内容
    with open(args.yaml_file, 'r') as f:
        config = yaml.safe_load(f)
        print(f"Config: mode = {config.get('mode', 'all')}")
        if config.get('mode') == 'selected':
            print(f"Config: case_id = {config.get('case_id', '')}")
        print(f"Config: features = {config.get('features', {})}")
    
    modify_coverage(args.csv_file, args.yaml_file, args.stream_root)
    print(f"Successfully processed {args.csv_file} using config file {args.yaml_file}")

if __name__ == '__main__':
    main() 