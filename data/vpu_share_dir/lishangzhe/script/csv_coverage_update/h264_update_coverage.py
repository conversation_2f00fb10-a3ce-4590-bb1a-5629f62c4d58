#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
update_coverage_values.py - 根据YAML配置文件更新CSV文件中的coverage栏

基于modify_coverage.py和get_coverage.py创建的新脚本，
允许用户通过YAML文件指定要刷入到CSV文件coverage栏的内容。

处理规则:
1. 当值为肯定布尔值（"yes"、"on"、"y"或类似格式，如"On (default)"）时（不区分大小写），只添加键到coverage栏，不添加值
2. 当值为否定布尔值（"no"、"off"、"n"或类似格式）时，不添加该特征到coverage栏
3. 当值为数字或包含数字的复杂格式（如"30 ( = 2 sec.)"）时，以"键=值"格式添加到coverage栏
4. 对于其他值，只将值添加到coverage栏，不添加键
5. 每次运行脚本时，会清除CSV文件中现有的coverage值，重新生成
6. 输出到CSV的coverage值顺序与YAML文件中定义的特征顺序一致

特征匹配规则:
1. 所有匹配过程均不区分大小写
2. 当YAML中出现包含"/"的特征（如"Frame/Field Coding"）时，会尝试匹配"/"左右两侧的任一部分
3. 当readme文件中出现包含"/"的特征（如"Video Resolution/FPS"）时，只要YAML中的特征与其中一部分匹配，就视为匹配成功
4. 如果上述匹配都失败，还会尝试部分匹配（检查包含关系）

特殊文件处理:
1. 对于包含"TOSHIBA"的文件，脚本会查找对应的param.csv文件（如CABA3_TOSHIBA_E_param.csv）
2. 从param.csv文件中提取特征和值，而不是从readme文件中提取
3. 其他处理规则保持不变

使用方法:
    python3 update_coverage_values.py <csv_file> <stream_root> <yaml_file>

参数:
    csv_file: CSV文件路径
    stream_root: 流文件根目录路径
    yaml_file: YAML配置文件路径

YAML配置文件格式示例:

1. 字典格式（键值对）:
```yaml
mode: 'selected'  # 'all' 或 'selected'
case_id: '1-3,5-10,100'  # 当mode为'selected'时使用
features:
  Loop Filter: 'Off'  # 将只添加 "Loop Filter"
  Adapt Ref Pic Mark: 'ON'  # 将只添加 "Adapt Ref Pic Mark"
  Number Slice Groups: '2'  # 将添加为 "Number Slice Groups:2"
  Profile: 'High'  # 将只添加 "High"
```

2. 列表格式（只有特征名称）:
```yaml
mode: 'selected'
case_id: '1-5,7-8,100'
features:
  - Loop Filter
  - Adapt Ref Pic Mark
  - Ref Pic List Reorder
  - Redundant Slices
  - Number Slice Groups
  - Access unit
  - Access unit test
  - entropy coding
```
对于列表格式，脚本会在readme文件中查找这些特征，并使用readme中的值。

3. 分离的key和value格式:
```yaml
mode: 'selected'
case_id: '1-5,7-8,100'
key:
  - Loop Filter
  - Adapt Ref Pic Mark
  - Ref Pic List Reorder
value:
  - field
  - CABAC
```
对于key部分，脚本会在readme文件中查找这些特征，并使用readme中的值。
对于value部分，脚本会在readme中查找匹配的值，并且只有当readme中的值与YAML中指定的值匹配时，才将该特征添加到coverage栏中。添加时会根据值的类型（数字、布尔值、其他值）应用相应的处理规则。
"""

import os
import csv
import argparse
import sys
import yaml
import re
from typing import Dict, Set, List

def find_param_file(input_bs: str, stream_root: str) -> str:
    """查找TOSHIBA文件对应的param.csv文件"""
    # 移除可能的文件扩展名
    input_bs_base = os.path.splitext(input_bs)[0]

    # 检查是否为TOSHIBA文件
    if "TOSHIBA" not in input_bs_base:
        return None

    # 构建可能的param.csv文件名
    param_file_name = f"{input_bs_base}_param.csv"

    # 搜索可能的目录
    for root, _, files in os.walk(stream_root):
        for file in files:
            if file == param_file_name:
                return os.path.join(root, file)

            # 尝试其他可能的命名格式
            if file.endswith("_param.csv") and input_bs_base in file:
                return os.path.join(root, file)

    # 如果没有找到精确匹配，尝试查找包含input_bs_base的param.csv文件
    for root, _, files in os.walk(stream_root):
        for file in files:
            if file.endswith("_param.csv") and any(part in file for part in input_bs_base.split('_')):
                return os.path.join(root, file)

    return None

def find_readme_file(input_bs: str, stream_root: str) -> str:
    """根据input_bs文件名查找对应的readme文件"""
    # 移除可能的文件扩展名
    input_bs_base = os.path.splitext(input_bs)[0]

    # 首先尝试直接查找对应的readme文件
    potential_dirs = []

    # 方法1: 直接搜索包含input_bs基本名称的目录
    for root, dirs, files in os.walk(stream_root):
        # 检查目录名是否匹配
        if input_bs_base in os.path.basename(root):
            potential_dirs.append(root)

        # 检查目录中的文件
        for file in files:
            # 1. 查找名称匹配的readme文件
            if 'readme' in file.lower() and input_bs_base in file:
                return os.path.join(root, file)

            # 2. 如果找到匹配的bs文件，查找同目录下的readme
            if file == input_bs:
                readme_files = [f for f in files if 'readme' in f.lower()]
                if readme_files:
                    return os.path.join(root, readme_files[0])

    # 从潜在目录中查找任何readme文件
    for dir_path in potential_dirs:
        for root, _, files in os.walk(dir_path):
            readme_files = [f for f in files if 'readme' in f.lower()]
            if readme_files:
                return os.path.join(root, readme_files[0])

    # 最后尝试在可能的bs文件所在目录查找
    for root, _, files in os.walk(stream_root):
        if input_bs in files:
            readme_files = [f for f in files if 'readme' in f.lower()]
            if readme_files:
                return os.path.join(root, readme_files[0])

    # 尝试查找内容包含input_bs的readme文件
    for root, _, files in os.walk(stream_root):
        for file in files:
            if 'readme' in file.lower():
                readme_path = os.path.join(root, file)
                try:
                    with open(readme_path, 'r', encoding='utf-8', errors='ignore') as f:
                        content = f.read()
                        if input_bs_base in content:
                            return readme_path
                except Exception:
                    continue

    return None

def get_param_csv_features(param_file: str) -> Dict[str, str]:
    """从TOSHIBA的param.csv文件中提取所有feature及其值"""
    features = {}
    try:
        with open(param_file, 'r', encoding='utf-8', errors='ignore') as f:
            # 使用csv模块读取文件
            reader = csv.reader(f)
            rows = list(reader)

        # 跳过第一行（通常是标题或空行）
        for row in rows[1:]:
            # 确保行至少有两列
            if len(row) >= 2:
                feature_name = row[0].strip()
                feature_value = row[1].strip()

                # 跳过空特征名或空值
                if not feature_name or not feature_value:
                    continue

                # 清理特征名和值
                feature_name = feature_name.strip(',').strip()
                feature_value = feature_value.strip(',').strip()

                # 记录所有找到的feature和值
                features[feature_name] = feature_value

        # 打印param.csv中找到的所有特征
        print(f"All features found in {param_file}:")
        for feature, value in features.items():
            print(f"  - '{feature}': '{value}'")

    except Exception as e:
        print(f"Error reading param.csv file {param_file}: {str(e)}", file=sys.stderr)

    return features

def get_readme_features(readme_file: str) -> Dict[str, str]:
    """从readme文件中提取所有feature及其值"""
    features = {}
    try:
        with open(readme_file, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()

        # 从上下文内容示例看，文件中的格式可能有以下几种:
        # 1. Loop Filter:             On (default)
        # 2. Loop filter                         ON
        # 3. :      CAVLC (特殊格式，冒号开头)

        # 定义模式来匹配这些格式
        patterns = [
            # 格式1：参数名: 值
            r"^([^:]+?)\s*:\s*(\S[^\n]*?)\s*$",
            # 格式2：参数名后跟多个空格然后是值
            r"^(\S[^0-9\n]+?)\s{2,}(\S[^\n]*?)\s*$",
            # 格式3：冒号开头，后面是值（特殊格式）
            r"^:\s*(\S[^\n]*?)\s*$"
        ]

        for pattern in patterns:
            matches = re.finditer(pattern, content, re.IGNORECASE | re.MULTILINE)
            for match in matches:
                if pattern == r"^:\s*(\S[^\n]*?)\s*$":
                    # 对于特殊格式，值就是特征名
                    feature_value = match.group(1).strip()
                    feature_name = feature_value  # 使用值作为特征名
                else:
                    feature_name = match.group(1).strip()
                    feature_value = match.group(2).strip()

                # 清理特征名和值
                feature_name = feature_name.strip(':').strip()
                feature_value = feature_value.strip(':').strip()

                # 跳过空特征名
                if not feature_name:
                    continue

                # 记录所有找到的feature和值
                features[feature_name] = feature_value

        # 打印readme中找到的所有特征
        print(f"All features found in {readme_file}:")
        for feature, value in features.items():
            print(f"  - '{feature}': '{value}'")

    except Exception as e:
        print(f"Error reading readme file {readme_file}: {str(e)}", file=sys.stderr)

    return features

def parse_case_ids(case_id_str: str) -> Set[int]:
    """解析case_id字符串，支持单个、连续范围和非连续多个格式"""
    case_ids = set()

    # 处理空字符串
    if not case_id_str or not case_id_str.strip():
        return case_ids

    # 分割非连续部分 (如 "1-5, 7-8, 100")
    parts = [p.strip() for p in case_id_str.split(',')]

    for part in parts:
        if '-' in part:
            # 处理范围 (如 "1-5")
            try:
                start, end = map(int, part.split('-'))
                case_ids.update(range(start, end + 1))
            except ValueError:
                print(f"Warning: Invalid range format '{part}', skipping")
        else:
            # 处理单个数字
            try:
                case_ids.add(int(part))
            except ValueError:
                print(f"Warning: Invalid case_id '{part}', skipping")

    return case_ids

def update_coverage_values(csv_file: str, yaml_file: str, stream_root: str):
    """根据YAML配置文件更新coverage信息，只刷入值而不刷入键"""
    try:
        # 读取YAML配置
        with open(yaml_file, 'r') as f:
            config = yaml.safe_load(f)

        # 读取CSV文件
        with open(csv_file, 'r', newline='') as f:
            reader = csv.reader(f)
            rows = list(reader)

        # 获取列索引
        header = rows[0]
        try:
            case_id_col = header.index('case_id')
            input_bs_col = header.index('INPUT_BS')
            coverage_col = header.index('coverage')
        except ValueError as e:
            print(f"Error: Required column not found - {str(e)}", file=sys.stderr)
            sys.exit(1)

        # 解析要处理的case_id
        target_case_ids = set()
        mode = config.get('mode', 'all')

        if mode == 'all':
            print("Processing all cases")
        elif mode == 'selected':
            case_id_str = config.get('case_id', '')
            target_case_ids = parse_case_ids(case_id_str)
            if not target_case_ids:
                print("Error: No valid case_ids specified for selected mode")
                sys.exit(1)
            print(f"Processing selected cases: {sorted(target_case_ids)}")
        else:
            print(f"Error: Unknown mode '{mode}'")
            sys.exit(1)

        # 计算处理的行数和修改的行数
        processed_rows = 0
        modified_rows = 0

        # 处理每一行
        for row in rows[1:]:
            if not row[input_bs_col]:
                continue  # 跳过没有输入文件的行

            # 获取case_id和input_bs
            case_id = row[case_id_col].strip()
            input_bs = row[input_bs_col].strip()

            # 根据模式决定是否处理当前行
            if mode == 'selected' and not case_id.isdigit():
                print(f"Warning: Non-numeric case_id '{case_id}', skipping")
                continue

            if mode == 'selected' and int(case_id) not in target_case_ids:
                continue

            print(f"Processing case_id: {case_id}, input_bs: {input_bs}")

            # 检查是否为TOSHIBA文件，如果是则查找param.csv文件
            is_toshiba = "TOSHIBA" in input_bs
            param_file = None
            readme_file = None
            readme_features = {}

            if is_toshiba:
                # 查找对应的param.csv文件
                param_file = find_param_file(input_bs, stream_root)
                if param_file:
                    print(f"Found param.csv file for TOSHIBA: {param_file}")
                    # 获取param.csv中的feature列表和值
                    readme_features = get_param_csv_features(param_file)
                else:
                    print(f"Warning: No param.csv file found for TOSHIBA file {input_bs}", file=sys.stderr)

            # 如果不是TOSHIBA文件或没有找到param.csv文件，则查找readme文件
            if not is_toshiba or not param_file:
                readme_file = find_readme_file(input_bs, stream_root)
                if not readme_file:
                    print(f"Warning: No readme file found for {input_bs}", file=sys.stderr)
                    continue

                print(f"Found readme file: {readme_file}")

                # 获取readme中的feature列表和值
                readme_features = get_readme_features(readme_file)

            # 获取当前的coverage信息
            # 使用列表而不是集合，以保持顺序
            current_coverage = []

            # 注意：我们不再使用现有的coverage值，而是每次都重新开始
            # 如果需要保留现有值，请取消下面的注释

            # if row[coverage_col]:
            #     # 移除引号并分割
            #     coverage_str = row[coverage_col].strip('"')
            #     if coverage_str:  # 确保不是空字符串
            #         current_coverage = coverage_str.split(',')

            # 获取YAML配置中的features
            features = config.get('features', [])
            keys = config.get('key', [])
            values = config.get('value', [])

            print(f"YAML config features:")

            # 处理不同格式的features配置
            feature_items = []

            # 处理features部分（如果存在）
            if features:
                if isinstance(features, dict):
                    # 如果是字典格式，包含键值对
                    for feature, value in features.items():
                        print(f"  - '{feature}': '{value}'")
                        feature_items.append((feature, value))
                elif isinstance(features, list):
                    # 如果是列表格式，只包含特征名称
                    for feature in features:
                        feature = feature.strip() if isinstance(feature, str) else str(feature)
                        print(f"  - '{feature}'")
                        # 对于只有特征名称的情况，值设为None
                        feature_items.append((feature, None))
                else:
                    print(f"Warning: Unsupported features format: {type(features)}")

            # 处理key部分（如果存在）
            if keys:
                print(f"YAML config keys:")
                if isinstance(keys, list):
                    for key in keys:
                        key = key.strip() if isinstance(key, str) else str(key)
                        print(f"  - '{key}'")
                        # 对于key部分，值设为None
                        feature_items.append((key, None))
                else:
                    print(f"Warning: Unsupported key format: {type(keys)}")

            # 处理value部分（如果存在）
            if values:
                print(f"YAML config values:")
                if isinstance(values, list):
                    for value in values:
                        value = value.strip() if isinstance(value, str) else str(value)
                        print(f"  - '{value}'")
                        # 对于value部分，直接添加值
                        feature_items.append((None, value))
                else:
                    print(f"Warning: Unsupported value format: {type(values)}")

            # 如果没有找到任何有效的配置，输出警告
            if not feature_items:
                print(f"Warning: No valid features, keys or values found in YAML config")

            # 根据配置修改coverage
            for feature, value in feature_items:
                # 如果特征为None（只有值的情况）
                if feature is None:
                    if value is None:
                        print(f"Skipping item with both feature and value as None")
                        continue

                    print(f"Processing value: '{value}'")

                    # 在readme中查找匹配的值
                    found_match = False
                    matching_feature = None

                    # 将值转换为小写，用于比较
                    value_lower = value.lower() if value else ""

                    # 检查readme中的所有特征和值
                    for rf, rf_value in readme_features.items():
                        # 将readme中的值转换为小写，用于比较
                        rf_value_lower = rf_value.lower() if rf_value else ""

                        # 检查值是否匹配
                        if value_lower == rf_value_lower:
                            found_match = True
                            matching_feature = rf
                            print(f"Found matching value '{value}' for feature '{matching_feature}' in readme")
                            break

                        # 处理复杂格式的值（如带括号的值）
                        if '(' in rf_value_lower and value_lower in rf_value_lower:
                            found_match = True
                            matching_feature = rf
                            print(f"Found partial matching value '{value}' in '{rf_value}' for feature '{matching_feature}' in readme")
                            break

                    # 如果在readme中找到匹配的值
                    if found_match:
                        # 检查值是否为yes/no/on/off (不区分大小写)
                        value_upper = value.upper() if value else ""
                        # 移除可能的括号和额外文本，如"On (default)"
                        if '(' in value_upper:
                            value_upper = value_upper.split('(')[0].strip()

                        # 增强布尔值识别
                        is_boolean_value = (value_upper in ['YES', 'NO', 'ON', 'OFF', 'Y', 'N'] or
                                           value_upper.startswith('ON') or value_upper.startswith('OFF') or
                                           value_upper.startswith('YES') or value_upper.startswith('NO'))

                        # 检查布尔值是否为否定值（no、off等）
                        if is_boolean_value:
                            is_negative_value = False
                            value_upper_clean = value_upper.strip()
                            if value_upper_clean in ['NO', 'OFF', 'N'] or value_upper_clean.startswith('NO') or value_upper_clean.startswith('OFF'):
                                is_negative_value = True
                                print(f"检测到否定值: '{value}'，不添加此特征")
                                continue

                            # 对于肯定布尔值，添加特征名称
                            if matching_feature not in current_coverage:
                                current_coverage.append(matching_feature)
                                print(f"添加特征: '{matching_feature}' 到coverage（对应布尔值: '{value}'）")

                        # 检查是否为数字值
                        else:
                            is_numeric = False
                            numeric_value = value

                            # 尝试直接转换为数字
                            try:
                                if value:
                                    float(value)  # 尝试将值转换为数字
                                    is_numeric = True
                            except ValueError:
                                # 如果直接转换失败，尝试提取复杂格式中的数字
                                # 例如："30 ( = 2 sec.)" -> "30"
                                if value:
                                    # 提取字符串开头的数字部分
                                    match = re.match(r'^\s*(\d+(?:\.\d+)?)', value)
                                    if match:
                                        numeric_part = match.group(1)
                                        try:
                                            float(numeric_part)  # 验证提取的部分是否为有效数字
                                            is_numeric = True
                                            numeric_value = numeric_part  # 使用提取的数字部分
                                            print(f"从复杂格式 '{value}' 中提取数字值: '{numeric_value}'")
                                        except ValueError:
                                            pass

                            # 根据值的类型决定如何处理
                            if is_numeric:
                                # 对于数字值，添加"特征=值"格式
                                entry_to_add = f"{matching_feature}={numeric_value}"
                                if entry_to_add not in current_coverage:
                                    current_coverage.append(entry_to_add)
                                    print(f"添加键值对: '{entry_to_add}' 到coverage")
                            else:
                                # 对于其他值，只添加值
                                if value not in current_coverage:
                                    current_coverage.append(value)
                                    print(f"添加值: '{value}' 到coverage")
                    else:
                        print(f"Value '{value}' not found in readme, skipping")

                    continue

                # 如果值为None（只有特征名称的情况）
                if value is None:
                    print(f"Processing feature without value: '{feature}'")
                # 只处理字符串类型的值
                elif not isinstance(value, str):
                    print(f"Skipping non-string feature value: '{feature}' with value: '{value}'")
                    continue

                # 在readme中查找匹配的feature（完全不区分大小写）
                matching_feature = None

                # 将YAML特征转换为小写，用于比较
                feature_lower = feature.lower()

                # 处理YAML中包含"/"的特征（如"Frame/Field Coding"）
                yaml_feature_parts = []
                if '/' in feature:
                    yaml_feature_parts = [part.strip().lower() for part in feature.split('/')]
                    print(f"YAML feature contains '/': '{feature}' -> {yaml_feature_parts}")

                # 创建readme特征的小写版本字典，用于快速查找
                readme_features_lower = {}
                for rf in readme_features:
                    readme_features_lower[rf.lower()] = rf

                # 1. 直接尝试精确匹配（不区分大小写）
                if feature_lower in readme_features_lower:
                    matching_feature = readme_features_lower[feature_lower]
                    print(f"Case-insensitive exact match found: '{feature}' -> '{matching_feature}'")

                # 2. 如果没有精确匹配，尝试其他匹配方式
                if not matching_feature:
                    # 处理YAML中包含"/"的特征（如"Frame/Field Coding"）
                    # 尝试匹配"/"左右两侧的任一部分
                    if yaml_feature_parts:
                        for part in yaml_feature_parts:
                            if part in readme_features_lower:
                                matching_feature = readme_features_lower[part]
                                print(f"Partial match found for YAML feature part '{part}' -> '{matching_feature}'")
                                break

                    # 处理readme中包含"/"的特征（如"Video Resolution/FPS"）
                    # 只要YAML特征与其中一部分匹配，就视为匹配成功
                    if not matching_feature:
                        for rf_lower, rf in readme_features_lower.items():
                            if '/' in rf_lower:
                                readme_parts = [part.strip() for part in rf_lower.split('/')]
                                for part in readme_parts:
                                    if feature_lower == part:
                                        matching_feature = rf
                                        print(f"Partial match found for readme feature part '{part}' -> '{matching_feature}'")
                                        break
                                if matching_feature:
                                    break

                    # 如果仍未找到匹配，尝试部分匹配（包含关系）
                    if not matching_feature:
                        for rf_lower, rf in readme_features_lower.items():
                            # 检查YAML特征是否包含在readme特征中
                            if feature_lower in rf_lower:
                                matching_feature = rf
                                print(f"Substring match found: '{feature}' is part of '{matching_feature}'")
                                break
                            # 检查readme特征是否包含在YAML特征中
                            elif rf_lower in feature_lower:
                                matching_feature = rf
                                print(f"Substring match found: '{matching_feature}' is part of '{feature}'")
                                break

                if matching_feature:
                    # 查看readme中的值
                    readme_value = readme_features[matching_feature]
                    print(f"Feature '{matching_feature}' in readme has value: '{readme_value}'")

                    # 如果值为None（只有特征名称的情况），使用readme中的值
                    if value is None:
                        value = readme_value
                        print(f"Using value from readme: '{value}'")

                    # 检查值是否为yes/no/on/off (不区分大小写)
                    value_upper = value.upper() if value else ""
                    # 移除可能的括号和额外文本，如"On (default)"
                    if '(' in value_upper:
                        value_upper = value_upper.split('(')[0].strip()

                    # 增强布尔值识别
                    is_boolean_value = (value_upper in ['YES', 'NO', 'ON', 'OFF', 'Y', 'N'] or
                                       value_upper.startswith('ON') or value_upper.startswith('OFF') or
                                       value_upper.startswith('YES') or value_upper.startswith('NO'))

                    # 检查是否为数字值
                    is_numeric = False
                    numeric_value = value

                    # 尝试直接转换为数字
                    try:
                        if value:
                            float(value)  # 尝试将值转换为数字
                            is_numeric = True
                    except ValueError:
                        # 如果直接转换失败，尝试提取复杂格式中的数字
                        # 例如："30 ( = 2 sec.)" -> "30"
                        if value:
                            # 提取字符串开头的数字部分
                            match = re.match(r'^\s*(\d+(?:\.\d+)?)', value)
                            if match:
                                numeric_part = match.group(1)
                                try:
                                    float(numeric_part)  # 验证提取的部分是否为有效数字
                                    is_numeric = True
                                    numeric_value = numeric_part  # 使用提取的数字部分
                                    print(f"从复杂格式 '{value}' 中提取数字值: '{numeric_value}'")
                                except ValueError:
                                    pass

                    if is_boolean_value:
                        # 检查布尔值是否为否定值（no、off等）
                        is_negative_value = False
                        value_upper_clean = value_upper.strip()
                        if value_upper_clean in ['NO', 'OFF', 'N'] or value_upper_clean.startswith('NO') or value_upper_clean.startswith('OFF'):
                            is_negative_value = True
                            print(f"检测到否定值: '{value}'，不添加此特征")

                        # 首先检查是否已存在相关条目
                        # 创建一个新列表，只保留不需要移除的条目
                        new_coverage = []
                        for existing in current_coverage:
                            # 检查是否需要移除
                            if existing.lower() == matching_feature.lower() or existing.lower().startswith(matching_feature.lower() + ':'):
                                print(f"移除已存在的条目: '{existing}'")
                            elif existing.lower() in ['yes', 'no', 'on', 'off', 'y', 'n']:
                                print(f"移除已存在的布尔值: '{existing}'")
                            elif ':' in existing and existing.split(':', 1)[1].lower() in ['yes', 'no', 'on', 'off', 'y', 'n']:
                                print(f"移除已存在的键值对: '{existing}'")
                            else:
                                # 保留不需要移除的条目
                                new_coverage.append(existing)

                        # 更新current_coverage
                        current_coverage = new_coverage

                        # 只有在值不是否定值时才添加键
                        if not is_negative_value:
                            current_coverage.append(matching_feature)
                            print(f"添加键: '{matching_feature}' 到coverage")
                        else:
                            print(f"由于值为否定值，不添加特征: '{matching_feature}'")

                    elif is_numeric:
                        # 对于数字值，添加"键=值"格式
                        # 创建一个新列表，只保留不需要移除的条目
                        new_coverage = []
                        for existing in current_coverage:
                            # 检查是否需要移除
                            if existing.lower() == matching_feature.lower() or \
                               existing.lower().startswith(matching_feature.lower() + ':') or \
                               existing.lower().startswith(matching_feature.lower() + '='):
                                print(f"移除已存在的条目: '{existing}'")
                            else:
                                try:
                                    # 尝试直接作为数字比较
                                    if float(existing) == float(numeric_value):
                                        print(f"移除已存在的数字值: '{existing}'")
                                    # 检查是否为"key:value"或"key=value"格式
                                    elif ':' in existing or '=' in existing:
                                        separator = ':' if ':' in existing else '='
                                        parts = existing.split(separator, 1)
                                        if len(parts) == 2:
                                            try:
                                                existing_value = parts[1].strip()
                                                # 尝试提取数字部分
                                                match = re.match(r'^\s*(\d+(?:\.\d+)?)', existing_value)
                                                if match and float(match.group(1)) == float(numeric_value):
                                                    print(f"移除已存在的键值对: '{existing}'")
                                                else:
                                                    # 保留不需要移除的条目
                                                    new_coverage.append(existing)
                                            except ValueError:
                                                # 保留不需要移除的条目
                                                new_coverage.append(existing)
                                        else:
                                            # 保留不需要移除的条目
                                            new_coverage.append(existing)
                                    else:
                                        # 保留不需要移除的条目
                                        new_coverage.append(existing)
                                except ValueError:
                                    # 保留不需要移除的条目
                                    new_coverage.append(existing)

                        # 更新current_coverage
                        current_coverage = new_coverage

                        # 添加"键=值"格式（使用提取的数字值）
                        entry_to_add = f"{matching_feature}={numeric_value}"
                        current_coverage.append(entry_to_add)
                        print(f"添加键值对: '{entry_to_add}' 到coverage")

                    else:
                        # 对于其他非布尔值，只添加值
                        # 创建一个新列表，只保留不需要移除的条目
                        new_coverage = []
                        for existing in current_coverage:
                            # 检查是否需要移除
                            if existing.lower() == matching_feature.lower() or existing.lower().startswith(matching_feature.lower() + ':'):
                                print(f"移除已存在的条目: '{existing}'")
                            elif existing.lower() == value.lower():
                                print(f"移除已存在的值: '{existing}'")
                            else:
                                # 保留不需要移除的条目
                                new_coverage.append(existing)

                        # 更新current_coverage
                        current_coverage = new_coverage

                        # 添加值
                        current_coverage.append(value)
                        print(f"添加值: '{value}' 到coverage")
                else:
                    print(f"Feature '{feature}' not found in readme for {input_bs}, skipping")

            # 检查是否有修改
            old_coverage_value = row[coverage_col]
            # 使用列表的顺序，不进行排序
            new_coverage_value = f'"{",".join(current_coverage)}"'

            if old_coverage_value != new_coverage_value:
                # 更新coverage列
                row[coverage_col] = new_coverage_value
                modified_rows += 1
                print(f"Coverage已修改:")
                print(f"  旧值: {old_coverage_value}")
                print(f"  新值: {new_coverage_value}")
            else:
                print(f"Coverage未修改，保持原值")

            processed_rows += 1

            # 打印更新后的coverage
            print(f"Updated coverage: {current_coverage}")

        # 检查是否有行被修改
        if modified_rows == 0:
            print(f"\n没有任何行被修改，不需要更新CSV文件")
            return

        # 创建备份文件
        backup_file = f"{csv_file}.bak"
        print(f"\n创建原始CSV文件的备份: {backup_file}")
        try:
            with open(csv_file, 'r', newline='', encoding='utf-8') as src, open(backup_file, 'w', newline='', encoding='utf-8') as dst:
                dst.write(src.read())
            print(f"备份创建成功")
        except Exception as e:
            print(f"警告: 创建备份失败: {str(e)}", file=sys.stderr)

        # 写回CSV文件
        print(f"正在将 {modified_rows} 行的修改写入CSV文件...")
        try:
            # 先写入临时文件
            temp_file = f"{csv_file}.tmp"
            print(f"正在写入临时文件: {temp_file}")
            with open(temp_file, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                writer.writerows(rows)

            # 检查临时文件是否有内容
            if os.path.getsize(temp_file) > 0:
                print(f"临时文件写入成功，大小: {os.path.getsize(temp_file)} 字节")
                # 替换原文件
                os.replace(temp_file, csv_file)
                print(f"成功处理 {processed_rows} 行，修改了 {modified_rows} 行")
                print(f"CSV文件已更新: {csv_file}")
            else:
                print(f"错误: 生成的CSV文件为空。使用备份文件替代。", file=sys.stderr)
                os.replace(backup_file, csv_file)
                sys.exit(1)
        except Exception as e:
            print(f"写入CSV文件时出错: {str(e)}", file=sys.stderr)
            print(f"正在从备份恢复...")
            try:
                if os.path.exists(backup_file):
                    os.replace(backup_file, csv_file)
                    print(f"已从备份恢复原始CSV文件")
                else:
                    print(f"错误: 未找到备份文件", file=sys.stderr)
            except Exception as restore_error:
                print(f"恢复备份时出错: {str(restore_error)}", file=sys.stderr)
            sys.exit(1)

    except Exception as e:
        print(f"处理文件时出错: {str(e)}", file=sys.stderr)
        import traceback
        traceback.print_exc()
        sys.exit(1)

def main():
    parser = argparse.ArgumentParser(
        description='Update coverage values in CSV file using YAML configuration',
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    parser.add_argument('csv_file', help='Path to CSV file')
    parser.add_argument('stream_root', help='Root directory containing stream folders')
    parser.add_argument('yaml_file', help='Path to YAML configuration file')

    args = parser.parse_args()

    if not os.path.exists(args.csv_file):
        print(f"Error: CSV file {args.csv_file} not found", file=sys.stderr)
        sys.exit(1)

    if not os.path.exists(args.stream_root):
        print(f"Error: Stream root directory {args.stream_root} not found", file=sys.stderr)
        sys.exit(1)

    if not os.path.exists(args.yaml_file):
        print(f"Error: Config file {args.yaml_file} not found", file=sys.stderr)
        sys.exit(1)

    # 显示当前配置文件内容
    with open(args.yaml_file, 'r') as f:
        config = yaml.safe_load(f)
        print(f"Config: mode = {config.get('mode', 'all')}")
        if config.get('mode') == 'selected':
            print(f"Config: case_id = {config.get('case_id', '')}")
        print(f"Config: features = {config.get('features', {})}")

    try:
        update_coverage_values(args.csv_file, args.yaml_file, args.stream_root)
        print(f"成功使用配置文件 {args.yaml_file} 处理 {args.csv_file}")
    except Exception as e:
        print(f"执行过程中出错: {str(e)}", file=sys.stderr)
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == '__main__':
    main()
