# vc9000e cmodel mapping configuration

# Executable configuration
executable:
  default_args:
    #- "--verbose"
    #- "--log-level=info"

# Parameter mappings
mappings:
  case_id:
    csv_column: "case_id"
    param_name: ""
    param_type: "string"
    required: False
    description: "case_id parameter"

  flag:
    csv_column: "flag"
    param_name: ""
    param_type: "string"
    required: False
    description: "flag parameter"

  milestone:
    csv_column: "milestone"
    param_name: ""
    param_type: "string"
    required: False
    description: "milestone parameter"

  cv_id:
    csv_column: "cv_id"
    param_name: ""
    param_type: "string"
    required: False
    description: "cv_id parameter"

  codec:
    csv_column: "codec"
    param_name: ""
    param_type: "string"
    required: False
    description: "codec parameter"

  category:
    csv_column: "category"
    param_name: ""
    param_type: "string"
    required: False
    description: "category parameter"

  coverage:
    csv_column: "coverage"
    param_name: ""
    param_type: "string"
    required: False
    description: "coverage parameter"

  firstpic:
    csv_column: "firstPic"
    param_name: "firstPic"
    param_type: "integer"
    required: False
    description: "firstPic parameter"

  lastpic:
    csv_column: "lastPic"
    param_name: "lastPic"
    param_type: "integer"
    required: False
    description: "lastPic parameter"

  const_chroma:
    csv_column: "const_chroma"
    param_name: ""
    param_type: "string"
    required: False
    description: "const_chroma parameter"

  gop_cfg:
    csv_column: "gop_cfg"
    param_name: ""
    param_type: "string"
    required: False
    description: "gop_cfg parameter"

  rdo_cfg:
    csv_column: "rdo_cfg"
    param_name: ""
    param_type: "string"
    required: False
    description: "rdo_cfg parameter"

  sei_cfg:
    csv_column: "sei_cfg"
    param_name: ""
    param_type: "string"
    required: False
    description: "sei_cfg parameter"

  vui_cfg:
    csv_column: "vui_cfg"
    param_name: ""
    param_type: "string"
    required: False
    description: "vui_cfg parameter"

  hdr10_cfg:
    csv_column: "hdr10_cfg"
    param_name: ""
    param_type: "string"
    required: False
    description: "hdr10_cfg parameter"

  eos_cfg:
    csv_column: "eos_cfg"
    param_name: ""
    param_type: "string"
    required: False
    description: "eos_cfg parameter"

  intrarecon_cfg:
    csv_column: "intrarecon_cfg"
    param_name: ""
    param_type: "string"
    required: False
    description: "intrarecon_cfg parameter"

  me_cfg:
    csv_column: "me_cfg"
    param_name: ""
    param_type: "string"
    required: False
    description: "me_cfg parameter"

  rc_cfg:
    csv_column: "rc_cfg"
    param_name: ""
    param_type: "string"
    required: False
    description: "rc_cfg parameter"

  vq_cfg:
    csv_column: "vq_cfg"
    param_name: ""
    param_type: "string"
    required: False
    description: "vq_cfg parameter"

  frame_rate_cfg:
    csv_column: "frame_rate_cfg"
    param_name: ""
    param_type: "string"
    required: False
    description: "frame_rate_cfg parameter"

  roi_cfg:
    csv_column: "roi_cfg"
    param_name: ""
    param_type: "string"
    required: False
    description: "roi_cfg parameter"

  statistics_cfg:
    csv_column: "statistics_cfg"
    param_name: ""
    param_type: "string"
    required: False
    description: "statistics_cfg parameter"

  rfc_cfg:
    csv_column: "rfc_cfg"
    param_name: ""
    param_type: "string"
    required: False
    description: "rfc_cfg parameter"

  osd_cfg:
    csv_column: "osd_cfg"
    param_name: ""
    param_type: "string"
    required: False
    description: "osd_cfg parameter"

  osd_map_cfg:
    csv_column: "osd_map_cfg"
    param_name: ""
    param_type: "string"
    required: False
    description: "osd_map_cfg parameter"

  mosaic_cfg:
    csv_column: "mosaic_cfg"
    param_name: ""
    param_type: "string"
    required: False
    description: "mosaic_cfg parameter"

  alignment_cfg:
    csv_column: "alignment_cfg"
    param_name: ""
    param_type: "string"
    required: False
    description: "alignment_cfg parameter"

  parallel_cfg:
    csv_column: "parallel_cfg"
    param_name: ""
    param_type: "string"
    required: False
    description: "parallel_cfg parameter"

  axi_cfg:
    csv_column: "axi_cfg"
    param_name: ""
    param_type: "string"
    required: False
    description: "axi_cfg parameter"

  irq_cfg:
    csv_column: "irq_cfg"
    param_name: ""
    param_type: "string"
    required: False
    description: "irq_cfg parameter"

  debug_cfg:
    csv_column: "debug_cfg"
    param_name: ""
    param_type: "string"
    required: False
    description: "debug_cfg parameter"

  md5_ref:
    csv_column: "md5_ref"
    param_name: ""
    param_type: "string"
    required: False
    description: "md5_ref parameter"

  md5:
    csv_column: "md5"
    param_name: ""
    param_type: "string"
    required: False
    description: "md5 parameter"

  result:
    csv_column: "result"
    param_name: ""
    param_type: "string"
    required: False
    description: "result parameter"

  input:
    yaml_cfg: "input"
    description: "parameter from yaml in io_cfg column"
    param_name: "input"
    param_type: "string"
    dir_type: "stream"
    required: False

  output:
    yaml_cfg: "output"
    description: "parameter from yaml in io_cfg column"
    param_name: "output"
    param_type: "string"
    required: False

  lumWidthSrc:
    yaml_cfg: "lumWidthSrc"
    description: "parameter from yaml in io_cfg column"
    param_name: "lumWidthSrc"
    param_type: "integer"
    required: False

  lumHeightSrc:
    yaml_cfg: "lumHeightSrc"
    description: "parameter from yaml in io_cfg column"
    param_name: "lumHeightSrc"
    param_type: "integer"
    required: False

  inputFormat:
    yaml_cfg: "inputFormat"
    description: "parameter from yaml in io_cfg column"
    param_name: "inputFormat"
    param_type: "integer"
    required: False

  frameType:
    yaml_cfg: "frameType"
    description: "parameter from yaml in io_cfg column"
    param_name: "frameType"
    param_type: "integer"
    required: False

  inputRateNumer:
    yaml_cfg: "inputRateNumer"
    description: "parameter from yaml in io_cfg column"
    param_name: "inputRateNumer"
    param_type: "integer"
    required: False

  inputRateDenom:
    yaml_cfg: "inputRateDenom"
    description: "parameter from yaml in io_cfg column"
    param_name: "inputRateDenom"
    param_type: "integer"
    required: False

  outputRateNumer:
    yaml_cfg: "outputRateNumer"
    description: "parameter from yaml in io_cfg column"
    param_name: "outputRateNumer"
    param_type: "string"
    required: False

  outputRateDenom:
    yaml_cfg: "outputRateDenom"
    description: "parameter from yaml in io_cfg column"
    param_name: "outputRateDenom"
    param_type: "string"
    required: False

  inputFileList:
    yaml_cfg: "inputFileList"
    description: "parameter from yaml in io_cfg column"
    param_name: "inputFileList"
    param_type: "string"
    dir_type: "cfg"
    required: False

  outBufSizeMax:
    yaml_cfg: "outBufSizeMax"
    description: "parameter from yaml in io_cfg column"
    param_name: "outBufSizeMax"
    param_type: "integer"
    required: False

  write:
    yaml_cfg: "write"
    description: "parameter from yaml in io_cfg column"
    param_name: "write"
    
  width:
    yaml_cfg: "width"
    description: "parameter from yaml in io_cfg column"
    param_name: "width"
    param_type: "string"
    required: False

  height:
    yaml_cfg: "height"
    description: "parameter from yaml in io_cfg column"
    param_name: "height"
    param_type: "string"
    required: False

  horOffsetSrc:
    yaml_cfg: "horOffsetSrc"
    description: "parameter from yaml in io_cfg column"
    param_name: "horOffsetSrc"
    param_type: "integer"
    required: False

  verOffsetSrc:
    yaml_cfg: "verOffsetSrc"
    description: "parameter from yaml in io_cfg column"
    param_name: "verOffsetSrc"
    param_type: "integer"
    required: False

  outReconFrame:
    yaml_cfg: "outReconFrame"
    description: "parameter from yaml in io_cfg column"
    param_name: "outReconFrame"
    param_type: "integer"
    required: False

  writeReconToDDR:
    yaml_cfg: "writeReconToDDR"
    description: "parameter from yaml in io_cfg column"
    param_name: "writeReconToDDR"
    param_type: "integer"
    required: False

  streamBufChain:
    yaml_cfg: "streamBufChain"
    description: "parameter from yaml in io_cfg column"
    param_name: "streamBufChain"
    param_type: "integer"
    required: False

  codedChromaIdc:
    yaml_cfg: "codedChromaIdc"
    description: "parameter from yaml in pp_cfg column"
    param_name: "codedChromaIdc"
    param_type: "integer"
    required: False

  inputAlignmentExp:
    yaml_cfg: "inputAlignmentExp"
    description: "parameter from yaml in pp_cfg column"
    param_name: "inputAlignmentExp"
    param_type: "integer"
    required: False

  colorConversion:
    yaml_cfg: "colorConversion"
    description: "parameter from yaml in pp_cfg column"
    param_name: "colorConversion"
    param_type: "integer"
    required: False

  mirror:
    yaml_cfg: "mirror"
    description: "parameter from yaml in pp_cfg column"
    param_name: "mirror"
    param_type: "integer"
    required: False

  rotation:
    yaml_cfg: "rotation"
    description: "parameter from yaml in pp_cfg column"
    param_name: "rotation"
    param_type: "integer"
    required: False

  scanType:
    yaml_cfg: "scanType"
    description: "parameter from yaml in pp_cfg column"
    param_name: "scanType"
    param_type: "integer"
    required: False

  videoStab:
    yaml_cfg: "videoStab"
    description: "parameter from yaml in pp_cfg column"
    param_name: "videoStab"
    param_type: "integer"
    required: False

  beta_Offset:
    yaml_cfg: "beta_Offset"
    description: "parameter from yaml in bs_fmt_cfg column"
    param_name: "beta_Offset"
    param_type: "integer"
    required: False

  cabacInitFlag:
    yaml_cfg: "cabacInitFlag"
    description: "parameter from yaml in bs_fmt_cfg column"
    param_name: "cabacInitFlag"
    param_type: "integer"
    required: False

  codecFormat:
    yaml_cfg: "codecFormat"
    description: "parameter from yaml in bs_fmt_cfg column"
    param_name: "codecFormat"
    param_type: "integer"
    required: False

  level:
    yaml_cfg: "level"
    description: "parameter from yaml in bs_fmt_cfg column"
    param_name: "level"
    param_type: "integer"
    required: False

  POCConfig:
    yaml_cfg: "POCConfig"
    description: "parameter from yaml in bs_fmt_cfg column"
    param_name: "POCConfig"
    param_type: "string"
    required: False

  profile:
    yaml_cfg: "profile"
    description: "parameter from yaml in bs_fmt_cfg column"
    param_name: "profile"
    param_type: "integer"
    required: False

  sliceSize:
    yaml_cfg: "sliceSize"
    description: "parameter from yaml in bs_fmt_cfg column"
    param_name: "sliceSize"
    param_type: "integer"
    required: False

  tc_Offset:
    yaml_cfg: "tc_Offset"
    description: "parameter from yaml in bs_fmt_cfg column"
    param_name: "tc_Offset"
    param_type: "integer"
    required: False

  tile:
    yaml_cfg: "tile"
    description: "parameter from yaml in bs_fmt_cfg column"
    param_name: "tile"
    param_type: "string"
    required: False

  bitDepthChroma:
    yaml_cfg: "bitDepthChroma"
    description: "parameter from yaml in bs_fmt_cfg column"
    param_name: "bitDepthChroma"
    param_type: "integer"
    required: False

  bitDepthLuma:
    yaml_cfg: "bitDepthLuma"
    description: "parameter from yaml in bs_fmt_cfg column"
    param_name: "bitDepthLuma"
    param_type: "integer"
    required: False

  byteStream:
    yaml_cfg: "byteStream"
    description: "parameter from yaml in bs_fmt_cfg column"
    param_name: "byteStream"
    param_type: "integer"
    required: False

  constCb:
    yaml_cfg: "constCb"
    description: "parameter from yaml in bs_fmt_cfg column"
    param_name: "constCb"
    param_type: "integer"
    required: False

  constCr:
    yaml_cfg: "constCr"
    description: "parameter from yaml in bs_fmt_cfg column"
    param_name: "constCr"
    param_type: "integer"
    required: False

  crossTileRefRange:
    yaml_cfg: "crossTileRefRange"
    description: "parameter from yaml in bs_fmt_cfg column"
    param_name: "crossTileRefRange"
    param_type: "integer"
    required: False

  deblockOverride:
    yaml_cfg: "deblockOverride"
    description: "parameter from yaml in bs_fmt_cfg column"
    param_name: "deblockOverride"
    param_type: "integer"
    required: False

  disableDeblocking:
    yaml_cfg: "disableDeblocking"
    description: "parameter from yaml in bs_fmt_cfg column"
    param_name: "disableDeblocking"
    param_type: "integer"
    required: False

  enableCabac:
    yaml_cfg: "enableCabac"
    description: "parameter from yaml in bs_fmt_cfg column"
    param_name: "enableCabac"
    param_type: "integer"
    required: False

  enableConstChroma:
    yaml_cfg: "enableConstChroma"
    description: "parameter from yaml in bs_fmt_cfg column"
    param_name: "enableConstChroma"
    param_type: "integer"
    required: False

  enableDeblockOverride:
    yaml_cfg: "enableDeblockOverride"
    description: "parameter from yaml in bs_fmt_cfg column"
    param_name: "enableDeblockOverride"
    param_type: "integer"
    required: False

  enableSao:
    yaml_cfg: "enableSao"
    description: "parameter from yaml in bs_fmt_cfg column"
    param_name: "enableSao"
    param_type: "integer"
    required: False

  enableScalingList:
    yaml_cfg: "enableScalingList"
    description: "parameter from yaml in bs_fmt_cfg column"
    param_name: "enableScalingList"
    param_type: "integer"
    required: False

  enableTMVP:
    yaml_cfg: "enableTMVP"
    description: "parameter from yaml in bs_fmt_cfg column"
    param_name: "enableTMVP"
    param_type: "integer"
    required: False

  enableTS:
    yaml_cfg: "enableTS"
    description: "parameter from yaml in bs_fmt_cfg column"
    param_name: "enableTS"
    param_type: "integer"
    required: False

  enableTU32Mode:
    yaml_cfg: "enableTU32Mode"
    description: "parameter from yaml in bs_fmt_cfg column"
    param_name: "enableTU32Mode"
    param_type: "integer"
    required: False

  ivf:
    yaml_cfg: "ivf"
    description: "parameter from yaml in bs_fmt_cfg column"
    param_name: "ivf"
    param_type: "integer"
    required: False

  layerInRefIdc:
    yaml_cfg: "layerInRefIdc"
    description: "parameter from yaml in bs_fmt_cfg column"
    param_name: "layerInRefIdc"
    param_type: "integer"
    required: False

  log2MaxTSBlockSizeMinus2:
    yaml_cfg: "log2MaxTSBlockSizeMinus2"
    description: "parameter from yaml in bs_fmt_cfg column"
    param_name: "log2MaxTSBlockSizeMinus2"
    param_type: "integer"
    required: False

  prefixNalSvcFlag:
    yaml_cfg: "prefixNalSvcFlag"
    description: "parameter from yaml in bs_fmt_cfg column"
    param_name: "prefixNalSvcFlag"
    param_type: "integer"
    required: False

  psyFactor:
    yaml_cfg: "psyFactor"
    description: "parameter from yaml in bs_fmt_cfg column"
    param_name: "psyFactor"
    param_type: "string"
    required: False

  resendParamSet:
    yaml_cfg: "resendParamSet"
    description: "parameter from yaml in bs_fmt_cfg column"
    param_name: "resendParamSet"
    param_type: "integer"
    required: False

  RPSInSliceHeader:
    yaml_cfg: "RPSInSliceHeader"
    description: "parameter from yaml in bs_fmt_cfg column"
    param_name: "RPSInSliceHeader"
    param_type: "integer"
    required: False

  sendAUD:
    yaml_cfg: "sendAUD"
    description: "parameter from yaml in bs_fmt_cfg column"
    param_name: "sendAUD"
    param_type: "integer"
    required: False

  smoothingIntra:
    yaml_cfg: "smoothingIntra"
    description: "parameter from yaml in bs_fmt_cfg column"
    param_name: "smoothingIntra"
    param_type: "integer"
    required: False

  svctEnable:
    yaml_cfg: "svctEnable"
    description: "parameter from yaml in bs_fmt_cfg column"
    param_name: "svctEnable"
    param_type: "integer"
    required: False

  tier:
    yaml_cfg: "tier"
    description: "parameter from yaml in bs_fmt_cfg column"
    param_name: "tier"
    param_type: "integer"
    required: False

  tileMvConstraint:
    yaml_cfg: "tileMvConstraint"
    description: "parameter from yaml in bs_fmt_cfg column"
    param_name: "tileMvConstraint"
    param_type: "integer"
    required: False

  bFrameQpDelta:
    yaml_cfg: "bFrameQpDelta"
    description: "parameter from yaml in gop_cfg column"
    param_name: "bFrameQpDelta"
    param_type: "integer"
    required: False

  enableLeadingPictures:
    yaml_cfg: "enableLeadingPictures"
    description: "parameter from yaml in gop_cfg column"
    param_name: "enableLeadingPictures"
    param_type: "string"
    required: False

  gopConfig:
    yaml_cfg: "gopConfig"
    description: "parameter from yaml in gop_cfg column"
    param_name: "gopConfig"
    param_type: "string"
    required: False

  enablesubjAgop:
    yaml_cfg: "enablesubjAgop"
    description: "parameter from yaml in gop_cfg column"
    param_name: "enablesubjAgop"
    param_type: "integer"
    required: False

  gopLowdelay:
    yaml_cfg: "gopLowdelay"
    description: "parameter from yaml in gop_cfg column"
    param_name: "gopLowdelay"
    param_type: "integer"
    required: False

  gopMaxBSize:
    yaml_cfg: "gopMaxBSize"
    description: "parameter from yaml in gop_cfg column"
    param_name: "gopMaxBSize"
    param_type: "integer"
    required: False

  gopSize:
    yaml_cfg: "gopSize"
    description: "parameter from yaml in gop_cfg column"
    param_name: "gopSize"
    param_type: "integer"
    required: False

  lowdelayB:
    yaml_cfg: "lowdelayB"
    description: "parameter from yaml in gop_cfg column"
    param_name: "lowdelayB"
    param_type: "integer"
    required: False

  numRefP:
    yaml_cfg: "numRefP"
    description: "parameter from yaml in gop_cfg column"
    param_name: "numRefP"
    param_type: "integer"
    required: False

  refRingBufEnable:
    yaml_cfg: "refRingBufEnable"
    description: "parameter from yaml in gop_cfg column"
    param_name: "refRingBufEnable"
    param_type: "integer"
    required: False

  disableEOS:
    yaml_cfg: "disableEOS"
    description: "parameter from yaml in eos_cfg column"
    param_name: "disableEOS"
    param_type: "integer"
    required: False

  bTrailAvoidIntraBias:
    yaml_cfg: "bTrailAvoidIntraBias"
    description: "parameter from yaml in vq_cfg column"
    param_name: "bTrailAvoidIntraBias"
    param_type: "integer"
    required: False

  ctbRcTrailDeltaQp:
    yaml_cfg: "ctbRcTrailDeltaQp"
    description: "parameter from yaml in vq_cfg column"
    param_name: "ctbRcTrailDeltaQp"
    param_type: "integer"
    required: False

  IntraBiasChromaStrength:
    yaml_cfg: "IntraBiasChromaStrength"
    description: "parameter from yaml in vq_cfg column"
    param_name: "IntraBiasChromaStrength"
    param_type: "integer"
    required: False

  IntraBiasMvThreshold:
    yaml_cfg: "IntraBiasMvThreshold"
    description: "parameter from yaml in vq_cfg column"
    param_name: "IntraBiasMvThreshold"
    param_type: "integer"
    required: False

  IntraBiasStrength:
    yaml_cfg: "IntraBiasStrength"
    description: "parameter from yaml in vq_cfg column"
    param_name: "IntraBiasStrength"
    param_type: "integer"
    required: False

  lineDetectEnable:
    yaml_cfg: "lineDetectEnable"
    description: "parameter from yaml in vq_cfg column"
    param_name: "lineDetectEnable"
    param_type: "integer"
    required: False

  visualBitRateTolerance:
    yaml_cfg: "visualBitRateTolerance"
    description: "parameter from yaml in vq_cfg column"
    param_name: "visualBitRateTolerance"
    param_type: "integer"
    required: False

  ctbRcTrailStrengthMax:
    yaml_cfg: "ctbRcTrailStrengthMax"
    description: "parameter from yaml in vq_cfg column"
    param_name: "ctbRcTrailStrengthMax"
    param_type: "integer"
    required: False

  compressor:
    yaml_cfg: "compressor"
    description: "parameter from yaml in rfc_cfg column"
    param_name: "compressor"
    param_type: "integer"
    required: False

  enableP010Ref:
    yaml_cfg: "enableP010Ref"
    description: "parameter from yaml in rfc_cfg column"
    param_name: "enableP010Ref"
    param_type: "integer"
    required: False

  batchCount:
    yaml_cfg: "batchCount"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "batchCount"
    param_type: "string"
    required: False

  parallelCoreNum:
    yaml_cfg: "parallelCoreNum"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "parallelCoreNum"
    param_type: "integer"
    required: False

  batchEnable:
    yaml_cfg: "batchEnable"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "batchEnable"
    param_type: "integer"
    required: False

  multimode:
    yaml_cfg: "multimode"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "multimode"
    param_type: "integer"
    required: False

  aqInfoAlignmentExp:
    yaml_cfg: "aqInfoAlignmentExp"
    description: "parameter from yaml in alignment_cfg column"
    param_name: "aqInfoAlignmentExp"
    param_type: "integer"
    required: False

  inputAlignmentExp:
    yaml_cfg: "inputAlignmentExp"
    description: "parameter from yaml in alignment_cfg column"
    param_name: "inputAlignmentExp"
    param_type: "integer"
    required: False

  refAlignmentExp:
    yaml_cfg: "refAlignmentExp"
    description: "parameter from yaml in alignment_cfg column"
    param_name: "refAlignmentExp"
    param_type: "integer"
    required: False

  refChromaAlignmentExp:
    yaml_cfg: "refChromaAlignmentExp"
    description: "parameter from yaml in alignment_cfg column"
    param_name: "refChromaAlignmentExp"
    param_type: "integer"
    required: False

  tileStreamAlignmentExp:
    yaml_cfg: "tileStreamAlignmentExp"
    description: "parameter from yaml in alignment_cfg column"
    param_name: "tileStreamAlignmentExp"
    param_type: "integer"
    required: False

  comFile:
    yaml_cfg: "comFile"
    description: "parameter from yaml in bs_fmt_cfg column"
    param_name: "comFile"
    param_type: "string"
    required: False

  comLength:
    yaml_cfg: "comLength"
    description: "parameter from yaml in bs_fmt_cfg column"
    param_name: "comLength"
    param_type: "integer"
    required: False

  heightThumb:
    yaml_cfg: "heightThumb"
    description: "parameter from yaml in bs_fmt_cfg column"
    param_name: "heightThumb"
    param_type: "integer"
    required: False

  inputThumb:
    yaml_cfg: "inputThumb"
    description: "parameter from yaml in bs_fmt_cfg column"
    param_name: "inputThumb"
    param_type: "string"
    required: False

  qLevel:
    yaml_cfg: "qLevel"
    description: "parameter from yaml in bs_fmt_cfg column"
    param_name: "qLevel"
    param_type: "integer"
    required: False

  widthThumb:
    yaml_cfg: "widthThumb"
    description: "parameter from yaml in bs_fmt_cfg column"
    param_name: "widthThumb"
    param_type: "integer"
    required: False

  xdensity:
    yaml_cfg: "xdensity"
    description: "parameter from yaml in bs_fmt_cfg column"
    param_name: "xdensity"
    param_type: "integer"
    required: False

  ydensity:
    yaml_cfg: "ydensity"
    description: "parameter from yaml in bs_fmt_cfg column"
    param_name: "ydensity"
    param_type: "integer"
    required: False

  codingMode:
    yaml_cfg: "codingMode"
    description: "parameter from yaml in bs_fmt_cfg column"
    param_name: "codingMode"
    param_type: "integer"
    required: False

  codingType:
    yaml_cfg: "codingType"
    description: "parameter from yaml in bs_fmt_cfg column"
    param_name: "codingType"
    param_type: "integer"
    required: False

  markerType:
    yaml_cfg: "markerType"
    description: "parameter from yaml in bs_fmt_cfg column"
    param_name: "markerType"
    param_type: "integer"
    required: False

  quality:
    yaml_cfg: "quality"
    description: "parameter from yaml in bs_fmt_cfg column"
    param_name: "quality"
    param_type: "integer"
    required: False

  restartInterval:
    yaml_cfg: "restartInterval"
    description: "parameter from yaml in bs_fmt_cfg column"
    param_name: "restartInterval"
    param_type: "integer"
    required: False

  sramPowerdownDisable:
    yaml_cfg: "sramPowerdownDisable"
    description: "parameter from yaml in bs_fmt_cfg column"
    param_name: "sramPowerdownDisable"
    param_type: "integer"
    required: False

  sramPowerdownMode:
    yaml_cfg: "sramPowerdownMode"
    description: "parameter from yaml in bs_fmt_cfg column"
    param_name: "sramPowerdownMode"
    param_type: "integer"
    required: False

  thumbnail:
    yaml_cfg: "thumbnail"
    description: "parameter from yaml in bs_fmt_cfg column"
    param_name: "thumbnail"
    param_type: "integer"
    required: False

  units:
    yaml_cfg: "units"
    description: "parameter from yaml in bs_fmt_cfg column"
    param_name: "units"
    param_type: "integer"
    required: False

  AXIAlignment:
    yaml_cfg: "AXIAlignment"
    description: "parameter from yaml in axi_cfg column"
    param_name: "AXIAlignment"
    param_type: "integer"
    required: False

  burstMaxLength:
    yaml_cfg: "burstMaxLength"
    description: "parameter from yaml in axi_cfg column"
    param_name: "burstMaxLength"
    param_type: "integer"
    required: False

  trigger:
    yaml_cfg: "trigger"
    description: "parameter from yaml in debug_cfg column"
    param_name: "trigger"
    param_type: "integer"
    required: False

  XformCustomerPrivateFormat:
    yaml_cfg: "XformCustomerPrivateFormat"
    description: "parameter from yaml in debug_cfg column"
    param_name: "XformCustomerPrivateFormat"
    param_type: "integer"
    required: False

  core_mask:
    yaml_cfg: "core_mask"
    description: "parameter from yaml in debug_cfg column"
    param_name: "core_mask"
    param_type: "integer"
    required: False

  dumpRegister:
    yaml_cfg: "dumpRegister"
    description: "parameter from yaml in debug_cfg column"
    param_name: "dumpRegister"
    param_type: "integer"
    required: False

  logCheckMap:
    yaml_cfg: "logCheckMap"
    description: "parameter from yaml in debug_cfg column"
    param_name: "logCheckMap"
    param_type: "integer"
    required: False

  logOutDir:
    yaml_cfg: "logOutDir"
    description: "parameter from yaml in debug_cfg column"
    param_name: "logOutDir"
    param_type: "integer"
    required: False

  logOutLevel:
    yaml_cfg: "logOutLevel"
    description: "parameter from yaml in debug_cfg column"
    param_name: "logOutLevel"
    param_type: "integer"
    required: False

  logTraceMap:
    yaml_cfg: "logTraceMap"
    description: "parameter from yaml in debug_cfg column"
    param_name: "logTraceMap"
    param_type: "integer"
    required: False

  priority:
    yaml_cfg: "priority"
    description: "parameter from yaml in debug_cfg column"
    param_name: "priority"
    param_type: "integer"
    required: False

  rasterscan:
    yaml_cfg: "rasterscan"
    description: "parameter from yaml in debug_cfg column"
    param_name: "rasterscan"
    param_type: "integer"
    required: False

  verbose:
    yaml_cfg: "verbose"
    description: "parameter from yaml in debug_cfg column"
    param_name: "verbose"
    param_type: "integer"
    required: False

  insertIDRonFPSAdjust:
    yaml_cfg: "insertIDRonFPSAdjust"
    description: "parameter from yaml in frame_rate_cfg column"
    param_name: "insertIDRonFPSAdjust"
    param_type: "integer"
    required: False

  sendSPSonFPSAdjust:
    yaml_cfg: "sendSPSonFPSAdjust"
    description: "parameter from yaml in frame_rate_cfg column"
    param_name: "sendSPSonFPSAdjust"
    param_type: "integer"
    required: False

  HDR10_display:
    yaml_cfg: "HDR10_display"
    description: "parameter from yaml in hdr10_cfg column"
    param_name: "HDR10_display"
    param_type: "integer"
    required: False

  writeOnceHDR10:
    yaml_cfg: "writeOnceHDR10"
    description: "parameter from yaml in hdr10_cfg column"
    param_name: "writeOnceHDR10"
    param_type: "integer"
    required: False

  intraReconEnable:
    yaml_cfg: "intraReconEnable"
    description: "parameter from yaml in intraRecon_cfg column"
    param_name: "intraReconEnable"
    param_type: "integer"
    required: False

  irqTypeCutreeMask:
    yaml_cfg: "irqTypeCutreeMask"
    description: "parameter from yaml in irq_cfg column"
    param_name: "irqTypeCutreeMask"
    param_type: "integer"
    required: False

  irqTypeMask:
    yaml_cfg: "irqTypeMask"
    description: "parameter from yaml in irq_cfg column"
    param_name: "irqTypeMask"
    param_type: "integer"
    required: False

  inputLineBufferMode:
    yaml_cfg: "inputLineBufferMode"
    description: "parameter from yaml in lowlatency_cfg column"
    param_name: "inputLineBufferMode"
    param_type: "integer"
    required: False

  segmentUnitHeight:
    yaml_cfg: "segmentUnitHeight"
    description: "parameter from yaml in lowlatency_cfg column"
    param_name: "segmentUnitHeight"
    param_type: "integer"
    required: False

  inputSliceInfoEn:
    yaml_cfg: "inputSliceInfoEn"
    description: "parameter from yaml in lowlatency_cfg column"
    param_name: "inputSliceInfoEn"
    param_type: "integer"
    required: False
  
  gmvList1:
    yaml_cfg: "gmvList1"
    description: "parameter from yaml in me_cfg column"
    param_name: "gmvList1"
    param_type: "string"
    required: False

  gmv:
    yaml_cfg: "gmv"
    description: "parameter from yaml in me_cfg column"
    param_name: "gmv"
    param_type: "string"
    required: False

  MEVertRange:
    yaml_cfg: "MEVertRange"
    description: "parameter from yaml in me_cfg column"
    param_name: "MEVertRange"
    param_type: "integer"
    required: False

  mosaicEnables:
    yaml_cfg: "mosaicEnables"
    description: "parameter from yaml in mosaic_cfg column"
    param_name: "mosaicEnables"
    param_type: "integer"
    required: False

  mosSizeIndex:
    yaml_cfg: "mosSizeIndex"
    description: "parameter from yaml in mosaic_cfg column"
    param_name: "mosSizeIndex"
    param_type: "integer"
    required: False

  noiseFirstFrameSigma:
    yaml_cfg: "noiseFirstFrameSigma"
    description: "parameter from yaml in nr_cfg column"
    param_name: "noiseFirstFrameSigma"
    param_type: "integer"
    required: False

  noiseLow:
    yaml_cfg: "noiseLow"
    description: "parameter from yaml in nr_cfg column"
    param_name: "noiseLow"
    param_type: "integer"
    required: False

  noiseReductionStrength_InterU:
    yaml_cfg: "noiseReductionStrength_InterU"
    description: "parameter from yaml in nr_cfg column"
    param_name: "noiseReductionStrength_InterU"
    param_type: "integer"
    required: False

  noiseReductionStrength_InterV:
    yaml_cfg: "noiseReductionStrength_InterV"
    description: "parameter from yaml in nr_cfg column"
    param_name: "noiseReductionStrength_InterV"
    param_type: "integer"
    required: False

  noiseReductionStrength_InterY:
    yaml_cfg: "noiseReductionStrength_InterY"
    description: "parameter from yaml in nr_cfg column"
    param_name: "noiseReductionStrength_InterY"
    param_type: "integer"
    required: False

  noiseReductionStrength_IntraU:
    yaml_cfg: "noiseReductionStrength_IntraU"
    description: "parameter from yaml in nr_cfg column"
    param_name: "noiseReductionStrength_IntraU"
    param_type: "integer"
    required: False

  noiseReductionStrength_IntraV:
    yaml_cfg: "noiseReductionStrength_IntraV"
    description: "parameter from yaml in nr_cfg column"
    param_name: "noiseReductionStrength_IntraV"
    param_type: "integer"
    required: False

  noiseReductionStrength_IntraY:
    yaml_cfg: "noiseReductionStrength_IntraY"
    description: "parameter from yaml in nr_cfg column"
    param_name: "noiseReductionStrength_IntraY"
    param_type: "integer"
    required: False

  noiseReduction_ChromaMaxMV:
    yaml_cfg: "noiseReduction_ChromaMaxMV"
    description: "parameter from yaml in nr_cfg column"
    param_name: "noiseReduction_ChromaMaxMV"
    param_type: "integer"
    required: False

  noiseReductionEnable:
    yaml_cfg: "noiseReductionEnable"
    description: "parameter from yaml in nr_cfg column"
    param_name: "noiseReductionEnable"
    param_type: "integer"
    required: False

  olAlphaN:
    yaml_cfg: "olAlphaN"
    description: "parameter from yaml in osd_cfg column"
    param_name: "olAlphaN"
    param_type: "integer"
    required: False

  olBitmapUN:
    yaml_cfg: "olBitmapUN"
    description: "parameter from yaml in osd_cfg column"
    param_name: "olBitmapUN"
    param_type: "integer"
    required: False

  olBitmapVN:
    yaml_cfg: "olBitmapVN"
    description: "parameter from yaml in osd_cfg column"
    param_name: "olBitmapVN"
    param_type: "integer"
    required: False

  olBitmapYN:
    yaml_cfg: "olBitmapYN"
    description: "parameter from yaml in osd_cfg column"
    param_name: "olBitmapYN"
    param_type: "integer"
    required: False

  olCropHeightN:
    yaml_cfg: "olCropHeightN"
    description: "parameter from yaml in osd_cfg column"
    param_name: "olCropHeightN"
    param_type: "string"
    required: False

  olCropWidthN:
    yaml_cfg: "olCropWidthN"
    description: "parameter from yaml in osd_cfg column"
    param_name: "olCropWidthN"
    param_type: "string"
    required: False

  olCropXoffsetN:
    yaml_cfg: "olCropXoffsetN"
    description: "parameter from yaml in osd_cfg column"
    param_name: "olCropXoffsetN"
    param_type: "integer"
    required: False

  olCropYoffsetN:
    yaml_cfg: "olCropYoffsetN"
    description: "parameter from yaml in osd_cfg column"
    param_name: "olCropYoffsetN"
    param_type: "integer"
    required: False

  olFormatN:
    yaml_cfg: "olFormatN"
    description: "parameter from yaml in osd_cfg column"
    param_name: "olFormatN"
    param_type: "integer"
    required: False

  olHeightN:
    yaml_cfg: "olHeightN"
    description: "parameter from yaml in osd_cfg column"
    param_name: "olHeightN"
    param_type: "integer"
    required: False

  olInputN:
    yaml_cfg: "olInputN"
    description: "parameter from yaml in osd_cfg column"
    param_name: "olInputN"
    param_type: "string"
    required: False

  olScaleHeightN:
    yaml_cfg: "olScaleHeightN"
    description: "parameter from yaml in osd_cfg column"
    param_name: "olScaleHeightN"
    param_type: "integer"
    required: False

  olScaleWidthN:
    yaml_cfg: "olScaleWidthN"
    description: "parameter from yaml in osd_cfg column"
    param_name: "olScaleWidthN"
    param_type: "integer"
    required: False

  olWidthN:
    yaml_cfg: "olWidthN"
    description: "parameter from yaml in osd_cfg column"
    param_name: "olWidthN"
    param_type: "integer"
    required: False

  olXoffsetN:
    yaml_cfg: "olXoffsetN"
    description: "parameter from yaml in osd_cfg column"
    param_name: "olXoffsetN"
    param_type: "integer"
    required: False

  olYoffsetN:
    yaml_cfg: "olYoffsetN"
    description: "parameter from yaml in osd_cfg column"
    param_name: "olYoffsetN"
    param_type: "integer"
    required: False

  olYStrideN:
    yaml_cfg: "olYStrideN"
    description: "parameter from yaml in osd_cfg column"
    param_name: "olYStrideN"
    param_type: "string"
    required: False

  overlayEnables:
    yaml_cfg: "overlayEnables"
    description: "parameter from yaml in osd_cfg column"
    param_name: "overlayEnables"
    param_type: "integer"
    required: False

  olFormatN:
    yaml_cfg: "olFormatN"
    description: "parameter from yaml in osd_cfg column"
    param_name: "olFormatN"
    param_type: "integer"
    required: False

  olSuperTileN:
    yaml_cfg: "olSuperTileN"
    description: "parameter from yaml in osd_cfg column"
    param_name: "olSuperTileN"
    param_type: "integer"
    required: False

  osdMapAlphaN:
    yaml_cfg: "osdMapAlphaN"
    description: "parameter from yaml in osd_map_cfg column"
    param_name: "osdMapAlphaN"
    param_type: "integer"
    required: False

  osdMapBlockSize:
    yaml_cfg: "osdMapBlockSize"
    description: "parameter from yaml in osd_map_cfg column"
    param_name: "osdMapBlockSize"
    param_type: "integer"
    required: False

  osdMapEnable:
    yaml_cfg: "osdMapEnable"
    description: "parameter from yaml in osd_map_cfg column"
    param_name: "osdMapEnable"
    param_type: "integer"
    required: False

  osdMapInput:
    yaml_cfg: "osdMapInput"
    description: "parameter from yaml in osd_map_cfg column"
    param_name: "osdMapInput"
    param_type: "string"
    required: False

  osdMapStride:
    yaml_cfg: "osdMapStride"
    description: "parameter from yaml in osd_map_cfg column"
    param_name: "osdMapStride"
    param_type: "integer"
    required: False

  osdMapUN:
    yaml_cfg: "osdMapUN"
    description: "parameter from yaml in osd_map_cfg column"
    param_name: "osdMapUN"
    param_type: "integer"
    required: False

  osdMapVN:
    yaml_cfg: "osdMapVN"
    description: "parameter from yaml in osd_map_cfg column"
    param_name: "osdMapVN"
    param_type: "integer"
    required: False

  osdMapYN:
    yaml_cfg: "osdMapYN"
    description: "parameter from yaml in osd_map_cfg column"
    param_name: "osdMapYN"
    param_type: "integer"
    required: False

  aifQpDelta:
    yaml_cfg: "aifQpDelta"
    description: "parameter from yaml in rc_cfg column"
    param_name: "aifQpDelta"
    param_type: "integer"
    required: False

  bankLoanRatio:
    yaml_cfg: "bankLoanRatio"
    description: "parameter from yaml in rc_cfg column"
    param_name: "bankLoanRatio"
    param_type: "string"
    required: False

  bankLoanWindow:
    yaml_cfg: "bankLoanWindow"
    description: "parameter from yaml in rc_cfg column"
    param_name: "bankLoanWindow"
    param_type: "integer"
    required: False

  bitPerSecond:
    yaml_cfg: "bitPerSecond"
    description: "parameter from yaml in rc_cfg column"
    param_name: "bitPerSecond"
    param_type: "integer"
    required: False

  bitrateWindow:
    yaml_cfg: "bitrateWindow"
    description: "parameter from yaml in rc_cfg column"
    param_name: "bitrateWindow"
    param_type: "string"
    required: False

  bitVarRangeB:
    yaml_cfg: "bitVarRangeB"
    description: "parameter from yaml in rc_cfg column"
    param_name: "bitVarRangeB"
    param_type: "string"
    required: False

  bitVarRangeI:
    yaml_cfg: "bitVarRangeI"
    description: "parameter from yaml in rc_cfg column"
    param_name: "bitVarRangeI"
    param_type: "string"
    required: False

  bitVarRangeP:
    yaml_cfg: "bitVarRangeP"
    description: "parameter from yaml in rc_cfg column"
    param_name: "bitVarRangeP"
    param_type: "string"
    required: False

  changePos:
    yaml_cfg: "changePos"
    description: "parameter from yaml in rc_cfg column"
    param_name: "changePos"
    param_type: "integer"
    required: False

  chromaQpOffset:
    yaml_cfg: "chromaQpOffset"
    description: "parameter from yaml in rc_cfg column"
    param_name: "chromaQpOffset"
    param_type: "integer"
    required: False

  cpbMaxRate:
    yaml_cfg: "cpbMaxRate"
    description: "parameter from yaml in rc_cfg column"
    param_name: "cpbMaxRate"
    param_type: "integer"
    required: False

  ctbRcDirection:
    yaml_cfg: "ctbRcDirection"
    description: "parameter from yaml in rc_cfg column"
    param_name: "ctbRcDirection"
    param_type: "integer"
    required: False

  ctbRcRowQpDeltaRange:
    yaml_cfg: "ctbRcRowQpDeltaRange"
    description: "parameter from yaml in rc_cfg column"
    param_name: "ctbRcRowQpDeltaRange"
    param_type: "integer"
    required: False

  ctbRcSkinCbRange:
    yaml_cfg: "ctbRcSkinCbRange"
    description: "parameter from yaml in rc_cfg column"
    param_name: "ctbRcSkinCbRange"
    param_type: "string"
    required: False

  ctbRcSkinCrRange:
    yaml_cfg: "ctbRcSkinCrRange"
    description: "parameter from yaml in rc_cfg column"
    param_name: "ctbRcSkinCrRange"
    param_type: "string"
    required: False

  ctbRcSkinLumRange:
    yaml_cfg: "ctbRcSkinLumRange"
    description: "parameter from yaml in rc_cfg column"
    param_name: "ctbRcSkinLumRange"
    param_type: "string"
    required: False

  ctbRcSkinMinQPDelta:
    yaml_cfg: "ctbRcSkinMinQPDelta"
    description: "parameter from yaml in rc_cfg column"
    param_name: "ctbRcSkinMinQPDelta"
    param_type: "integer"
    required: False

  ctbRcSkinQPDelta:
    yaml_cfg: "ctbRcSkinQPDelta"
    description: "parameter from yaml in rc_cfg column"
    param_name: "ctbRcSkinQPDelta"
    param_type: "integer"
    required: False

  ctbRcThresholdB:
    yaml_cfg: "ctbRcThresholdB"
    description: "parameter from yaml in rc_cfg column"
    param_name: "ctbRcThresholdB"
    param_type: "string"
    required: False

  ctbRcThresholdI:
    yaml_cfg: "ctbRcThresholdI"
    description: "parameter from yaml in rc_cfg column"
    param_name: "ctbRcThresholdI"
    param_type: "string"
    required: False

  ctbRcThresholdP:
    yaml_cfg: "ctbRcThresholdP"
    description: "parameter from yaml in rc_cfg column"
    param_name: "ctbRcThresholdP"
    param_type: "string"
    required: False

  fixedIntraQp:
    yaml_cfg: "fixedIntraQp"
    description: "parameter from yaml in rc_cfg column"
    param_name: "fixedIntraQp"
    param_type: "integer"
    required: False

  gdrDuration:
    yaml_cfg: "gdrDuration"
    description: "parameter from yaml in rc_cfg column"
    param_name: "gdrDuration"
    param_type: "integer"
    required: False

  hieQpDeltaEnable:
    yaml_cfg: "hieQpDeltaEnable"
    description: "parameter from yaml in rc_cfg column"
    param_name: "hieQpDeltaEnable"
    param_type: "string"
    required: False

  intraQpDelta:
    yaml_cfg: "intraQpDelta"
    description: "parameter from yaml in rc_cfg column"
    param_name: "intraQpDelta"
    param_type: "integer"
    required: False

  lookaheadDepth:
    yaml_cfg: "lookaheadDepth"
    description: "parameter from yaml in rc_cfg column"
    param_name: "lookaheadDepth"
    param_type: "integer"
    required: False

  LTR:
    yaml_cfg: "LTR"
    description: "parameter from yaml in rc_cfg column"
    param_name: "LTR"
    param_type: "integer"
    required: False

  minIprop:
    yaml_cfg: "minIprop"
    description: "parameter from yaml in rc_cfg column"
    param_name: "minIprop"
    param_type: "integer"
    required: False

  monitorFrames:
    yaml_cfg: "monitorFrames"
    description: "parameter from yaml in rc_cfg column"
    param_name: "monitorFrames"
    param_type: "string"
    required: False

  frameRateDenom:
    yaml_cfg: "frameRateDenom"
    description: "parameter from yaml in rc_cfg column"
    param_name: "frameRateDenom"
    param_type: "integer"
    required: False

  frameRateNum:
    yaml_cfg: "frameRateNum"
    description: "parameter from yaml in rc_cfg column"
    param_name: "frameRateNum"
    param_type: "integer"
    required: False
    
  picQpDeltaRange:
    yaml_cfg: "picQpDeltaRange"
    description: "parameter from yaml in rc_cfg column"
    param_name: "picQpDeltaRange"
    param_type: "integer"
    required: False

  qpHdr:
    yaml_cfg: "qpHdr"
    description: "parameter from yaml in rc_cfg column"
    param_name: "qpHdr"
    param_type: "integer"
    required: False

  qpMaxI:
    yaml_cfg: "qpMaxI"
    description: "parameter from yaml in rc_cfg column"
    param_name: "qpMaxI"
    param_type: "integer"
    required: False

  qpMax:
    yaml_cfg: "qpMax"
    description: "parameter from yaml in rc_cfg column"
    param_name: "qpMax"
    param_type: "integer"
    required: False

  qpMinI:
    yaml_cfg: "qpMinI"
    description: "parameter from yaml in rc_cfg column"
    param_name: "qpMinI"
    param_type: "integer"
    required: False

  qpMin:
    yaml_cfg: "qpMin"
    description: "parameter from yaml in rc_cfg column"
    param_name: "qpMin"
    param_type: "integer"
    required: False

  rcBaseMBComplexity:
    yaml_cfg: "rcBaseMBComplexity"
    description: "parameter from yaml in rc_cfg column"
    param_name: "rcBaseMBComplexity"
    param_type: "integer"
    required: False

  rcQpDeltaRange:
    yaml_cfg: "rcQpDeltaRange"
    description: "parameter from yaml in rc_cfg column"
    param_name: "rcQpDeltaRange"
    param_type: "integer"
    required: False

  skipFramePOC:
    yaml_cfg: "skipFramePOC"
    description: "parameter from yaml in rc_cfg column"
    param_name: "skipFramePOC"
    param_type: "integer"
    required: False

  staticSceneIbitPercent:
    yaml_cfg: "staticSceneIbitPercent"
    description: "parameter from yaml in rc_cfg column"
    param_name: "staticSceneIbitPercent"
    param_type: "integer"
    required: False

  tolCtbRcInter:
    yaml_cfg: "tolCtbRcInter"
    description: "parameter from yaml in rc_cfg column"
    param_name: "tolCtbRcInter"
    param_type: "string"
    required: False

  tolCtbRcIntra:
    yaml_cfg: "tolCtbRcIntra"
    description: "parameter from yaml in rc_cfg column"
    param_name: "tolCtbRcIntra"
    param_type: "string"
    required: False

  tolMovingBitRate:
    yaml_cfg: "tolMovingBitRate"
    description: "parameter from yaml in rc_cfg column"
    param_name: "tolMovingBitRate"
    param_type: "integer"
    required: False

  tolRcUnderflow:
    yaml_cfg: "tolRcUnderflow"
    description: "parameter from yaml in rc_cfg column"
    param_name: "tolRcUnderflow"
    param_type: "integer"
    required: False

  tune:
    yaml_cfg: "tune"
    description: "parameter from yaml in rc_cfg column"
    param_name: "tune"
    param_type: "integer"
    required: False

  aifEnable:
    yaml_cfg: "aifEnable"
    description: "parameter from yaml in rc_cfg column"
    param_name: "aifEnable"
    param_type: "integer"
    required: False

  aq_mode:
    yaml_cfg: "aq_mode"
    description: "parameter from yaml in rc_cfg column"
    param_name: "aq_mode"
    param_type: "integer"
    required: False

  aq_strength:
    yaml_cfg: "aq_strength"
    description: "parameter from yaml in rc_cfg column"
    param_name: "aq_strength"
    param_type: "string"
    required: False

  blockRCSize:
    yaml_cfg: "blockRCSize"
    description: "parameter from yaml in rc_cfg column"
    param_name: "blockRCSize"
    param_type: "integer"
    required: False

  ctbRc:
    yaml_cfg: "ctbRc"
    description: "parameter from yaml in rc_cfg column"
    param_name: "ctbRc"
    param_type: "integer"
    required: False

  ctbRcRowQpStep:
    yaml_cfg: "ctbRcRowQpStep"
    description: "parameter from yaml in rc_cfg column"
    param_name: "ctbRcRowQpStep"
    param_type: "integer"
    required: False

  fillerData:
    yaml_cfg: "fillerData"
    description: "parameter from yaml in rc_cfg column"
    param_name: "fillerData"
    param_type: "integer"
    required: False

  hrdConformance:
    yaml_cfg: "hrdConformance"
    description: "parameter from yaml in rc_cfg column"
    param_name: "hrdConformance"
    param_type: "integer"
    required: False

  inLoopDSRatio:
    yaml_cfg: "inLoopDSRatio"
    description: "parameter from yaml in rc_cfg column"
    param_name: "inLoopDSRatio"
    param_type: "integer"
    required: False

  intraPeriod:
    yaml_cfg: "intraPeriod"
    description: "parameter from yaml in rc_cfg column"
    param_name: "intraPeriod"
    param_type: "integer"
    required: False

  intraPicRate:
    yaml_cfg: "intraPicRate"
    description: "parameter from yaml in rc_cfg column"
    param_name: "intraPicRate"
    param_type: "integer"
    required: False

  picSkip:
    yaml_cfg: "picSkip"
    description: "parameter from yaml in rc_cfg column"
    param_name: "picSkip"
    param_type: "integer"
    required: False

  fixedQP:
    yaml_cfg: "fixedQP"
    description: "parameter from yaml in rc_cfg column"
    param_name: "fixedQP"
    param_type: "integer"
    required: False

  mjpeg:
    yaml_cfg: "mjpeg"
    description: "parameter from yaml in rc_cfg column"
    param_name: "mjpeg"
    param_type: "integer"
    required: False

  rcMode:
    yaml_cfg: "rcMode"
    description: "parameter from yaml in rc_cfg column"
    param_name: "rcMode"
    param_type: "string"
    required: False

  crf:
    yaml_cfg: "crf"
    description: "parameter from yaml in rc_cfg column"
    param_name: "crf"
    param_type: "integer"
    required: False

  picRc:
    yaml_cfg: "picRc"
    description: "parameter from yaml in rc_cfg column"
    param_name: "picRc"
    param_type: "integer"
    required: False

  vbr:
    yaml_cfg: "vbr"
    description: "parameter from yaml in rc_cfg column"
    param_name: "vbr"
    param_type: "integer"
    required: False

  chDistWeightCoeff:
    yaml_cfg: "chDistWeightCoeff"
    description: "parameter from yaml in rdo_cfg column"
    param_name: "chDistWeightCoeff"
    param_type: "string"
    required: False

  dynamicRdoCu16Bias:
    yaml_cfg: "dynamicRdoCu16Bias"
    description: "parameter from yaml in rdo_cfg column"
    param_name: "dynamicRdoCu16Bias"
    param_type: "integer"
    required: False

  dynamicRdoCu16Factor:
    yaml_cfg: "dynamicRdoCu16Factor"
    description: "parameter from yaml in rdo_cfg column"
    param_name: "dynamicRdoCu16Factor"
    param_type: "integer"
    required: False

  dynamicRdoCu32Bias:
    yaml_cfg: "dynamicRdoCu32Bias"
    description: "parameter from yaml in rdo_cfg column"
    param_name: "dynamicRdoCu32Bias"
    param_type: "integer"
    required: False

  dynamicRdoCu32Factor:
    yaml_cfg: "dynamicRdoCu32Factor"
    description: "parameter from yaml in rdo_cfg column"
    param_name: "dynamicRdoCu32Factor"
    param_type: "integer"
    required: False

  enableDynamicRdo:
    yaml_cfg: "enableDynamicRdo"
    description: "parameter from yaml in rdo_cfg column"
    param_name: "enableDynamicRdo"
    param_type: "integer"
    required: False

  enableRdoQuant:
    yaml_cfg: "enableRdoQuant"
    description: "parameter from yaml in rdo_cfg column"
    param_name: "enableRdoQuant"
    param_type: "integer"
    required: False

  rdoLevel:
    yaml_cfg: "rdoLevel"
    description: "parameter from yaml in rdo_cfg column"
    param_name: "rdoLevel"
    param_type: "integer"
    required: False

  cir:
    yaml_cfg: "cir"
    description: "parameter from yaml in roi_cfg column"
    param_name: "cir"
    param_type: "string"
    required: False

  roi1DeltaQp:
    yaml_cfg: "roi1DeltaQp"
    description: "parameter from yaml in roi_cfg column"
    param_name: "roi1DeltaQp"
    param_type: "integer"
    required: False

  roi1Qp:
    yaml_cfg: "roi1Qp"
    description: "parameter from yaml in roi_cfg column"
    param_name: "roi1Qp"
    param_type: "integer"
    required: False

  roi2DeltaQp:
    yaml_cfg: "roi2DeltaQp"
    description: "parameter from yaml in roi_cfg column"
    param_name: "roi2DeltaQp"
    param_type: "integer"
    required: False

  roi2Qp:
    yaml_cfg: "roi2Qp"
    description: "parameter from yaml in roi_cfg column"
    param_name: "roi2Qp"
    param_type: "integer"
    required: False

  roi3DeltaQp:
    yaml_cfg: "roi3DeltaQp"
    description: "parameter from yaml in roi_cfg column"
    param_name: "roi3DeltaQp"
    param_type: "integer"
    required: False

  roi3Qp:
    yaml_cfg: "roi3Qp"
    description: "parameter from yaml in roi_cfg column"
    param_name: "roi3Qp"
    param_type: "integer"
    required: False

  roi4DeltaQp:
    yaml_cfg: "roi4DeltaQp"
    description: "parameter from yaml in roi_cfg column"
    param_name: "roi4DeltaQp"
    param_type: "integer"
    required: False

  roi4Qp:
    yaml_cfg: "roi4Qp"
    description: "parameter from yaml in roi_cfg column"
    param_name: "roi4Qp"
    param_type: "integer"
    required: False

  roi5DeltaQp:
    yaml_cfg: "roi5DeltaQp"
    description: "parameter from yaml in roi_cfg column"
    param_name: "roi5DeltaQp"
    param_type: "integer"
    required: False

  roi5Qp:
    yaml_cfg: "roi5Qp"
    description: "parameter from yaml in roi_cfg column"
    param_name: "roi5Qp"
    param_type: "integer"
    required: False

  roi6DeltaQp:
    yaml_cfg: "roi6DeltaQp"
    description: "parameter from yaml in roi_cfg column"
    param_name: "roi6DeltaQp"
    param_type: "integer"
    required: False

  roi6Qp:
    yaml_cfg: "roi6Qp"
    description: "parameter from yaml in roi_cfg column"
    param_name: "roi6Qp"
    param_type: "integer"
    required: False

  roi7DeltaQp:
    yaml_cfg: "roi7DeltaQp"
    description: "parameter from yaml in roi_cfg column"
    param_name: "roi7DeltaQp"
    param_type: "integer"
    required: False

  roi7Qp:
    yaml_cfg: "roi7Qp"
    description: "parameter from yaml in roi_cfg column"
    param_name: "roi7Qp"
    param_type: "integer"
    required: False

  roi8DeltaQp:
    yaml_cfg: "roi8DeltaQp"
    description: "parameter from yaml in roi_cfg column"
    param_name: "roi8DeltaQp"
    param_type: "integer"
    required: False

  roi8Qp:
    yaml_cfg: "roi8Qp"
    description: "parameter from yaml in roi_cfg column"
    param_name: "roi8Qp"
    param_type: "integer"
    required: False

  ipcmFilterDisable:
    yaml_cfg: "ipcmFilterDisable"
    description: "parameter from yaml in roi_cfg column"
    param_name: "ipcmFilterDisable"
    param_type: "integer"
    required: False

  ipcmMapEnable:
    yaml_cfg: "ipcmMapEnable"
    description: "parameter from yaml in roi_cfg column"
    param_name: "ipcmMapEnable"
    param_type: "integer"
    required: False

  rdoqMapEnable:
    yaml_cfg: "rdoqMapEnable"
    description: "parameter from yaml in roi_cfg column"
    param_name: "rdoqMapEnable"
    param_type: "integer"
    required: False

  RoiCuCtrlVer:
    yaml_cfg: "RoiCuCtrlVer"
    description: "parameter from yaml in roi_cfg column"
    param_name: "RoiCuCtrlVer"
    param_type: "integer"
    required: False

  roiMapDeltaQpBlockUnit:
    yaml_cfg: "roiMapDeltaQpBlockUnit"
    description: "parameter from yaml in roi_cfg column"
    param_name: "roiMapDeltaQpBlockUnit"
    param_type: "integer"
    required: False

  roiMapDeltaQpEnable:
    yaml_cfg: "roiMapDeltaQpEnable"
    description: "parameter from yaml in roi_cfg column"
    param_name: "roiMapDeltaQpEnable"
    param_type: "integer"
    required: False

  RoiQpDeltaVer:
    yaml_cfg: "RoiQpDeltaVer"
    description: "parameter from yaml in roi_cfg column"
    param_name: "RoiQpDeltaVer"
    param_type: "integer"
    required: False

  skipMapBlockUnit:
    yaml_cfg: "skipMapBlockUnit"
    description: "parameter from yaml in roi_cfg column"
    param_name: "skipMapBlockUnit"
    param_type: "integer"
    required: False

  skipMapEnable:
    yaml_cfg: "skipMapEnable"
    description: "parameter from yaml in roi_cfg column"
    param_name: "skipMapEnable"
    param_type: "integer"
    required: False

  t35:
    yaml_cfg: "t35"
    description: "parameter from yaml in sei_cfg column"
    param_name: "t35"
    param_type: "string"
    required: False

  nonRoiFilter:
    yaml_cfg: "nonRoiFilter"
    description: "parameter from yaml in roi_cfg column"
    param_name: "nonRoiFilter"
    param_type: "string"
    required: False

  nonRoiLevel:
    yaml_cfg: "nonRoiLevel"
    description: "parameter from yaml in roi_cfg column"
    param_name: "nonRoiLevel"
    param_type: "integer"
    required: False

  roimapFile:
    yaml_cfg: "roimapFile"
    description: "parameter from yaml in roi_cfg column"
    param_name: "roimapFile"
    param_type: "string"
    required: False

  sei:
    yaml_cfg: "sei"
    description: "parameter from yaml in sei_cfg column"
    param_name: "sei"
    param_type: "integer"
    required: False

  cuInfoVersion:
    yaml_cfg: "cuInfoVersion"
    description: "parameter from yaml in statistics_cfg column"
    param_name: "cuInfoVersion"
    param_type: "integer"
    required: False

  enableFrameInfoVersion:
    yaml_cfg: "enableFrameInfoVersion"
    description: "parameter from yaml in statistics_cfg column"
    param_name: "enableFrameInfoVersion"
    param_type: "integer"
    required: False

  enableOutputCtbBits:
    yaml_cfg: "enableOutputCtbBits"
    description: "parameter from yaml in statistics_cfg column"
    param_name: "enableOutputCtbBits"
    param_type: "integer"
    required: False

  enableOutputCuInfo:
    yaml_cfg: "enableOutputCuInfo"
    description: "parameter from yaml in statistics_cfg column"
    param_name: "enableOutputCuInfo"
    param_type: "integer"
    required: False

  enableVuiTimingInfo:
    yaml_cfg: "enableVuiTimingInfo"
    description: "parameter from yaml in statistics_cfg column"
    param_name: "enableVuiTimingInfo"
    param_type: "integer"
    required: False

  hashtype:
    yaml_cfg: "hashtype"
    description: "parameter from yaml in statistics_cfg column"
    param_name: "hashtype"
    param_type: "integer"
    required: False

  psnr:
    yaml_cfg: "psnr"
    description: "parameter from yaml in statistics_cfg column"
    param_name: "psnr"
    param_type: "integer"
    required: False

  sse0Enable:
    yaml_cfg: "sse0Enable"
    description: "parameter from yaml in statistics_cfg column"
    param_name: "sse0Enable"
    param_type: "integer"
    required: False

  sse0Rect:
    yaml_cfg: "sse0Rect"
    description: "parameter from yaml in statistics_cfg column"
    param_name: "sse0Rect"
    param_type: "integer"
    required: False

  sse1Enable:
    yaml_cfg: "sse1Enable"
    description: "parameter from yaml in statistics_cfg column"
    param_name: "sse1Enable"
    param_type: "integer"
    required: False

  sse1Rect:
    yaml_cfg: "sse1Rect"
    description: "parameter from yaml in statistics_cfg column"
    param_name: "sse1Rect"
    param_type: "integer"
    required: False

  sse2Enable:
    yaml_cfg: "sse2Enable"
    description: "parameter from yaml in statistics_cfg column"
    param_name: "sse2Enable"
    param_type: "integer"
    required: False

  sse2Rect:
    yaml_cfg: "sse2Rect"
    description: "parameter from yaml in statistics_cfg column"
    param_name: "sse2Rect"
    param_type: "integer"
    required: False

  sse3Enable:
    yaml_cfg: "sse3Enable"
    description: "parameter from yaml in statistics_cfg column"
    param_name: "sse3Enable"
    param_type: "integer"
    required: False

  sse3Rect:
    yaml_cfg: "sse3Rect"
    description: "parameter from yaml in statistics_cfg column"
    param_name: "sse3Rect"
    param_type: "integer"
    required: False

  sse4Enable:
    yaml_cfg: "sse4Enable"
    description: "parameter from yaml in statistics_cfg column"
    param_name: "sse4Enable"
    param_type: "integer"
    required: False

  sse4Rect:
    yaml_cfg: "sse4Rect"
    description: "parameter from yaml in statistics_cfg column"
    param_name: "sse4Rect"
    param_type: "integer"
    required: False

  sse5Enable:
    yaml_cfg: "sse5Enable"
    description: "parameter from yaml in statistics_cfg column"
    param_name: "sse5Enable"
    param_type: "integer"
    required: False

  sse5Rect:
    yaml_cfg: "sse5Rect"
    description: "parameter from yaml in statistics_cfg column"
    param_name: "sse5Rect"
    param_type: "integer"
    required: False

  sse6Enable:
    yaml_cfg: "sse6Enable"
    description: "parameter from yaml in statistics_cfg column"
    param_name: "sse6Enable"
    param_type: "integer"
    required: False

  sse6Rect:
    yaml_cfg: "sse6Rect"
    description: "parameter from yaml in statistics_cfg column"
    param_name: "sse6Rect"
    param_type: "integer"
    required: False

  sse7Enable:
    yaml_cfg: "sse7Enable"
    description: "parameter from yaml in statistics_cfg column"
    param_name: "sse7Enable"
    param_type: "integer"
    required: False

  sse7Rect:
    yaml_cfg: "sse7Rect"
    description: "parameter from yaml in statistics_cfg column"
    param_name: "sse7Rect"
    param_type: "integer"
    required: False

  ssim:
    yaml_cfg: "ssim"
    description: "parameter from yaml in statistics_cfg column"
    param_name: "ssim"
    param_type: "integer"
    required: False

  vuiAspectRatio:
    yaml_cfg: "vuiAspectRatio"
    description: "parameter from yaml in vui_cfg column"
    param_name: "vuiAspectRatio"
    param_type: "string"
    required: False

  vuiColordescription:
    yaml_cfg: "vuiColordescription"
    description: "parameter from yaml in vui_cfg column"
    param_name: "vuiColordescription"
    param_type: "integer"
    required: False

  videoRange:
    yaml_cfg: "videoRange"
    description: "parameter from yaml in vui_cfg column"
    param_name: "videoRange"
    param_type: "integer"
    required: False

  vuiVideoFormat:
    yaml_cfg: "vuiVideoFormat"
    description: "parameter from yaml in vui_cfg column"
    param_name: "vuiVideoFormat"
    param_type: "integer"
    required: False

  vuiVideosignalPresent:
    yaml_cfg: "vuiVideosignalPresent"
    description: "parameter from yaml in vui_cfg column"
    param_name: "vuiVideosignalPresent"
    param_type: "integer"
    required: False

  olInput01:
    yaml_cfg: "olInput01"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olInput01"
    param_type: "string"
    required: False
    dir_type: "stream"

  olScaleHeight01:
    yaml_cfg: "olScaleHeight01"
    description: "parameter from yaml in osd_cfg column"
    param_name: "olScaleHeight01"
    param_type: "integer"
    required: False

  olScaleWidth01:
    yaml_cfg: "olScaleWidth01"
    description: "parameter from yaml in osd_cfg column"
    param_name: "olScaleWidth01"
    param_type: "integer"
    required: False

  olWidth01:
    yaml_cfg: "olWidth01"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olWidth01"
    param_type: "integer"
    required: False

  olHeight01:
    yaml_cfg: "olHeight01"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olHeight01"
    param_type: "integer"
    required: False

  olFormat01:
    yaml_cfg: "olFormat01"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olFormat01"
    param_type: "integer"
    required: False

  olAlpha01:
    yaml_cfg: "olAlpha01"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olAlpha01"
    param_type: "integer"
    required: False

  olCropXoffset01:
    yaml_cfg: "olCropXoffset01"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olCropXoffset01"
    param_type: "integer"
    required: False

  olCropYoffset01:
    yaml_cfg: "olCropYoffset01"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olCropYoffset01"
    param_type: "integer"
    required: False

  olCropWidth01:
    yaml_cfg: "olCropWidth01"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olCropWidth01"
    param_type: "integer"
    required: False

  olCropHeight01:
    yaml_cfg: "olCropHeight01"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olCropHeight01"
    param_type: "integer"
    required: False

  olXoffset01:
    yaml_cfg: "olXoffset01"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olXoffset01"
    param_type: "integer"
    required: False

  olYoffset01:
    yaml_cfg: "olYoffset01"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olYoffset01"
    param_type: "integer"
    required: False

  olInput02:
    yaml_cfg: "olInput02"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olInput02"
    param_type: "string"
    required: False
    dir_type: "stream"

  olWidth02:
    yaml_cfg: "olWidth02"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olWidth02"
    param_type: "integer"
    required: False

  olHeight02:
    yaml_cfg: "olHeight02"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olHeight02"
    param_type: "integer"
    required: False

  olFormat02:
    yaml_cfg: "olFormat02"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olFormat02"
    param_type: "integer"
    required: False

  olAlpha02:
    yaml_cfg: "olAlpha02"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olAlpha02"
    param_type: "integer"
    required: False

  olCropXoffset02:
    yaml_cfg: "olCropXoffset02"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olCropXoffset02"
    param_type: "integer"
    required: False

  olCropYoffset02:
    yaml_cfg: "olCropYoffset02"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olCropYoffset02"
    param_type: "integer"
    required: False

  olCropWidth02:
    yaml_cfg: "olCropWidth02"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olCropWidth02"
    param_type: "integer"
    required: False

  olCropHeight02:
    yaml_cfg: "olCropHeight02"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olCropHeight02"
    param_type: "integer"
    required: False

  olXoffset02:
    yaml_cfg: "olXoffset02"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olXoffset02"
    param_type: "integer"
    required: False

  olYoffset02:
    yaml_cfg: "olYoffset02"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olYoffset02"
    param_type: "integer"
    required: False

  olInput03:
    yaml_cfg: "olInput03"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olInput03"
    param_type: "string"
    required: False
    dir_type: "stream"

  olWidth03:
    yaml_cfg: "olWidth03"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olWidth03"
    param_type: "integer"
    required: False

  olHeight03:
    yaml_cfg: "olHeight03"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olHeight03"
    param_type: "integer"
    required: False

  olFormat03:
    yaml_cfg: "olFormat03"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olFormat03"
    param_type: "integer"
    required: False

  olAlpha03:
    yaml_cfg: "olAlpha03"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olAlpha03"
    param_type: "integer"
    required: False

  olCropXoffset03:
    yaml_cfg: "olCropXoffset03"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olCropXoffset03"
    param_type: "integer"
    required: False

  olCropYoffset03:
    yaml_cfg: "olCropYoffset03"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olCropYoffset03"
    param_type: "integer"
    required: False

  olCropWidth03:
    yaml_cfg: "olCropWidth03"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olCropWidth03"
    param_type: "integer"
    required: False

  olCropHeight03:
    yaml_cfg: "olCropHeight03"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olCropHeight03"
    param_type: "integer"
    required: False

  olXoffset03:
    yaml_cfg: "olXoffset03"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olXoffset03"
    param_type: "integer"
    required: False

  olYoffset03:
    yaml_cfg: "olYoffset03"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olYoffset03"
    param_type: "integer"
    required: False

  olInput04:
    yaml_cfg: "olInput04"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olInput04"
    param_type: "string"
    required: False
    dir_type: "stream"

  olWidth04:
    yaml_cfg: "olWidth04"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olWidth04"
    param_type: "integer"
    required: False

  olHeight04:
    yaml_cfg: "olHeight04"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olHeight04"
    param_type: "integer"
    required: False

  olFormat04:
    yaml_cfg: "olFormat04"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olFormat04"
    param_type: "integer"
    required: False

  olAlpha04:
    yaml_cfg: "olAlpha04"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olAlpha04"
    param_type: "integer"
    required: False

  olCropXoffset04:
    yaml_cfg: "olCropXoffset04"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olCropXoffset04"
    param_type: "integer"
    required: False

  olCropYoffset04:
    yaml_cfg: "olCropYoffset04"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olCropYoffset04"
    param_type: "integer"
    required: False

  olCropWidth04:
    yaml_cfg: "olCropWidth04"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olCropWidth04"
    param_type: "integer"
    required: False

  olCropHeight04:
    yaml_cfg: "olCropHeight04"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olCropHeight04"
    param_type: "integer"
    required: False

  olXoffset04:
    yaml_cfg: "olXoffset04"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olXoffset04"
    param_type: "integer"
    required: False

  olYoffset04:
    yaml_cfg: "olYoffset04"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olYoffset04"
    param_type: "integer"
    required: False

  olInput05:
    yaml_cfg: "olInput05"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olInput05"
    param_type: "string"
    required: False
    dir_type: "stream"

  olWidth05:
    yaml_cfg: "olWidth05"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olWidth05"
    param_type: "integer"
    required: False

  olHeight05:
    yaml_cfg: "olHeight05"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olHeight05"
    param_type: "integer"
    required: False

  olFormat05:
    yaml_cfg: "olFormat05"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olFormat05"
    param_type: "integer"
    required: False

  olAlpha05:
    yaml_cfg: "olAlpha05"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olAlpha05"
    param_type: "integer"
    required: False

  olCropXoffset05:
    yaml_cfg: "olCropXoffset05"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olCropXoffset05"
    param_type: "integer"
    required: False

  olCropYoffset05:
    yaml_cfg: "olCropYoffset05"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olCropYoffset05"
    param_type: "integer"
    required: False

  olCropWidth05:
    yaml_cfg: "olCropWidth05"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olCropWidth05"
    param_type: "integer"
    required: False

  olCropHeight05:
    yaml_cfg: "olCropHeight05"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olCropHeight05"
    param_type: "integer"
    required: False

  olXoffset05:
    yaml_cfg: "olXoffset05"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olXoffset05"
    param_type: "integer"
    required: False

  olYoffset05:
    yaml_cfg: "olYoffset05"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olYoffset05"
    param_type: "integer"
    required: False

  olInput06:
    yaml_cfg: "olInput06"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olInput06"
    param_type: "string"
    required: False
    dir_type: "stream"
  

  olWidth06:
    yaml_cfg: "olWidth06"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olWidth06"
    param_type: "integer"
    required: False

  olHeight06:
    yaml_cfg: "olHeight06"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olHeight06"
    param_type: "integer"
    required: False

  olFormat06:
    yaml_cfg: "olFormat06"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olFormat06"
    param_type: "integer"
    required: False

  olAlpha06:
    yaml_cfg: "olAlpha06"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olAlpha06"
    param_type: "integer"
    required: False

  olCropXoffset06:
    yaml_cfg: "olCropXoffset06"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olCropXoffset06"
    param_type: "integer"
    required: False

  olCropYoffset06:
    yaml_cfg: "olCropYoffset06"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olCropYoffset06"
    param_type: "integer"
    required: False

  olCropWidth06:
    yaml_cfg: "olCropWidth06"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olCropWidth06"
    param_type: "integer"
    required: False

  olCropHeight06:
    yaml_cfg: "olCropHeight06"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olCropHeight06"
    param_type: "integer"
    required: False

  olXoffset06:
    yaml_cfg: "olXoffset06"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olXoffset06"
    param_type: "integer"
    required: False

  olYoffset06:
    yaml_cfg: "olYoffset06"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olYoffset06"
    param_type: "integer"
    required: False

  olInput07:
    yaml_cfg: "olInput07"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olInput07"
    param_type: "string"
    required: False
    dir_type: "stream"

  olWidth07:
    yaml_cfg: "olWidth07"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olWidth07"
    param_type: "integer"
    required: False

  olHeight07:
    yaml_cfg: "olHeight07"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olHeight07"
    param_type: "integer"
    required: False

  olFormat07:
    yaml_cfg: "olFormat07"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olFormat07"
    param_type: "integer"
    required: False

  olAlpha07:
    yaml_cfg: "olAlpha07"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olAlpha07"
    param_type: "integer"
    required: False

  olCropXoffset07:
    yaml_cfg: "olCropXoffset07"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olCropXoffset07"
    param_type: "integer"
    required: False

  olCropYoffset07:
    yaml_cfg: "olCropYoffset07"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olCropYoffset07"
    param_type: "integer"
    required: False

  olCropWidth07:
    yaml_cfg: "olCropWidth07"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olCropWidth07"
    param_type: "integer"
    required: False

  olCropHeight07:
    yaml_cfg: "olCropHeight07"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olCropHeight07"
    param_type: "integer"
    required: False

  olXoffset07:
    yaml_cfg: "olXoffset07"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olXoffset07"
    param_type: "integer"
    required: False

  olYoffset07:
    yaml_cfg: "olYoffset07"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olYoffset07"
    param_type: "integer"
    required: False

  olInput08:
    yaml_cfg: "olInput08"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olInput08"
    param_type: "string"
    required: False
    dir_type: "stream"

  olWidth08:
    yaml_cfg: "olWidth08"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olWidth08"
    param_type: "integer"
    required: False

  olHeight08:
    yaml_cfg: "olHeight08"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olHeight08"
    param_type: "integer"
    required: False

  olFormat08:
    yaml_cfg: "olFormat08"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olFormat08"
    param_type: "integer"
    required: False

  olAlpha08:
    yaml_cfg: "olAlpha08"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olAlpha08"
    param_type: "integer"
    required: False

  olCropXoffset08:
    yaml_cfg: "olCropXoffset08"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olCropXoffset08"
    param_type: "integer"
    required: False

  olCropYoffset08:
    yaml_cfg: "olCropYoffset08"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olCropYoffset08"
    param_type: "integer"
    required: False

  olCropWidth08:
    yaml_cfg: "olCropWidth08"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olCropWidth08"
    param_type: "integer"
    required: False

  olCropHeight08:
    yaml_cfg: "olCropHeight08"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olCropHeight08"
    param_type: "integer"
    required: False

  olXoffset08:
    yaml_cfg: "olXoffset08"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olXoffset08"
    param_type: "integer"
    required: False

  olYoffset08:
    yaml_cfg: "olYoffset08"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olYoffset08"
    param_type: "integer"
    required: False

  olInput09:
    yaml_cfg: "olInput09"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olInput09"
    param_type: "string"
    required: False
    dir_type: "stream"

  olWidth09:
    yaml_cfg: "olWidth09"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olWidth09"
    param_type: "integer"
    required: False

  olHeight09:
    yaml_cfg: "olHeight09"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olHeight09"
    param_type: "integer"
    required: False

  olFormat09:
    yaml_cfg: "olFormat09"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olFormat09"
    param_type: "integer"
    required: False

  olAlpha09:
    yaml_cfg: "olAlpha09"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olAlpha09"
    param_type: "integer"
    required: False

  olCropXoffset09:
    yaml_cfg: "olCropXoffset09"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olCropXoffset09"
    param_type: "integer"
    required: False

  olCropYoffset09:
    yaml_cfg: "olCropYoffset09"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olCropYoffset09"
    param_type: "integer"
    required: False

  olCropWidth09:
    yaml_cfg: "olCropWidth09"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olCropWidth09"
    param_type: "integer"
    required: False

  olCropHeight09:
    yaml_cfg: "olCropHeight09"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olCropHeight09"
    param_type: "integer"
    required: False

  olXoffset09:
    yaml_cfg: "olXoffset09"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olXoffset09"
    param_type: "integer"
    required: False

  olYoffset09:
    yaml_cfg: "olYoffset09"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olYoffset09"
    param_type: "integer"
    required: False

  olInput10:
    yaml_cfg: "olInput10"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olInput10"
    param_type: "string"
    required: False
    dir_type: "stream"

  olWidth10:
    yaml_cfg: "olWidth10"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olWidth10"
    param_type: "integer"
    required: False

  olHeight10:
    yaml_cfg: "olHeight10"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olHeight10"
    param_type: "integer"
    required: False

  olFormat10:
    yaml_cfg: "olFormat10"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olFormat10"
    param_type: "integer"
    required: False

  olAlpha10:
    yaml_cfg: "olAlpha10"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olAlpha10"
    param_type: "integer"
    required: False

  olCropXoffset10:
    yaml_cfg: "olCropXoffset10"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olCropXoffset10"
    param_type: "integer"
    required: False

  olCropYoffset10:
    yaml_cfg: "olCropYoffset10"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olCropYoffset10"
    param_type: "integer"
    required: False

  olCropWidth10:
    yaml_cfg: "olCropWidth10"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olCropWidth10"
    param_type: "integer"
    required: False

  olCropHeight10:
    yaml_cfg: "olCropHeight10"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olCropHeight10"
    param_type: "integer"
    required: False

  olXoffset10:
    yaml_cfg: "olXoffset10"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olXoffset10"
    param_type: "integer"
    required: False

  olYoffset10:
    yaml_cfg: "olYoffset10"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olYoffset10"
    param_type: "integer"
    required: False

  olInput11:
    yaml_cfg: "olInput11"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olInput11"
    param_type: "string"
    required: False
    dir_type: "stream"

  olWidth11:
    yaml_cfg: "olWidth11"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olWidth11"
    param_type: "integer"
    required: False

  olHeight11:
    yaml_cfg: "olHeight11"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olHeight11"
    param_type: "integer"
    required: False

  olFormat11:
    yaml_cfg: "olFormat11"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olFormat11"
    param_type: "integer"
    required: False

  olAlpha11:
    yaml_cfg: "olAlpha11"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olAlpha11"
    param_type: "integer"
    required: False

  olCropXoffset11:
    yaml_cfg: "olCropXoffset11"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olCropXoffset11"
    param_type: "integer"
    required: False

  olCropYoffset11:
    yaml_cfg: "olCropYoffset11"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olCropYoffset11"
    param_type: "integer"
    required: False

  olCropWidth11:
    yaml_cfg: "olCropWidth11"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olCropWidth11"
    param_type: "integer"
    required: False

  olCropHeight11:
    yaml_cfg: "olCropHeight11"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olCropHeight11"
    param_type: "integer"
    required: False

  olXoffset11:
    yaml_cfg: "olXoffset11"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olXoffset11"
    param_type: "integer"
    required: False

  olYoffset11:
    yaml_cfg: "olYoffset11"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olYoffset11"
    param_type: "integer"
    required: False

  olInput12:
    yaml_cfg: "olInput12"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olInput12"
    param_type: "string"
    required: False
    dir_type: "stream"

  olWidth12:
    yaml_cfg: "olWidth12"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olWidth12"
    param_type: "integer"
    required: False

  olHeight12:
    yaml_cfg: "olHeight12"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olHeight12"
    param_type: "integer"
    required: False

  olFormat12:
    yaml_cfg: "olFormat12"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olFormat12"
    param_type: "integer"
    required: False

  olAlpha12:
    yaml_cfg: "olAlpha12"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olAlpha12"
    param_type: "integer"
    required: False

  olCropXoffset12:
    yaml_cfg: "olCropXoffset12"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olCropXoffset12"
    param_type: "integer"
    required: False

  olCropYoffset12:
    yaml_cfg: "olCropYoffset12"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olCropYoffset12"
    param_type: "integer"
    required: False

  olCropWidth12:
    yaml_cfg: "olCropWidth12"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olCropWidth12"
    param_type: "integer"
    required: False

  olCropHeight12:
    yaml_cfg: "olCropHeight12"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olCropHeight12"
    param_type: "integer"
    required: False

  olXoffset12:
    yaml_cfg: "olXoffset12"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olXoffset12"
    param_type: "integer"
    required: False

  olYoffset12:
    yaml_cfg: "olYoffset12"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olYoffset12"
    param_type: "integer"
    required: False

  olBitmapYN:
    yaml_cfg: "olBitmapYN"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olBitmapYN"
    param_type: "integer"
    required: False

  olBitmapUN:
    yaml_cfg: "olBitmapUN"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olBitmapUN"
    param_type: "integer"
    required: False

  olBitmapVN:
    yaml_cfg: "olBitmapVN"
    description: "parameter from yaml in parallel_cfg column"
    param_name: "olBitmapVN"
    param_type: "integer"
    required: False

# Parameter constraints
constraints: {}
