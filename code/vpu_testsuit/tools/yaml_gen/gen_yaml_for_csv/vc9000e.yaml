Encoder Input and Output:
  csv_column: 'io_cfg'
  input:
    description: The file from which the video encoder reads input video sequence.
      [input.yuv]
    csv_column: ''
    group: 'base'
    dependency: ''
    default: input.yuv
  output:
    description: The file to which the video encoder writes output stream. [stream.hevc]
    csv_column: ''
    group: 'base'
    dependency: ''
    default: stream.bin
  firstPic:
    description: The first picture to be encoded in the input file. [0]
    csv_column: ''
    group: 'base'
    dependency: ''
    default: 0
  lastPic:
    description: The last picture to be encoded in the input file. [100]
    csv_column: ''
    group: 'base'
    dependency: ''
    default: 20
  outReconFrame:
    description: Whether to output reconstructed frames. [1] 0 - do not output reconstructed
      frames. 1 - output reconstructed frames.
    csv_column: ''
    group: 'recon'
    dependency: 'base'
    default: 1
    range: [0, 1]
  inputRateNumer:
    description: 1..1048575 The numerator used to calculate the input frame rate.
      [30]
    csv_column: ''
    group: 'FrameRate'
    dependency: 'base'
    default: 30
    range: [1, 1048575]
    test_range: [30]
  inputRateDenom:
    description: 1..1048575 The denominator used to calculate the input frame rate.
      [1]
    csv_column: ''
    group: 'FrameRate'
    dependency: 'base'
    default: 1
    range: [1, 1048575]
    test_range: [1]
  outputRateNumer:
    description: 1..1048575 The numerator used to calculate the output frame rate.
      [Same as input]
    csv_column: ''
    group: 'FrameRate'
    dependency: 'base'
    default: '--inputRateNumer'
    range: [1, 1048575]
    test_range: ['--inputRateNumer']
  outputRateDenom:
    description: 1..1048575 The denominator used to calculate the output frame rate.
      [Same as input]
    csv_column: ''
    group: 'FrameRate'
    dependency: 'base'
    default: '--inputRateDenom'
    range: [1, 1048575]
    test_range: ['--inputRateDenom']
  writeReconToDDR:
    description: Whether to write reconstructed frames to DDR. [1] 0 - do not write
      reconstructed frames to DDR. 1 - write reconstructed frames to DDR. The option
      is valid only for I-frame only encoding.
    csv_column: ''
    group: 'recon'
    dependency: 'base'
    default: 1
    range: [0, 1]
  Input and Encoded Frame Resolutions and Cropping:
    csv_column: ''
    lumWidthSrc:
      description: The width of the input picture, in pixels. [176]
      csv_column: ''
      group: 'base'
      dependency: ''
      default: 176
    lumHeightSrc:
      description: The height of the input picture, in pixels. [144]
      csv_column: ''
      group: 'base'
      dependency: ''
      default: 144
    width:
      description: The width of the encoded picture, in pixels. [--lumWidthSrc]
      csv_column: ''
      group: 'vceInput'
      dependency: ''
      default: --lumWidthSrc
    height:
      description: The height of the encoded picture, in pixels. [--lumHeightSrc]
      csv_column: ''
      group: 'vceInput'
      dependency: ''
      default: --lumHeightSrc
    horOffsetSrc:
      description: The horizontal offset, in pixels, of the top-left corner of the
        encoded picture relative to the input picture. [0] The option value must be
        an even integer.
      csv_column: ''
      group: 'vceInput'
      dependency: ''
      default: 0
    verOffsetSrc:
      description: The vertical offset, in pixels, of the top-left corner of the encoded
        picture relative to the input picture. [0] The option value must be an even
        integer.
      csv_column: ''
      group: 'vceInput'
      dependency: ''
      default: 0
    inputFileList:
      description: 'The path to a file that contains a list of input files. The option
        is useful for testing resolution change. Supported sub-options in the file
        include: -i: the input file path -a: the start picture -b: the end picture
        -w: the picture width in pixels -h: the picture height in pixels -o: the path
        to the output file'
      csv_column: ''
      group: 'inputFileList'
      dependency: 'inputFileList'
      default: inputFileList.txt
Pre-Processing:
  csv_column: 'pp_cfg'
  inputFormat:
    description: The input color format. [0] 0 - YUV420P8b_Raster_A16N 1 - YUV420SP8b_Raster_A16N
      2 - YVU420SP8b_Raster_A16N 3 - YUV422YUYV8b_Raster_A16N 4 - YUV422UYVY8b_Raster_A16N
      5 - RGB565_Raster_A16N 6 - BGR565_Raster_A16N 7 - XRGB1555_Raster_A16N 8 - XBGR1555_Raster_A16N
      9 - XRGB4444_Raster_A16N 10 - XBGR4444_Raster_A16N 11 - XRGB8888_Raster_A16N
      12 - XBGR8888_Raster_A16N 13 - XRGB2101010_Raster_A16N 14 - XBGR2101010_Raster_A16N
      15 - YUV420P10bWL_Raster_A16N 16 - YUV420SP10bWH_Raster_A16N 17 - YUV420P10b_Raster_A16N
      18 - YUV420Y0L210b_Raster_A16N 19 - (Reserved) YUV420P8b_Tile32x32_A16N for
      HEVC (H.265) 20 - (Reserved) YUV420P8b_Tile16x16_A16N for H.264 (AVC) 21 - YUV420SP8b_YuvSp4x4_A16N
      22 - YVU420SP8b_YuvSp4x4_A16N 23 - YUV420SP10bWH_YuvSp4x4_A32N 24 - YUV420SP10bDWL_Raster_A16N
      25 - YUV422SP8b_Raster_A16N 26 - (Reserved) YUV420YVU8b_Tile64x4_A16N 27 - (Reserved)
      YUV420YUV8b_Tile64x4_A16N 28 - (Reserved) YUV420YUV10b_Tile32x4_A16N 29 - (Reserved)
      YUV420YUV10b_Tile48x4_A16N 30 - (Reserved) YUV420YVU10b_Tile48x4_A16N 31 - (Reserved)
      YUV420YVU8b_Tile128x2_A16N 32 - (Reserved) YUV420YUV8b_Tile128x2_A16N 33 - (Reserved)
      YUV420YUV10b_Tile96x2_A16N 34 - (Reserved) YUV420YVU10b_Tile96x2_A16N 35 - YUV420SP8b_YuvSp8x8_A64N
      36 - YUV420SP10bWH_YuvSp8x8_A128N 37 - YUV420PYVU8b_Raster_A16N 38 - (Reserved)
      YVU420SP8b_Tile64x2_A16N 39 - (Reserved) YVU420SP10b_Tile128x2_A16N 40 - RGB888_Raster_A16N
      41 - BGR888_Raster_A16N 42 - RBG888_Raster_A16N 43 - GBR888_Raster_A16N 44 -
      BRG888_Raster_A16N 45 - GRB888_Raster_A16N 46 - YVU422SP8b_Raster_A16N 47 -
      YUV444P8b_Raster_A16N (YCbCr 4:4:4 planar(I444)) 48 - YUV444XYUV8888_Raster_A16N
      49 - YUV444XYUV2101010_Raster_A16N 50 - RGBX8888_Raster_A16N 51 - BGRX8888_Raster_A16N
      52 - RGBX1010102_Raster_A16N 53 - BGRX1010102_Raster_A16N 54 - YVU420SP10bWH_Raster_A16N
      55 - Y8b_Raster_A16N (YCbCr 4:0:0 ) Monochrome 8-bit 56 - Y10bWL_Raster_A16N
      (YCbCr 4:0:0 ) Monochrome 10-bit occupies 2B [9:0] 57 - Y10bWH_Raster_A16N (YCbCr
      4:0:0 ) Monochrome 10-bit occupies 2B [15:6] 58 - YUV420SP10b_Raster_A16N 59
      - YUV422P10bWL_Raster_A16N 60 - Y8b_Tile8x8_A16N (YCbCr 4:0:0 ) Monochrome 8-bit
      61 - Y10bWH_Tile8x8_A16N (YCbCr 4:0:0 ) Monochrome 10-bit occupies 2B [15:6]
      62 - YUV422YVYU8b_Raster_A16N 63 - YUV422VYUY8b_Raster_A16N 64 - YVU420SP8b_YuvSp8x8_A64N
      65 - Y10b_Raster_A16N 66 - YUV422P8b_Raster_A16N 67 - ARGB8888_Raster_A16N 68
      - ARGB2101010_Raster_A16N For details about the color formats, see "Hantro VC9x00E
      Series Memory Buffer and Format Organization".
    csv_column: 'io_cfg'
    group: 'base'
    dependency: ''
    default: 0
    range: [0, 68]
    test_range: [0]
  colorConversion:
    description: The RGB-to-YUV color conversion type. [0] 0 - conversion to YUV values
      without any range change according to Rec. ITU-R BT.601. 1 - conversion to YUV
      values without any range change according to Rec. ITU-R BT.709. 2 - conversion
      using custom coefficients. 3 - conversion according to Rec. ITU-R BT.2020. 4
      - conversion from full range RGB to limited range YUV according to Rec. ITU-R
      BT.601. 5 - (just for test) conversion from RGB in range of (0~219) to limited
      range YUV according to Rec. ITU-R BT.601. 6 - conversion from full range RGB
      to limited range YUV according to Rec. ITU-R BT.709.
    csv_column: ''
    group: ''
    dependency: ''
    default: 0
    range: [0, 6]
  mirror:
    description: The mirroring mode to pre-process the input image. [0] 0 - do not
      mirror 1 - mirror
    csv_column: ''
    group: ''
    dependency: ''
    default: 0
    range: [0, 1]
  rotation:
    description: The rotation mode to pre-process the input image. [0] 0 - do not
      rotate 1 - rotate 90 degrees clockwise 2 - roatate 90 degrees counterclockwise
      3 - rotate 180 degrees clockwise
    csv_column: ''
    group: ''
    dependency: ''
    default: 0
    range: [0, 3]
  videoStab:
    description: Whether to enable video stabilization. [0] 0 - disable 1 - enable
      When this option is set to 1, the video stabilization module also performs scene
      change detection if the input picture resolution is the same as the encoded
      picture esolution.
    csv_column: ''
    group: ''
    dependency: ''
    default: 0
    range: [0, 1]
  scaledWidth:
    description: 0..width The width of the down-scaled picture. [0] The value must
      a multiple of 4. (not supported by TGU02)
    csv_column: ''
    group: ''
    dependency: ''
  scaledHeight:
    description: 0..height The height of the down-scaled picture. [0] The value must
      a multiple of 4.(not supported by TGU02)
    csv_column: ''
    group: ''
    dependency: ''
  scaledOutputFormat:
    description: The color format of the output down-scaled picture. [0] 0 - YUV422
      interleaved 1 - YUV420 semiplanar (NV12) (not supported by TGU02)
    csv_column: ''
    group: ''
    dependency: ''
  interlacedFrame:
    description: Whether the input frames are progressive or interlaced. [0] 0 - progressive
      frame input 1 - interlaced framed input (not supported by TGU02)
    csv_column: ''
    group: ''
    dependency: ''
  fieldOrder:
    description: The interlaced field order. [0] 0 - bottom field first 1 - top field
      first (not supported by TGU02)
    csv_column: ''
    group: ''
    dependency: ''
  codedChromaIdc:
    description: The chroma sampling modes relative to luma sampling. [1] 0 - 4:0:0
      for HEVC (H.265) and H.264 (AVC) only 1 - 4:2:0 2 - 4:2:2 (under development)
      3 - 4:4:4 for HEVC (H.265) only This field is used as the SPS syntax element
      chroma_format_idc specified in ITU-T Rec. H.265 and H.264.
    csv_column: ''
    group: ''
    dependency: ''
    default: 1
    range: [0, 3]
    test_range: [0, 1]
  scanType:
    description: The scan type of input image. [0] 0 - raster 1 - supertileX
    csv_column: ''
    group: ''
    dependency: ''
    default: 0
    range: [0, 1]
  Input Conversion to Customized Formats:
    csv_column: ''
    formatCustomizedType:
      description: -1..13 (For test use) Converts the input image to the specified
        custom format. The data after format conversion is fed to the video encoder
        as its input. [-1] An option value in range from 0 to 13 indicates a pre-defined
        custom format. If this option is set to -1, format converion before input
        to the encoder is disabled. If this option is specified, dividing the input
        image into multiple tile columns and rows is not supported.
      csv_column: ''
      group: ''
      dependency: ''
  DEC400 Compression Table (Tile Status):
    csv_column: 'dec400_cfg'
    dec400TableInput:
      description: The file from which the video encoder reads the DEC400 compression
        table for video layer input. [dec400CompTableinput.bin]
      csv_column: ''
      group: ''
      dependency: ''
      default: dec400CompTableinput.bin
    osdDec400TableInputN:
      description: The file from which the video encoder reads the DEC400 compression
        table for OSD input. [NULL]
      csv_column: ''
      group: ''
      dependency: ''
      default: 'NULL'
    dec400TileSize:
      description: 0 - default 1 - 128byte. 2 - 512byte. 3 - 256byte.
      csv_column: ''
      group: ''
      dependency: ''
      range: [0, 3]
    dec400TileMode:
      description: 0 - default.[0] 1 - TILE_8x8_x. 2 - TILE_8x8_y. 3 - TILE_16x4.
        4 - TILE_8x4. 5 - TILE_4x8. 6 - RASTER_16x4. 7 - TILE_64x4. 8 - TILE_32x4.
        9 - RASTER256x1. 10 - RASTER128x1. 11 - RASTER64x1. 12 - TILE_16x8. 13 - RASTER32x4.
        14 - RASTER32x1. 15 - RASTER16x1. 16 - TILE_8x4_s. 17 - TILE_16x4_s. 18 -
        TILE_32x4_s. 19 - TILE_32x8.
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
      range: [0, 19]
      test_range: [0]
    dec400DataAlignment:
      description: 0 - default 1 - 32byte. 2 - 64byte. 3 - 1byte. 4 - 16byte.
      csv_column: ''
      group: ''
      dependency: ''
      range: [0, 4]
    dec400RGBAX:
      description: 0 - ARGB.[0] 1 - XRGB.
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
      range: [0, 1]
    dec400RGBFormat:
      description: 0 - default 1 - RGB8. 2 - RGB10. 3 - RGB4. 4 - RGB1555. 5 - RGB565.
      csv_column: ''
      group: ''
      dependency: ''
      range: [0, 5]
    dec400TSHeaderEnable:
      description: Whether to process dec400 tile status header buffer.(128byte) 0
        - disable 1 - enable
      csv_column: ''
      group: ''
      dependency: ''
      range: [0, 1]
  UFBC Parameters:
    csv_column: 'ufbc_cfg'
    ufbcMode:
      description: The core mode of UFBC 0 - disable 1 - afbc (only support 32x8 pixels)
        - Version 1.0 2 - afbc (support 32x8 and 16x16 pixels) - Version 1.1 3 - dec400
        - Version 4.0 4 - pvric - Version 2.0 5 - afbc (support tile format) - Version
        1.2
      csv_column: ''
      group: ''
      dependency: ''
      range: [0, 5]
    ufbcYuvTrans:
      description: Whether to enable interal YUV transformation. [0] 0 - disable 1
        - enable
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
      range: [0, 1]
    ufbcHeaderTile:
      description: Is the header in tile format. [0] Can only be configured in Version
        1.2.(ufbcMode = 5) 0 - disable 1 - enable
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
      range: [0, 1]
    ufbcPayloadAlign:
      description: Payload address and length byte align. [0] Can only be configured
        in Version 1.2.(ufbcMode = 5) 0 - 16 byte 1 - 32 byte 2 - 64 byte 3 - 128
        byte
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
      range: [0, 3]
    ufbcHeaderAlign:
      description: Header address and length byte align. [0] Can only be configured
        in Version 1.2.(ufbcMode = 5) 0 - 16 byte 1 - 32 byte 2 - 64 byte 3 - 128
        byte
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
      range: [0, 3]
    ufbcBlockType:
      description: The size of the superblock. Version 1.x 0 - 32x8 pixels 1 - 16x16
        pixels Version 2.x 0 - 8x8 pixels 1 - 16x4 pixels 2 - 32x2 pixels
      csv_column: ''
      group: ''
      dependency: ''
      range: [0, 2]
    ufbcBlockSplit:
      description: Whether to enable the superblock split mode for version 1.x. 0
        - disable 1 - enable
      csv_column: ''
      group: ''
      dependency: ''
      range: [0, 1]
    ufbcConstantVal:
      description: The constant color for version 2.x.
      csv_column: ''
      group: ''
      dependency: ''
Stream Coding Tools:
  csv_column: ''
  Output Stream Format:
    csv_column: 'bs_fmt_cfg'
    codecFormat:
      description: The video codec standard. [0] 0 or hevc - HEVC (H.265) 1 or h264
        - H.264 (AVC) 2 or av1 - AV1 3 or vp9 - VP9
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
    profile:
      description: 'The stream profile. [-1] -1 - Adaptive profile HEVC (H.265) profiles:
        0 - Main profile 1 - Main Still Picture profile 2 - Main 10 profile 3 - Monochrome
        profile 4 - Monochrome 10 profile 5 - Main 10 Still Picture profile 6 - Main
        422 10 profile 7 - Main 444 10 profile 8 - SCC Main profile 9 - SCC 444 10
        profile H.264 (AVC) profiles: 9 - Baseline profile 10 - Main profile 11 -
        High profile 12 - High 10 profile AV1 profiles: 0 - Main profile VP9 profiles:
        0 - Main profile 2 - High profile'
      csv_column: ''
      group: ''
      dependency: ''
      default: -1
      range: [0, 12]
      test_range: [-1]
    level:
      description: 'The stream level. If the option is set to 0xFFFF, the encoder
        software automatically selects the level. HEVC (H.265) levels [180]: Levels
        up to level 5.1 High tier are supported in real-time. Levels 5.0 and 5.1 with
        resolutions up to 4096x2048 are supported with 4K@60FPS performance. Levels
        greater than level 5.1 are supported in non-realtime mode. Value - Level -
        Resolution - Main tier max bit rate - High tier max bit rate 30 - 1.0 - QCIF
        -128 kbps - N/A 60 - 2.0 - CIF - 1.5 Mbps - N/A 63 - 2.1 - Q720p - 3.0 Mbps
        - N/A 90 - 3.0 - QHD - 6.0 Mbps - N/A 93 - 3.1 - 1280x720 - 10.0 Mbps - N/A
        120 - 4.0 - 2Kx1080 - 12.0 Mbps - 30 Mbps 123 - 4.1 - 2Kx1080 - 20.0 Mbps
        - 50 Mbps 150 - 5.0 - 4096x2160 - 25.0 Mbps - 100 Mbps 153 - 5.1 - 4096x2160
        - 40.0 Mbps - 160 Mbps 156 - 5.2 - 4096x2160 - 60.0 Mbps - 240 Mbps 180 -
        6.0 - 8192x4320 - 60.0 Mbps - 240 Mbps 183 - 6.1 - 8192x4320 - 120.0 Mbps
        - 480 Mbps 186 - 6.2 - 8192x4320 - 240.0 Mbps - 800 Mbps H.264 levels [51]:
        10 - H264_LEVEL_1 99 - H264_LEVEL_1_b 11 - H264_LEVEL_1_1 12 - H264_LEVEL_1_2
        13 - H264_LEVEL_1_3 20 - H264_LEVEL_2 21 - H264_LEVEL_2_1 22 - H264_LEVEL_2_2
        30 - H264_LEVEL_3 31 - H264_LEVEL_3_1 32 - H264_LEVEL_3_2 40 - H264_LEVEL_4
        41 - H264_LEVEL_4_1 42 - H264_LEVEL_4_2 50 - H264_LEVEL_5 51 - H264_LEVEL_5_1
        52 - H264_LEVEL_5_2 60 - H264_LEVEL_6 61 - H264_LEVEL_6_1 62 - H264_LEVEL_6_2
        AV1 levels [13]: 0 - AV1_LEVEL_2.0 1 - AV1_LEVEL_2.1 2 - AV1_LEVEL_2.2 3 -
        AV1_LEVEL_2.3 4 - AV1_LEVEL_3.0 5 - AV1_LEVEL_3.1 6 - AV1_LEVEL_3.2 7 - AV1_LEVEL_3.3
        8 - AV1_LEVEL_4.0 9 - AV1_LEVEL_4.1 10 - AV1_LEVEL_4.2 11 - AV1_LEVEL_4.3
        12 - AV1_LEVEL_5.0 13 - AV1_LEVEL_5.1'
      csv_column: ''
      group: ''
      dependency: ''
      default: 180
      range: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 20, 21, 22, 30, 31, 32,
        40, 41, 42, 50, 51, 52, 60, 61, 62, 63, 90, 93, 99, 120, 123, 150, 153, 156,
        180, 183, 186]
      test_range: [180]
    tier:
      description: The stream tier for HEVC (H.265) or AV1. [0] 0 - Main tier 1 -
        High tier
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
      range: [0, 1]
    bitDepthLuma:
      description: The bit depth of luma samples in the encoded stream. [8] 8 - 8-bit
        luma samples 10 - 10-bit luma samples
      csv_column: ''
      group: ''
      dependency: ''
      default: 8
      range: [8, 10]
    bitDepthChroma:
      description: The bit depth of chroma samples in the encoded stream. [8] 8 -
        8-bit Chroma samples 10 - 10-bit Chroma samples The value of bitDepthChroma
        must be the same as that of bitDepthLuma.
      csv_column: ''
      group: ''
      dependency: ''
      default: 8
      range: [8, 10]
    byteStream:
      description: (Only for HEVC/H.264) The stream type. [1] 0 - NAL unit stream.
        NAL sizes are stored in the nal_sizes.txt file. 1 - Byte stream. For AV1,
        only the byte stream type is supported.
      csv_column: ''
      group: ''
      dependency: ''
      default: 1
      range: [0, 1]
    ivf:
      description: (For AV1/VP9 only) The IVF encapsulation. [1] 0 - OBU raw output
        1 - IVF output
      csv_column: ''
      group: ''
      dependency: ''
      default: 1
      range: [0, 1]
    sendAUD:
      description: Whether to enable the access unit delimiter (AUD). [0] 0 - do not
        send 1 - send
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
      range: [0, 1]
    smoothingIntra:
      description: (For HEVC only) Whether to enable normal or strong smoothing. [1]
        0 - normal 1 - strong
      csv_column: ''
      group: ''
      dependency: ''
      default: 1
      range: [0, 1]
    cabacInitFlag:
      description: (Obsoleted) The initialization value for CABAC. [0] The option
        value is fixed to 0.
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
    enableCabac:
      description: (Only for H.264) The entropy coding mode. [1] 0 - content-adaptie
        variable-length coding (CAVLC) 1 - content-based adaptive binary arithmetic
        coding (CABAC)
      csv_column: ''
      group: ''
      dependency: ''
      default: 1
      range: [0, 1]
    sliceSize:
      description: (For HEVC/H.264 only) 0..height/ctu_size The number of CTB or MB
        rows in each slice. [0] If the option is set to 0, the encoder encodes a picture
        in each slice.
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
    disableDeblocking:
      description: Whether to disable inloop de-blocking filters. [0] 0 - enable.
        This value provides better quality. 1 - disable.
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
      range: [0, 1]
    enableTS:
      description: Disable/Enable HEVC Transform Skip [0] 0 - disable hevc transform
        skip 1 - enable hevc transform skip
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
      range: [0, 1]
    log2MaxTSBlockSizeMinus2:
      description: 0..2 Max transform skip size minus 2 [0]
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
      range: [0, 2]
    enableSao:
      description: Whether to enable sample adaptive offset (SAO) for HEVC (H.265)
        or CDEF filter for AV1. [1] 0 - disable 1 - enable
      csv_column: ''
      group: ''
      dependency: ''
      default: 1
      range: [0, 1]
    tc_Offset:
      description: -6..6 The de-blocking parameter tc_offset for HEVC (H.265) or alpha_c0_offset
        for H.264 (AVC). [0]
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
      range: [-6, 6]
      test_range: [0]
    beta_Offset:
      description: -6..6 The de-blocking parameter beta_offset for HEVC (H.265) and
        H.264 (AVC). [0]
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
      range: [-6, 6]
      test_range: [0]
    enableDeblockOverride:
      description: (Only for HEVC) Whether to enable de-blocking override between
        frames. [0] 0 - disable 1 - enable
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
      range: [0, 1]
    deblockOverride:
      description: (Only for HEVC) Whether the frame has de-blocking override information
        in the slice header. [0] 0 - does not have 1 - has
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
      range: [0, 1]
    tile:
      description: The tile settings. [1:1:1] - a indicates the number of tile columns
        to divide the picture. - b indicates the number of tile rows to divide the
        picture. - c indicates whether to enable the loop filter across the tile boundary(only
        for hevc). Value 0 indicates to disable and value 1 indicates to enable. Tiling
        is enabled when a * b > 1.
      csv_column: ''
      group: ''
      dependency: ''
      ref_format: a:b:c
      default: '1:1:1'
    tileMvConstraint:
      description: Tile tile settings. Whether the mv can cross the tile edge [0].
        0 - The mv can cross the tile edge 1 - The mv can't cross the tile edge
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
      range: [0, 1]
    crossTileRefRange:
      description: Tile tile settings. The search range by which the horizontol MV
        can across the tile edge. [0] 0 - 64 pixels (1 CTB) 1 - 128 pixels (2 CTBs)
        2 - 192 pixels (3 CTBs) 3 - 256 pixels (4 CTBs)
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
      range: [0, 3]
    enableScalingList:
      description: (For HEVC only) Whether to use the average or default scaling list.
        [0] 0 - average scaling list 1 - default scaling list
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
      range: [0, 1]
    RPSInSliceHeader:
      description: (For HEVC only) Whether to encode RPS in the slice header. [0]
        0 - do not encode 1 - encode
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
      range: [0, 1]
    resendParamSet:
      description: Whether to re-send VPS, PPS, and SPS before each IDR frame. [0]
        0 - do not re-send 1 - re-send
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
      range: [0, 1]
    POCConfig:
      description: The POC settings. [0:16:12] - a indicates the method to encode
        POC in the slice header. - b indicates the number of bits used to encode POC
        in the slice header. - c indicates the number of bits used to encode frame_num
        in the slice header.
      csv_column: ''
      group: ''
      dependency: ''
      ref_format: a:b:c
      default: 0:16:12
    psyFactor:
      description: 0..4.0 The strength of psycho-visual encoding. <float> [0] If the
        option is set to 0, psycho-visual encoding is disabled.
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
      range: [0, '4.0']
    layerInRefIdc:
      description: (Only for H.264) Whether to enable layer information in the nal_ref_idc
        syntax element. 0 - disable. In this case, the value of nal_ref_idc can be
        0 or 1. [default] 1 - enable. In this case, the value of nal_ref_idc can be
        0 to 3.
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
      range: [0, 1]
    prefixNalSvcFlag:
      description: (Only for H.264) Whether to add prefix NAL units betwwen slices
        for temporal scalable video coding (SVC-T). [0] 0 - do not add 1 - add
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
      range: [0, 1]
    svctEnable:
      description: (Only for H.264) Whether to enable SVC-T. [0] 0 - disable 1 - enable
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
      range: [0, 1]
    enableTMVP:
      description: Whether to enable temporal motion vector prediction (TMVP) in inter
        prediction. 0 - disable 1 - enable The default value is 1 for HEVC, AV1, and
        VP9 if TMVP is supported, and 0 in other cases.
      csv_column: ''
      group: ''
      dependency: ''
      range: [0, 1]
    Heifcfg:
      description: The path to the HEIF configuration file. ( Only support codec format
        HEVC and AV1 )
      csv_column: ''
      group: ''
      dependency: ''
    enableTU32Mode:
      description: intra/inter TU32 enable control separately. [3] 0 - disable intraTU32
        && disable interTU32 1 - disable intraTU32 && enable interTU32 2 - enable
        intraTU32 && disable interTU32 3 - enable intraTU32 && enable interTU32
      csv_column: ''
      group: ''
      dependency: ''
      default: 3
      range: [0, 3]
    gridEnable:
      description: enable padding picSize to gridSize.
      csv_column: ''
      group: ''
      dependency: ''
    gridWidth:
      description: The width of heif grid.(need to align with 8 pixel)
      csv_column: ''
      group: ''
      dependency: ''
    gridHeight:
      description: The height of heif grid.(need to align with 8 pixel)
      csv_column: ''
      group: ''
      dependency: ''
    Constant Chroma:
      csv_column: ''
      enableConstChroma:
        description: Whether to encode the picture with constant U and V values. [0]
          0 - do not encode with a constant U or V value 1 - encode with a constant
          U or V value
        csv_column: ''
        group: ''
        dependency: ''
        default: 0
        range: [0, 1]
      constCb:
        description: The constant U value. The option value range for 8-bit encoding
          color space is from 0 to 255, inclusive. The default value is 128. The option
          value range for 10-bit encoding color space is from 0 to 1023, inclusive.
          The default value is 512. This option is valid only if enableConstChroma
          is set to 1.
        csv_column: ''
        group: ''
        dependency: ''
        range: [8, 10]
      constCr:
        description: The constant V value. The option value range for 8-bit encoding
          color space is from 0 to 255, inclusive. The default value is 128. The option
          value range for 10-bit encoding color space is from 0 to 1023, inclusive.
          The default value is 512. This option is valid only if enableConstChroma
          is set to 1.
        csv_column: ''
        group: ''
        dependency: ''
        range: [8, 10]
  GOP Structure:
    csv_column: 'gop_cfg'
    smoothPsnrInGOP:
      description: (Obsoleted) Whether to enable smooth PSNR for frames in a GOP.
        [0] 0 - disable 1 - enable This option is valid only if gopSize is set to
        1.
      csv_column: ''
      group: ''
      dependency: ''
    gopSize:
      description: 0..8 The size of the GOP structure. [0] If the option is set to
        0, the encoder adaptively selects a GOP structure size. If the option is set
        to a value from 1 to 8, the encoder uses a fixed GOP structure size with a
        pre-defined GOP structure.
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
      range: [0, 8]
      test_range: [0, 8]
    gopMaxBSize:
      description: 0..7[default -255, disable] The GOP max B frame number for AGOP
      csv_column: ''
      group: ''
      dependency: 'gopSize'
      default: -255
      range: [0, 7]
      test_range: [-255, 0, 1, 2, 3, 4, 5, 6, 7]
    gopConfig:
      description: 'The path to a custom GOP configuration file, which defines the
        GOP structure table. If the gopConfig option is not specified, the default
        configurations take effect. The GOP configuration file must include gopSize
        normal configuration lines. Each normal configuration line must contain the
        following fields in order: - FrmN: the ID of the frame, where N is an integer
        ranging from 1 to gopSize. In the configuration file, frames must be listed
        in decoding order. - Type: the slice type, which can be P, B, or nrefB. -
        POC: the display order of the frame within the GOP, ranging from 1 to gopSize.
        - QPoffset: the offset to be added to the QP parameter to obtain the final
        QP value used for the frame. - QPfactor: the weight used during rate distortion
        optimization. - TemporalId: the temporal layer ID. - num_ref_pics: the number
        of reference frames kept for the current frame. The reference frames can be
        used by either the current frame or a subsequent frame. - ref_pics: a list
        of num_ref_pics integers, each indicating the POC offset of the reference
        frame relative to the current frame or the LTR index. - used_by_cur: a list
        of num_ref_pics binaries, each indicating whether the corresponding frame
        in the ref_pics list is used as a reference for encoding of the current frame.
        Each special configuration line must contain the following fields in order:
        - Frame0: indicates that the current line contains special GOP configurations
        (for example, for long-term reference). - Type: the slice type, which can
        be either P, B, or I. [-255] If the value is not -255, it overrides the value
        specified in the normal configurations. - QPoffset: the offset to be added
        to the QP parameter to obtain the final QP value used for this frame. [-255]
        If the value is not -255, it overrides the value specified in the normal configurations.
        - QPfactor: the weight used during rate distortion optimization. [-255] If
        the value is not -255, it overrides the value specified in the normal configurations.
        - TemporalId: temporal layer ID. [-255] If the value is not -255, it overrides
        the value specified in the normal configurations. - num_ref_pics: the number
        of reference frames kept for the current frame. The reference frames can be
        used by the current frame or a subsequent frame. - ref_pics: a list of num_ref_pics
        integers, each indicating the POC offset of the reference frame relative to
        the current frame or the LTR index. - used_by_cur: a list of num_ref_pics
        binaries, each indicating whether the frame is used as a reference for encoding
        of the current frame. - LTR: the LTR index of the frame. Value 0 indicates
        a common frame that uses LTR. A value in range from 1 to VCENC_MAX_LT_REF_FRAMES
        indicates an LTR frame. - Offset: If LTR equals 0, the Offset field indicates
        the POC delta between the LTR frame and the first subsequent frame that uses
        the LTR frame as reference. If LTR falls in the range from 1 to VCENC_MAX_LT_REF_FRAMES,
        the Offset field indicates the POC delta between the first LTR frame and the
        first encoded frame. - Interval: If LTR equals 0, the Interval field indicates
        the POC delta between two adjacent frames that use the same LTR frame as reference.
        If LTR falls in the range from 1 to VCENC_MAX_LT_REF_FRAMES, the Interval
        field indicates the POC delta between two adjacent frames with the same LTR
        index.'
      csv_column: ''
      group: 'custom_gop'
      dependency: ''
      default: 'custom_gop.yaml'
    gopLowdelay:
      description: Whether to use the default low-delay GOP configuration. [0] 0 -
        do not use 1 - use
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
      range: [0, 1]
    flexRefs:
      description: The path to a custom reference description file. For details, see
        Section "Flexible Referenc Configuration" in "Hantro VC9x00E Video Encoder
        Test Bench User Manual".
      csv_column: ''
      group: ''
      dependency: ''
    numRefP:
      description: 1..2 The number of reference frames kept for each P frame. [1]
        Change the number of reference frames for predefined GOP structure P frame
        to use
      csv_column: ''
      group: ''
      dependency: ''
      default: 1
      range: [1, 2]
    lowdelayB:
      description: 0..1 Use lowdelay B frame to replace regular P frame 0 - turn off
        1 - to replace predefined GOP structure P frame Default 0 for GOP1, default
        1 for GOP size > 1 and 2pass
      csv_column: ''
      group: ''
      dependency: 'gopSize'
      range: [0, 1]
    bFrameQpDelta:
      description: -1..51 The QP delta of B-frames to the target QP. [-1] If the option
        is set to -1, it indicates that no QP delta is set.
      csv_column: ''
      group: ''
      dependency: ''
      default: -1
      range: [-1, 51]
      test_range: [-1]
    refRingBufEnable:
      description: Whether to enable ring buffer for reference frame store. [0] 0
        - disable 1 - enable The option is valid for P or I frame encoding only. Since
        only one reference frame is stored, some coding tools are invalid, such as
        lookahead, reEncode, B frame, two reference P frame, multi-core and parallel
        encoding, LTR.
      csv_column: ''
      group: ''
      dependency: 'gopSize'
      default: 0
      range: [0, 1]
    enableLeadingPictures:
      description: enable leading pictures (RASL/RADL) [NONE]. NONE - disable RADL
        - enable RADL RASL - enable RASL BOTH - enable RADL+RASL
      csv_column: ''
      group: ''
      dependency: ''
      default: NONE
    enablesubjAgop:
      description: 0 disable subjAGop. 1 enable subjAGop when gopSize = 0. [0]
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
      range: [0, 1]
  Rate Distortion Optimization:
    csv_column: 'rdo_cfg'
    preset:
      description: The pre-defined RDO scheme. A higher value leads to higher quality
        but worse performance, and vice versa. To use this option, explicitly claim
        it.
      csv_column: ''
      group: ''
      dependency: ''
    rdoLevel:
      description: The RDO level. [1] The option value range is from 1 to 3, inclusive.
        A higher value leads to higher quality but worse performance, and vice versa.
      csv_column: ''
      group: ''
      dependency: ''
      default: 1
      range: [1, 3]
    enableDynamicRdo:
      description: Whether to enable dynamic RDO level selection. [0] 0 - disable
        1 - enable
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
      range: [0, 1]
    dynamicRdoCu16Bias:
      description: The bias used for 16x16 CU cost calculation in dynamic RDO level
        selection. [3] The option value range is from 0 to 255, inclusive.
      csv_column: ''
      group: ''
      dependency: ''
      default: 3
    dynamicRdoCu16Factor:
      description: The factor used for 16x16 CU cost calculation in dynamic RDO level
        selection. [80] The option value range is from 0 to 255, inclusive.
      csv_column: ''
      group: ''
      dependency: ''
      default: 80
    dynamicRdoCu32Bias:
      description: The bias used for 32x32 CU cost calculation in dynamic RDO level
        selection. [2] The option value range is from 0 to 255, inclusive.
      csv_column: ''
      group: ''
      dependency: ''
      default: 2
    dynamicRdoCu32Factor:
      description: The factor used for 32x32 CU cost calculation in dynamic RDO level
        selection. [32] The option value range is from 0 to 255, inclusive.
      csv_column: ''
      group: ''
      dependency: ''
      default: 32
    enableRdoQuant:
      description: Whether to enable rate-distortion optimized quantization (RDOQ).
        0 - disable 1 - enable The default value is 0 if the hardware does not support
        RDOQ, and 1 if the hardware supports RDOQ.
      csv_column: ''
      group: ''
      dependency: ''
      range: [0, 1]
    chDistWeightCoeff:
      description: Optimize chroma objective quality by adjusting distortion through
        delta_qp. There are 7 coeff in total, coeff1 corresponds to deltaqp=0 and
        coeff7 corresponds to deltaqp=6. Each coeff value range is [4, 16]. Default
        coeff array is [4, 4, 4, 4, 4, 4, 4] Recommended array is [4, 5, 6, 6, 6,
        6, 6] and [4, 5, 6, 8, 10, 12, 16]
      csv_column: ''
      group: ''
      dependency: ''
      default: 4, 16
  VUI and SEI:
    csv_column: ''
    SEI:
      csv_column: 'sei_cfg'
      sei:
        description: (Only for HEVC/H.264) Whether to insert picture timing and buffering
          period SEI messages into the stream. [0] 0 - do not insert 1 - insert
        csv_column: ''
        group: ''
        dependency: ''
        default: 0
        range: [0, 1]
      userData:
        description: The path to an SEI user data file. The file is parsed and inserted
          as an SEI message before the first frame.
        csv_column: ''
        group: ''
        dependency: ''
      extSEI:
        description: The path to an external SEI data file. The file is parsed and
          inserted as SEI messages for each frame.
        csv_column: ''
        group: ''
        dependency: ''
      jsonHDR:
        description: The file with dynamic HDR10+ and/or Dolby Vision information.
          If this option is specified, add the HDR_HELPER_SUPPORT=y option when generating
          the executable binary.
        csv_column: ''
        group: ''
        dependency: ''
      t35:
        description: The country code and area code. [0:0] - a indicates the country
          code, in one byte. - b indicates the area code, in one byte. For details,
          see Annex A and B of ITU-T Rec. T.35.
        csv_column: ''
        group: ''
        dependency: ''
        ref_format: a:b
        default: 0:0
      payloadT35File:
        description: The path to a file that contains the T.35 payload data (t35_payload_bytes)
          in binary bytes.
        csv_column: ''
        group: ''
        dependency: ''
    VUI:
      csv_column: 'vui_cfg'
      vuiAspectRatio:
        description: 'The sample aspect ratio in the VUI. [0:0] - aspectratioWidth:
          The sample aspect ratio in horizontal direction, in an arbitrary unit. The
          value range is from 0 to 65535, inclusive. - aspectratioHeight: The sample
          aspect ratio in vertical direction, in the same arbitrary unit as aspectratioWidth.
          The value range is from 0 to 65535, inclusive. If either aspectratioWidth
          or scaledHeight is set to 0, it indicates that the sample aspect ratio is
          not specified.'
        csv_column: ''
        group: ''
        dependency: ''
        default: 0:0
      vuiVideosignalPresent:
        description: Whether to present the video signal type in the VUI. [0] 0 -
          do not present 1 - present
        csv_column: ''
        group: ''
        dependency: ''
        default: 0
        range: [0, 1]
      vuiVideoFormat:
        description: The video format in the VUI. [5] 0 - component 1 - PAL 2 - NTSC
          3 - SECAM 4 - MAC 5 - UNDEF
        csv_column: ''
        group: ''
        dependency: ''
        default: 5
        range: [0, 5]
      videoRange:
        description: The video signal sample range in the encoded stream. [0] 0 -
          Y samples range from 16 to 235 and UV samples range in from 16 to 240. 1
          - YUV samples range from 0 to 255.
        csv_column: ''
        group: ''
        dependency: ''
        default: 0
        range: [0, 1]
      vuiColordescription:
        description: 'The color description in the VUI. - primary: The index of chromaticity
          coordinates. [2] Value 2 indicates unspecified. The value range is from
          0 to 255, inclusive.For details, see Table E.3 in ITU-T Rec. H.265. - transfer:
          The reference of the opto-electronic transfer function of the source picture.
          [2] Value 1 indicates IT-R BT.709-6 Value 2 indicates unspecified. Value
          14 indicates ITU-R Rec. BT.2020-2. Value 18 indicates ARIB STD-B67. For
          details, see Table E.4 in ITU-T Rec. H.265. - matrix: The index of matrix
          coefficients used for deriving luma and chroma signals from green, blue,
          and red or Y, Z, and X primaries. [2] Value 2 indicates unspecified. The
          value range is from 0 to 255, inclusive. For details, see Table E.5 in ITU-T
          Rec. H.265.'
        csv_column: ''
        group: ''
        dependency: ''
        default: 2
        range: [709, 2020]
        test_range: [2]
    HDR10:
      csv_column: 'hdr10_cfg'
      writeOnceHDR10:
        description: Whether to write HDR10 information only before the first IDR
          frame. [0] 0 - write before each IDR frame. 1 - write only before the first
          IDR frame.
        csv_column: ''
        group: ''
        dependency: ''
        default: 0
        range: [0, 1]
      HDR10_display:
        description: 'The color volume SEI message of the mastering display. - dx0:
          the normalized X chromaticity coordinate of component 0. [0] The value range
          is from 0 to 50000, inclusive. - dy0: the normalized Y chromaticity coordinate
          of component 0. [0] The value range is from 0 to 50000, inclusive. - dx1:
          the normalized X chromaticity coordinate of component 1. [0] The value range
          is from 0 to 50000, inclusive. - dy1: the normalized Y chromaticity coordinate
          of component 1. [0] The value range is from 0 to 50000, inclusive. - dx2:
          the normalized X chromaticity coordinate of component 2. [0] The value range
          is from 0 to 50000, inclusive. - dy2: the normalized Y chromaticity coordinate
          of component 2. [0] The value range is from 0 to 50000, inclusive. - wx:
          the normalized X chromaticity coordinate of the white point. [0] The value
          range is from 0 to 50000, inclusive. - wy: the normalized Y chromaticity
          coordinate of the white point. [0] The value range is from 0 to 50000, inclusive.
          - max: the nominal maximum display luminance. [0] - min: the nominal minimum
          display luminance. [0]'
        csv_column: ''
        group: ''
        dependency: ''
        default: 0
      HDR10_lightlevel:
        description: 'The content light level information SEI message. - maxlevel:
          the maximum content light level. - avglevel: the maximum picture average
          light level.'
        csv_column: ''
        group: ''
        dependency: ''
  (Obsoleted) Noise Reduction:
    csv_column: 'nr_cfg'
    noiseReductionEnable:
      description: (Obsoleted) Whether to enable noise reduction (3DNR). [0] 0 - disable
        3DNR 1 - enable 3DNR
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
      range: [0, 1]
    noiseLow:
      description: (Obsoleted) The minimum noise value. [10] The option value range
        is from 1 to 30, inclusive.
      csv_column: ''
      group: ''
      dependency: ''
      default: 10
    noiseFirstFrameSigma:
      description: (Obsoleted) The noise estimation for start frames. [11] The option
        value range is from 1 to 30, inclusive.
      csv_column: ''
      group: ''
      dependency: ''
      default: 11
    noiseReductionStrength_IntraY:
      description: 0..31 denoise strength for intra Y channel [7]
      csv_column: ''
      group: ''
      dependency: ''
      default: 7
      range: [0, 31]
      test_range: [7]
    noiseReductionStrength_IntraU:
      description: 0..31 denoise strength for intra U channel [7]
      csv_column: ''
      group: ''
      dependency: ''
      default: 7
      range: [0, 31]
      test_range: [7]
    noiseReductionStrength_IntraV:
      description: 0..31 denoise strength for intra V channel [7]
      csv_column: ''
      group: ''
      dependency: ''
      default: 7
      range: [0, 31]
      test_range: [7]
    noiseReductionStrength_InterY:
      description: 0..31 denoise strength for inter Y channel [7]
      csv_column: ''
      group: ''
      dependency: ''
      default: 7
      range: [0, 31]
      test_range: [7]
    noiseReductionStrength_InterU:
      description: 0..31 denoise strength for inter U channel [7]
      csv_column: ''
      group: ''
      dependency: ''
      default: 7
      range: [0, 31]
      test_range: [7]
    noiseReductionStrength_InterV:
      description: 0..31 denoise strength for inter V channel [7]
      csv_column: ''
      group: ''
      dependency: ''
      default: 7
      range: [0, 31]
      test_range: [7]
    noiseReduction_ChromaMaxMV:
      description: 0..15 only denoise U/V with MV x/y <= ChromaMaxMV [4]
      csv_column: ''
      group: ''
      dependency: ''
      default: 4
      range: [0, 15]
      test_range: [4]
  (Obsoleted) Smart Background Detection:
    csv_column: 'sbd_cfg'
    smartConfig:
      description: (Obsoleted) The path to the custom configuration file for the smart
        algorithm.
      csv_column: ''
      group: ''
      dependency: ''
  End-of-Sequence Disabling:
    csv_column: 'eos_cfg'
    disableEOS:
      description: Whether to write end-of-sequence (EOS) bytes before stream is closed.
        [0] 0 - do not write 1 - write
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
      range: [0, 1]
  Parameters for Enable intraRecon:
    csv_column: ''
    intraReconEnable:
      description: Enable/disable intra recon when HW support. [1] forced to 0 when
        HW_INTRARECON not supported. 0 = Disable intraRecon. 1 = Enable intraRecon.
      csv_column: 'intraRecon_cfg'
      group: ''
      dependency: ''
      default: 1
    disableIntraReconTU4:
      description: Enable/disable intra recon TU4,  [0] supported when HW_INTRARECON
        supported and buildID != 0x2157 if supported, forced to 1 when intraReconEnable
        = 0. 1 = disable intraReconTU4. 0 = do not disable intraReconTU4.
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
  AV1 Verification for FFmpeg Framework:
    csv_column: 'av1_cfg'
    modifiedTileGroupSize:
      description: (obsoleted)Whether to modify the space size of OBU_TILE_GROUP coded
        by the hardware. [0] 0 - do not modify 1 - modify Use this opion for AV1 verifiction
        only when the FFmpeg framework is used.
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
      range: [0, 1]
  Re-Encoding:
    csv_column: ''
    reEncode:
      description: Whether to re-encode frames in exceptional scenarios such as output
        buffer overflow. [0] 0 - do not re-encode frames 1 - re-encode frames For
        example, when an output buffer overflows, it can be reallocated through a
        callback function. Then, if this option is set to 1, the encoder re-encodes
        the current frame with the same settings but the new  output buffer.
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
      range: [0, 1]
  Motion Estimation and Global Motion:
    csv_column: 'me_cfg'
    gmvFile:
      description: '(Reserved) The path to a file that contains ME search range offsets
        for reference list 0. In the file, the search offsets of each frame must be
        listed sequentially line by line. To set a frame-level offset, there should
        be only one pair of offsets per line for the frame. For example: (MVX, MVY)
        When SEARCH_RANGE_ROW_OFFSET_TEST is enabled, to set CTU-row-level offsets,
        there should be multiple offset pairs per line for all CTU rows. For example:
        (MVX0, MVY0) (MVX1, MVY1) ... Common delimiters such as white spaces, commas
        (,), and semicolons (;) are allowed between offsets.'
      csv_column: ''
      group: ''
      dependency: ''
    gmv:
      description: 'The frame-level ME search range offsets for reference list 0.
        [0:0] - MVX: the horizontal offset, in pixels. The value must be 64 aligned
        and within the range from -128 to +128, inclusive. - MVY: the vertical offset,
        in pixels. The value must be 16 aligned and within the range from -128 to
        +128, inclusive. If both gmvFile and gmv are specified, the settings of gmvFile
        take effect.'
      csv_column: ''
      group: ''
      dependency: ''
      ref_format: MVX:MVY
      default: 0:0
    gmvList1:
      description: '(Reserved) The frame level ME search range offsets for reference
        list 1. [0:0] - MVX: the horizontal offset, in pixels. The value must be 64
        aligned and within the range from -128 to +128, inclusive. - MVY: the vertical
        offset, in pixels. The value must be 16 aligned and within the range from
        -128 to +128, inclusive.'
      csv_column: ''
      group: ''
      dependency: ''
      ref_format: MVX:MVY
      default: 0:0
    gmvList1File:
      description: '(Reserved) The path to a file that contains ME search range offsets
        for reference list 1. In the file, the search offsets of each frame must be
        listed sequentially line by line. To set a frame-level offset, there should
        be only one pair of offsets per line for the frame. For example: (MVX, MVY)
        When SEARCH_RANGE_ROW_OFFSET_TEST is enabled, to set CTU-row-level offsets,
        there should be multiple offset pairs per line for all CTU rows. For example:
        (MVX0, MVY0) (MVX1, MVY1) ... Common delimiters such as parentheses (), white
        spaces, commas (,), and semicolons (;) are allowed between offsets.'
      csv_column: ''
      group: ''
      dependency: ''
    MEVertRange:
      description: The ME vertical search range, in pixels. [0] For HEVC (H.265) and
        AV1, valid option values include 0, 40, and 64. For H.264 (AVC), valid option
        values include 0, 24, 48, and 64. If the option is set to 0, the maximum supported
        search range specified through EWLHwConfig_t.meVertSearchRangeHEVC or EWLHwConfig_t.meVertSearchRangeHEVC
        is used. This option is valid only if the value of EWLHwConfig_t.meVertRangeProgramable
        is 1.
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
Rate Control:
  csv_column: 'rc_cfg'
  Rate Control Mode:
    csv_column: ''
    rcMode:
      description: The rate control mode. [0] 0 or cvbr - constrained variable bit
        rate (CVBR) mode The CVBR mode reduces encoded bits in slow-motion and simple
        scenes, and uses the reduced bits for complex scenes to improve overall quality.
      csv_column: ''
      group: 'rc_mode'
      dependency: ''
      default: cvbr
      range: [cvbr, cbr, vbr, abr, crf, cqp]
  Output Stream Bit Rate:
    csv_column: ''
    intraPicRate:
      description: The interval between two IDR frames. [0] The encoder forces a frame
        to be encoded as an IDR frame every N frames. If the option is set to 0, the
        encode does not force any frame to be encoded as an IDR.
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
      range: [0, 10000000]
      test_range: [0, 5, 30]
    intraPeriod:
      description: The interval between two non-IDR intra frames. [0] Starting from
        an IDR frame, the encoder forces a frame to be encoded as an intra frame every
        N frames.
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
      range: [0, 10000000]
      test_range: [0, 5, 30]
    bitPerSecond:
      description: The target bit rate for rate control, in bits per second. [1000000]
        The option value range is from 10000 to the maximum limited by the stream
        level, inclusive. The option value must be greater than or equal to 10000.
        If HRD is enabled, the maximum target bit rate is limited by the stream tier
        and level selected during encoder initialization. For detailed limitations,
        see ITU-T Rec. H.265.
      csv_column: ''
      group: ''
      dependency: ''
      default: 1000000
    tolMovingBitRate:
      description: The tolerance percentage of the maximum moving bit rate over the
        target bit rate. [100] The average bit rate of the monitored frames is limited
        to [bitPerSecond * (1 + tolMovingBitRate/100)]. The option value range is
        from 0 to 2000, inclusive.
      csv_column: ''
      group: ''
      dependency: ''
      default: 100
    monitorFrames:
      description: The number of frames to be monitored for calculating the moving
        bit rate. [MAX(outputRateNumer/outputRateDenom, 3)] The monitorFrames value
        range is from 3 to 120, inclusive.
      csv_column: ''
      group: ''
      dependency: ''
      default: MAX(outputRateNumer/outputRateDenom, 3)
    bitVarRangeI:
      description: (Obsoleted) The permitting variance percentage over average bits
        per I-frame from the target bit rate. [maxPicSizeI * 100 / bitPerPic - 100]
        The option value range is from 10 to 10000, inclusive.
      csv_column: ''
      group: ''
      dependency: ''
      default: maxPicSizeI * 100 / bitPerPic - 100
    bitVarRangeP:
      description: The permitting variance percentage over average bits per P-frame
        from the target bit rate. [maxPicSizeP * 100 / bitPerPic - 100] The option
        value range is from 10 to 10000, inclusive.
      csv_column: ''
      group: ''
      dependency: ''
      default: maxPicSizeP * 100 / bitPerPic - 100
    bitVarRangeB:
      description: The permitting variance percentage over average bits per B-frame
        from the target bit rate. [maxPicSizeB * 100 / bitPerPic - 100] The option
        value range is from 10 to 10000, inclusive.
      csv_column: ''
      group: ''
      dependency: ''
      default: maxPicSizeB * 100 / bitPerPic - 100
    staticSceneIbitPercent:
      description: The percentage of I-frame bits in static scenes. [80] The option
        value range is from 0 to 100, inclusive.
      csv_column: ''
      group: ''
      dependency: ''
      default: 80
    crf:
      description: The constant rate factor. [-1] The option value range is from -1
        to 51, inclusive. If the option is set to -1, the constant rate factor mode
        is disabled.
      csv_column: ''
      group: 'rc_mode'
      dependency: ''
      default: -1
    picRc:
      description: Whether to enable picture-level rate control to adjust QP between
        pictures. [0] when rcMode = CVBR/CBR/VBR/ABR, picRc will be set to 1. 0 -
        disable 1 - enable
      csv_column: ''
      group: 'rc_mode'
      dependency: ''
      default: 0
      range: [0, 1]
    tolRcUnderflow:
      description: '[0, 99] Tolerance percent of frame RC underflow bitrate [50] Percentage
        of underflow bitrate tolerance for better quality Save bits in simple scene
        for complex scene Higher the value, larger underflow may happen Worst underflow
        bitrate:TargetBits*(100-tolRcUnderflow)/100'
      csv_column: ''
      group: ''
      dependency: ''
      default: 50
      range: [100, 100]
    picQpDeltaRange:
      description: The QP delta range in picture-level rate control. - Min indicates
        the minimum QP delta in picture-level rate control. [-4] The value range is
        from -10 to -1, inclusive. - Max indicates the maximum QP delta in picture-level
        rate control. [4] The value range is from 1 to 10, inclusive. This QP delta
        range applies only to two adjacent frames of the same coding type. It does
        not apply if HRD overflow occurs.
      csv_column: ''
      group: ''
      dependency: ''
      default: -4
    fillerData:
      description: Whether to fill data when HRD is disabled. [0] 0 - do not fill
        1 - fill If this option is set to 1, cpbSize must be specified.
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
      range: [0, 1]
    hrdConformance:
      description: (Only for HEVC/H.264) Whether to enable HRD conformance checking.
        [0] 0 - disable 1 - enable If this option is set to 1, the encoder uses the
        HRD model to restrict the bit rate variance.
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
      range: [0, 1]
    cpbSize:
      description: The size of the CPB in bits. only for CBR and ABR with hrd. The
        default value for CBR is 2 * bitPerSecond. When HRD is enabled, each encoded
        frame cannot be larger than the specified size. By default, the video encoder
        uses the maximum size allowed by the stream level. Setting this field to 0
        restores the default value. A value of (2 * bitPerSecond) is recommended.
      csv_column: ''
      group: ''
      dependency: ''
    cpbMaxRate:
      description: The maximum bit rate of the CPB, in bits per second. [0]
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
    bitrateWindow:
      description: 1..300 The number of frames within which the rate control algorithm
        tries to achieve the target bit rate. [intraPicRate] The rate control algorithm
        allocates bits for each window and tries to match the target bit rate at the
        end of the window. Typical windows begin with an intra frame, which is not
        mandatory.
      csv_column: ''
      group: ''
      dependency: ''
      default: intraPicRate
      range: [1, 300]
      test_range: [intraPicRate]
    LTR:
      description: 'The long term reference (LTR) settings. - a: the POC delta between
        two LTR frames. - b: the POC delta between the LTR frame and the first subsequent
        frame that uses the LTR frame as reference. - c: the POC delta between two
        adjacent frames that use the same LTR frame as reference. - d: the QP delta
        for frames using LTR as reference. [0]'
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
    picSkip:
      description: Whether to allow skippint pictures for bit rate control. [0] 0
        - disallow 1 - allow
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
      range: [0, 1]
    qpHdr:
      description: -1..51 The default or initial picture-level QP value. [26] If this
        option is set to -1, the encoder calculates the default or initial QP according
        to the target bit rate.
      csv_column: ''
      group: ''
      dependency: ''
      default: 26
      range: [-1, 51]
      test_range: [26]
    qpMin:
      description: 0..51 The minimum QP for all slices. [0]
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
      range: [0, 51]
      test_range: [0]
    qpMax:
      description: 0..51 The maximum QP for all slices. [51]
      csv_column: ''
      group: ''
      dependency: ''
      default: 51
      range: [0, 51]
      test_range: [51]
    qpMinI:
      description: 0..51 The minimum QP for I slices. [0]
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
      range: [0, 51]
      test_range: [0]
    qpMaxI:
      description: 0..51 The maximum QP for I slices. [51]
      csv_column: ''
      group: ''
      dependency: ''
      default: 51
      range: [0, 51]
      test_range: [51]
    intraQpDelta:
      description: -51..51 The delta between the target QP and intra QP values. [-3]
      csv_column: ''
      group: ''
      dependency: ''
      default: -3
      range: [-51, 51]
      test_range: [-3]
    fixedIntraQp:
      description: 0..51 The fixed QP value for all intra frames. [0] If this option
        is set to 0, the QP value for intra frames is not fixed.
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
      range: [0, 51]
      test_range: [0]
    chromaQpOffset:
      description: -12..12 The chroma QP offset. [0]
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
      range: [-12, 12]
      test_range: [0]
    vbr:
      description: Whether to enable variable bit rate control based on the minimum
        QP allowed. [0]
      csv_column: ''
      group: 'rc_mode'
      dependency: ''
      default: 0
    sceneChange:
      description: The frames with scene changes. Set the option value in format Frame1:Frame2:...:Frame20.
        Separate frame numbers with colons (:). A maximum of 20 scene change frames
        can be specified.
      csv_column: ''
      group: ''
      dependency: ''
    gdrDuration:
      description: 'The number of the P frame to be refreshed as an I frame in gradual
        decoder refresh (GDR). [0] If the option is set to 0, GDR is disabled. NOTE:
        The starting point of GDR is the frame with type set to VCENC_INTRA_FRAME.
        intraArea and roi1Area are used to implement the GDR function. The GDR begin
        to work from the second IDR frame.'
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
    skipFramePOC:
      description: The POC of the frame to be force encoded as a skip frame. [0] If
        this option is set to 0, the encoder does not force enocde any frame as a
        skip frame.
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
    insertIDR:
      description: The picture_cnt of the frame to be force encoded as an IDR frame.
        If this option is set to 0, the encoder does not force enocde any frame as
        an IDR frame.
      csv_column: ''
      group: ''
      dependency: ''
    minIprop:
      description: 0..100  The minimum ratio of I-frame bits to P-frame bits (I/P
        ratio). [1]
      csv_column: ''
      group: ''
      dependency: ''
      default: 1
      range: [0, 100]
      test_range: [1]
    maxIprop:
      description: minIprop..100 The maximum ratio of I/P ratio.
      csv_column: ''
      group: ''
      dependency: ''
    changePos:
      description: 50..100 The ratio of the bit rate at the initialization of QP adjustment
        to the maximun bit rate. [90]
      csv_column: ''
      group: ''
      dependency: ''
      default: 90
      range: [50, 100]
      test_range: [90]
    hieQpDeltaEnable:
      description: '[0,1] Frame QP delta based on GOP hierarchical layer. Only available
        in single pass encoding When gopSize > 1 and picRc = 1, the default hieQpDeltaEnble=1
        otherwise, the default hieQpDeltaEnable = 0. 0 : Disable. 1 : Enable.'
      csv_column: ''
      group: ''
      dependency: ''
      default: 0,1
    aifEnable:
      description: 'Whether to enable anti-intra flicker (AIF). [0] 0 - disable 1
        - enable NOTE: AIF works only for 1-pass 8-bit encoding. It is not available
        for multi-task, GDR, or low-latency encoding or if the input is tiled.'
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
      range: [0, 1, 8]
    aifQpDelta:
      description: -51..51 The QP delta of AIF frames to their reference frames. [-5]
      csv_column: ''
      group: ''
      dependency: ''
      default: -5
      range: [-51, 51]
      test_range: [-5]
    bankLoanRatio:
      description: 'allowable loan ratio. 0 turn off loan.[0.2] rc->bankLoanMin =
        (-rcCalculate(bps, rc->outRateDenom, rc->outRateNum) * pRateCtrl->bankLoanRatio)
        * rc->bitrateWindow; Note: The smaller the bankloan ratio, the less bits can
        be borrowed. The larger bankloan ratio, the larger the risk of excessing bit
        rate and the amount of excessed bit rate.'
      csv_column: ''
      group: ''
      dependency: ''
      default: '0.2'
    bankLoanWindow:
      description: The number of frames within loan window.[16]
      csv_column: ''
      group: ''
      dependency: ''
      default: 16
  Block-level rate control:
    csv_column: ''
    ctbRc:
      description: The block-level rate control mode for adjusting QP inside a frame.
        0 - block-level rate control disabled [default value except CBR mode ] 1 -
        subjective rate control only 2 - precise rate control only [default value
        for VCE_RC_CBR] This value is available only if the value of EWLHwConfig_t.CtbRcVersion
        is greater than or equal to 1. 3 - mixed subjective and precise rate control
        This value is available only if the value of EWLHwConfig_t.CtbRcVersion is
        greater than or equal to 1. 4 - (Reserved) precise rate control only 6 - (Reserved)
        mixed subjective and precise rate control
      csv_column: ''
      group: ''
      dependency: ''
      default: default value except CBR mode
      range: [0, 1, 2, 3, 4, 6]
    blockRCSize:
      description: The block size for block-level rate control 0 - 64x64 pixels [default
        value for ctbRcVersion 1] 1 - 32x32 pixels 2 - 16x16 pixels [default value
        for ctbRcVersion 2]
      csv_column: ''
      group: ''
      dependency: ''
      default: default value for ctbRcVersion 1
      range: [0, 2]
    rcQpDeltaRange:
      description: The maximum absolute delta between block-level and picture-level
        QP values in block-level rate control. [10] If the value of EWLHwConfig_t.CtbRcVersion
        is less than 1, the option value range is from 0 to 15, inclusive. If the
        value of EWLHwConfig_t.CtbRcVersion is greater than or equal to 1, the option
        value range is from 0 to 51, inclusive.
      csv_column: ''
      group: ''
      dependency: ''
      default: 10
    rcBaseMBComplexity:
      description: The MB complexity threshold for ctbRcV1 subjective block-level
        rate control. [15] When the MB complexity equals the threshold, the block-level
        QP is not adjusted. The QP value increases when the MB complexity exceeds
        the threshold, and vice versa. The option value range is from 0 to 31, inclusive.
      csv_column: ''
      group: ''
      dependency: ''
      default: 15
    tolCtbRcInter:
      description: 'The tolerance of block-level rate control for inter frames. [ctbRcv1:
        -1.0, ctbRcV2: 0.2] The rate control algorithm tries to limit inter frame
        bits within range from targetPicSize/(1 + tolCtbRcInter) to targetPicSize
        * (1 + tolCtbRcInter).'
      csv_column: ''
      group: ''
      dependency: ''
      default: 'ctbRcv1: -1.0, ctbRcV2: 0.2'
    tolCtbRcIntra:
      description: 'The tolerance of block-level rate control for intra frames. [ctbRcv1:
        -1.0, ctbRcV2: 19] The rate control algorithm tries to limit intra frame bits
        within range from targetPicSize/(1 + tolCtbRcIntra) to targetPicSize * (1
        + tolCtbRcIntra).'
      csv_column: ''
      group: ''
      dependency: ''
      default: 'ctbRcv1: -1.0, ctbRcV2: 19'
    ctbRcRowQpStep:
      description: 'The maximum accumulated QP adjustment step per CTB Row allowed
        by CTB rate control ctbrcv1:  0..x default value is [4] ctbrcv2:  0..4 and
        default [2]'
      csv_column: ''
      group: ''
      dependency: ''
      default: 4
      range: [0, 4]
    ctbRcRowQpDeltaRange:
      description: 0..31 Qp Delta based on picQP[1]. Only work when ctbrcV2
      csv_column: ''
      group: ''
      dependency: ''
      default: 1
      range: [0, 31]
      test_range: [1]
    ctbRcDirection:
      description: 0..15 Subscript of minus direction threshold. [8] Only work when
        ctbrcV2 It used with ctbRcThreshold to decrease/increase block QP. When the
        block complexity is between ctbRcThreshold[0] and ctbRcThreshold[Direction]
        will decrease QP, and the complexity is bigger than ctbRcThreshold[Direction]
        will increase QP. It is used for subjective block-level rate control.
      csv_column: ''
      group: ''
      dependency: ''
      default: 8
      range: [0, 15]
      test_range: [8]
    ctbRcThresholdI:
      description: 0..255 A threshold array, it used with ctbRcDirection to decrease/increase
        block QP of Intra frame. [0:0:0:0:3:3:5:5:6:6:6:11:11:13:17:17] It is used
        for subjective block-level rate control.
      csv_column: ''
      group: ''
      dependency: ''
      default: 0:0:0:0:3:3:5:5:6:6:6:11:11:13:17:17
    ctbRcThresholdP:
      description: 0..255 A threshold array, it used with ctbRcDirection to decrease/increase
        block QP of P frame. [0:0:0:0:3:3:5:5:6:6:6:11:11:13:17:17] It is used for
        subjective block-level rate control.
      csv_column: ''
      group: ''
      dependency: ''
      default: 0:0:0:0:3:3:5:5:6:6:6:11:11:13:17:17
    ctbRcThresholdB:
      description: 0..255 A threshold array, it used with ctbRcDirection to decrease/increase
        block QP of B frame. [0:0:0:0:3:3:5:5:6:6:6:11:11:13:17:17] It is used for
        subjective block-level rate control.
      csv_column: ''
      group: ''
      dependency: ''
      default: 0:0:0:0:3:3:5:5:6:6:6:11:11:13:17:17
    ctbRcSkinQPDelta:
      description: 0..7 Negative qp delta of skin area for subjective block-level
        rate control. [2]
      csv_column: ''
      group: ''
      dependency: ''
      default: 2
      range: [0, 7]
      test_range: [2]
    ctbRcSkinMinQPDelta:
      description: 0..15 It subtract ctbRcDirection value is the minimal qp delta
        of skin area for subjective block-level rate control. [3] Skin area not set
        too small QPDelta, so use ctbRcSkinMinQPDelta - direction to limit it. ctbRcSkinMinQPDelta
        - direction always less than 0.
      csv_column: ''
      group: ''
      dependency: ''
      default: 3
      range: [0, 15]
      test_range: [3]
    ctbRcSkinCbRange:
      description: a:b 0..255 Cb value range to be regarded as skin Area for subjective
        block-level rate control.[0:0] We use Skin area color range to detect skin
        Area, otherwize use default algorithm to detect skin area.
      csv_column: ''
      group: ''
      dependency: ''
      ref_format: a:b
      default: 0:0
    ctbRcSkinCrRange:
      description: a:b 0..255 Cr value range to be regarded as skin Area for subjective
        block-level rate control.[0:0]
      csv_column: ''
      group: ''
      dependency: ''
      ref_format: a:b
      default: 0:0
    ctbRcSkinLumRange:
      description: a:b 0..255 Lum value range to be regarded as skin Area for subjective
        block-level rate control.[0:255]
      csv_column: ''
      group: ''
      dependency: ''
      ref_format: a:b
      default: 0:255
  Look-Ahead Encoding:
    csv_column: ''
    lookaheadDepth:
      description: The number of look-ahead frames. [0] 0 - disables look-ahead encoding.
        4..40 - enables look-ahead encoding with the specified number of look-ahead
        frames.
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
      range: [4, 40]
      test_range: [0]
    halfDsInput:
      description: The path to the file that contains the external provided half-downsampled
        YUV input.
      csv_column: ''
      group: ''
      dependency: ''
    aq_mode:
      description: The adaptive quantization mode. [0] 0 - none 1 - uniform AQ 2 -
        auto variance 3 - auto variance with bias to dark scenes
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
      range: [0, 3]
    aq_strength:
      description: 0..3.0 The strength of adaptive quantization. [1.0] A great value
        reduces blocking and blurring in flat and textured areas.
      csv_column: ''
      group: ''
      dependency: ''
      default: '1.0'
      range: [0, '3.0']
    tune:
      description: The type of the target based on which some encoding settings are
        forced for quality tuing. [0]
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
    inLoopDSRatio:
      description: The in-loop down-scaling ratio for the pass-1 encoder. [1] 0 -
        down-scaling disabled 1 - 1/2 down-scaling ratio
      csv_column: ''
      group: ''
      dependency: ''
      default: 1
      range: [0, 1]
Visual Quality:
  csv_column: 'vq_cfg'
  Parameters Affecting Visual Tools:
    csv_column: ''
    ctbRcTrailStrengthMax:
      description: 0..3 Trailing area detection max strength. [0] 0 turns off trailing
        detection. Suggested value is [3] Only available with GOP size 1 and ctbRc
        Version=2
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
      range: [0, 3]
    ctbRcTrailDeltaQp:
      description: -7..0 Trailing area base qp delta. [-4] QP delta for 16x16 trailing
        CU, adjusted by CU size
      csv_column: ''
      group: ''
      dependency: ''
      default: -4
      range: [-7, 0]
      test_range: [-4]
    IntraBiasChromaStrength:
      description: 0..10 Chroma error detection strength. Default off; to activate
        this feature, the suggested value is 7 [0] 0 turns off chroma error detection.
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
      range: [0, 10]
      test_range: [0]
    IntraBiasStrength:
      description: 0..15 Strength of intra mode is preferred which will guarantee
        visual quality.  Default off; to activate this feature, the suggested value
        is 10 [0] 0 turns off intra bias.
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
      range: [0, 15]
      test_range: [0]
    IntraBiasMvThreshold:
      description: Int Mv limit for intra bias decision.[5] 0 no mv limit for intra
        bias decision.
      csv_column: ''
      group: ''
      dependency: ''
      default: 5
    bTrailAvoidIntraBias:
      description: Whether intraBias is disabled in the trailing area.[1] 0 enable
        intraBias in the trailing area. 1 disable intraBias in the trailing area.
      csv_column: ''
      group: ''
      dependency: ''
      default: 1
    visualBitRateTolerance:
      description: -1..1000 Percent of target bitrate tolerance allowed for visual
        tools to exceed. [50] -1 no limit for visual tools to spend extra bits Trail
        Reduce and Intra Bias will adjust strength at frame level adaptively to keep
        bitrate less than (1 + visualBitRateTolerance/100)*target bitrate Only available
        with GOP size 1
      csv_column: ''
      group: ''
      dependency: ''
      default: 50
      range: [-1, 1000]
      test_range: [50]
    lineDetectEnable:
      description: 0 turn off line detection. 1 turn on line detection.[0]
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
  Dynamic Frame Rate:
    csv_column: 'frame_rate_cfg'
    fpsAdjust:
      description: 'Frame rate update setting.(allow repeat) - frameCnt: at which
        frame to update - numer: updated frame rate numerator - denom: updated frame
        rate denominator - delay: issue update request <delay> frames in advance 0
        if ommitted.'
      csv_column: ''
      group: ''
      dependency: ''
    sendSPSonFPSAdjust:
      description: Send updated SPS on frame rate update. [1]
      csv_column: ''
      group: ''
      dependency: ''
      default: 1
    insertIDRonFPSAdjust:
      description: SPS on frame rate update. [1]
      csv_column: ''
      group: ''
      dependency: ''
      default: 1
ROI:
  csv_column: 'roi_cfg'
  ROI-Based Coding Control:
    csv_column: ''
    cir:
      description: 'Forces one MB or CTB to be encoded with intra mode at an  interval,
        starting from the specified CTB. [0:0] - start: the order count of the start
        MB or CTB. - interval: the interval.'
      csv_column: ''
      group: ''
      dependency: ''
      default: 0:0
    intraArea:
      description: 'The ROI for forcing the intra mode. - left: the leftmost MB or
        CTB column inside the ROI. - top: the top MB or CTB column inside the ROI.
        - right: the rightmost MB or CTB column inside the ROI. - bottom: the bottom
        MB or CTB column inside the ROI.'
      csv_column: ''
      group: ''
      dependency: ''
    ipcmFilterDisable:
      description: (For HEVC only) Whether to disable de-blocking and SAO filters
        for IPCM. 0 - Enables de-blocking and SAO filters around IPCM ROIs. 1 - Disables
        de-blocking and SAO filters for IPCM. This prevents the lossless content in
        the ROIs from being changed by the filters.
      csv_column: ''
      group: ''
      dependency: ''
      range: [0, 1]
    ipcm1Area:
      description: 'ROI 1 for forcing the IPCM mode. - left: the leftmost MB or CTB
        column inside the ROI. - top: the top MB or CTB column inside the ROI. - right:
        the rightmost MB or CTB column inside the ROI. - bottom: the bottom MB or
        CTB column inside the ROI.'
      csv_column: ''
      group: ''
      dependency: ''
    ipcm2Area:
      description: ROI 2 for forcing the IPCM mode.
      csv_column: ''
      group: ''
      dependency: ''
    ipcm3Area:
      description: ROI 3 for forcing the IPCM mode.
      csv_column: ''
      group: ''
      dependency: ''
    ipcm4Area:
      description: ROI 4 for forcing the IPCM mode.
      csv_column: ''
      group: ''
      dependency: ''
    ipcm5Area:
      description: ROI 5 for forcing the IPCM mode.
      csv_column: ''
      group: ''
      dependency: ''
    ipcm6Area:
      description: ROI 6 for forcing the IPCM mode.
      csv_column: ''
      group: ''
      dependency: ''
    ipcm7Area:
      description: ROI 7 for forcing the IPCM mode.
      csv_column: ''
      group: ''
      dependency: ''
    ipcm8Area:
      description: ROI 8 for forcing the IPCM mode.
      csv_column: ''
      group: ''
      dependency: ''
    ipcmMapEnable:
      description: Whether to enable the ROI map for forcing IPCM. [0] 0 - disable
        1 - enable
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
      range: [0, 1]
    ipcmMapFile:
      description: The path to the file that contains the IPCM map. The IPCM map defines
        the IPCM flag for each block in the frame. For the IPCM map, the block size
        is 64x64 pixels for HEVC (H.265) and 16x16 pixels for H.264 (AVC).
      csv_column: ''
      group: ''
      dependency: ''
    roi1Area:
      description: 'ROI 1 for forcing a QP delta or an absolute QP value. - left:
        the leftmost MB or CTB column inside the ROI. - top: the top MB or CTB column
        inside the ROI. - right: the rightmost MB or CTB column inside the ROI. -
        bottom: the bottom MB or CTB column inside the ROI.'
      csv_column: ''
      group: ''
      dependency: ''
    roi2Area:
      description: ROI 2 for forcing a QP delta or an absolute QP value.
      csv_column: ''
      group: ''
      dependency: ''
    roi3Area:
      description: ROI 3 for forcing a QP delta or an absolute QP value.
      csv_column: ''
      group: ''
      dependency: ''
    roi4Area:
      description: ROI 4 for forcing a QP delta or an absolute QP value.
      csv_column: ''
      group: ''
      dependency: ''
    roi5Area:
      description: ROI 5 for forcing a QP delta or an absolute QP value.
      csv_column: ''
      group: ''
      dependency: ''
    roi6Area:
      description: ROI 6 for forcing a QP delta or an absolute QP value.
      csv_column: ''
      group: ''
      dependency: ''
    roi7Area:
      description: ROI 7 for forcing a QP delta or an absolute QP value.
      csv_column: ''
      group: ''
      dependency: ''
    roi8Area:
      description: ROI 8 for forcing a QP delta or an absolute QP value.
      csv_column: ''
      group: ''
      dependency: ''
    roi1DeltaQp:
      description: The QP delta value for blocks in ROI 1. [0] If absolute QP is supported,
        the option value range is from -51 to 51, inclusive. If absolute QP is not
        supported, the option value range is from -30 to 0, inclusive. Set either
        the QP delta or absolute QP value for each ROI.
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
    roi2DeltaQp:
      description: The QP delta value for blocks in ROI 2. [0]
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
    roi3DeltaQp:
      description: The QP delta value for blocks in ROI 3. [0]
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
    roi4DeltaQp:
      description: The QP delta value for blocks in ROI 4. [0]
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
    roi5DeltaQp:
      description: The QP delta value for blocks in ROI 5. [0]
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
    roi6DeltaQp:
      description: The QP delta value for blocks in ROI 6. [0]
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
    roi7DeltaQp:
      description: The QP delta value for blocks in ROI 7. [0]
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
    roi8DeltaQp:
      description: The QP delta value for blocks in ROI 8. [0]
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
    roi1Qp:
      description: 0..51 The absolute QP value for blocks in ROI 1. [-1] Negative
        values are invalid.
      csv_column: ''
      group: ''
      dependency: ''
      default: -1
      range: [0, 51]
      test_range: [-1]
    roi2Qp:
      description: 0..51 The absolute QP value for blocks in ROI 2. [-1]
      csv_column: ''
      group: ''
      dependency: ''
      default: -1
      range: [0, 51]
      test_range: [-1]
    roi3Qp:
      description: 0..51 The absolute QP value for blocks in ROI 3. [-1]
      csv_column: ''
      group: ''
      dependency: ''
      default: -1
      range: [0, 51]
      test_range: [-1]
    roi4Qp:
      description: 0..51 The absolute QP value for blocks in ROI 4. [-1]
      csv_column: ''
      group: ''
      dependency: ''
      default: -1
      range: [0, 51]
      test_range: [-1]
    roi5Qp:
      description: 0..51 The absolute QP value for blocks in ROI 5. [-1]
      csv_column: ''
      group: ''
      dependency: ''
      default: -1
      range: [0, 51]
      test_range: [-1]
    roi6Qp:
      description: 0..51 The absolute QP value for blocks in ROI 6. [-1]
      csv_column: ''
      group: ''
      dependency: ''
      default: -1
      range: [0, 51]
      test_range: [-1]
    roi7Qp:
      description: 0..51 The absolute QP value for blocks in ROI 7. [-1]
      csv_column: ''
      group: ''
      dependency: ''
      default: -1
      range: [0, 51]
      test_range: [-1]
    roi8Qp:
      description: 0..51 The absolute QP value for blocks in ROI 8. [-1]
      csv_column: ''
      group: ''
      dependency: ''
      default: -1
      range: [0, 51]
      test_range: [-1]
    roiMapDeltaQpEnable:
      description: Whether to enable the ROI map for forcing QP delta or absolute
        QP values. [0] 0 - disable 1 - enable
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
      range: [0, 1]
    roiMapDeltaQpBlockUnit:
      description: The block size for the QP ROI map. [0] 0 - 64x64 pixels 1 - 32x32
        pixels 2 - 16x16 pixels 3 - 8x8 pixels For H.264 (AVC), 8x8 pixel block is
        not supported.
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
      range: [0, 3]
    roiMapConfigFile:
      description: The path to the ROI definition file.
      csv_column: ''
      group: ''
      dependency: ''
    roiMapDeltaQpFile:
      description: The path to the file that contains the QP map in text format. The
        QP map defines the delta QP or absolute QP value for each block in the frame.
      csv_column: ''
      group: ''
      dependency: ''
    roiMapInfoBinFile:
      description: The path to the file that contains the QP map in binary format.
        The QP map in binary format uses one byte to define the delta QP or absolute
        QP value for each block in the frame.
      csv_column: ''
      group: ''
      dependency: ''
    RoiQpDeltaVer:
      description: 1..4 The format version of the ROI map. This option determines
        the format used in the file specified
      csv_column: ''
      group: ''
      dependency: ''
      range: [1, 4]
    RoimapCuCtrlInfoBinFile:
      description: The path to the file that contains the CU control map in binary
        format. The CU control map defines the CU control information for each block
        in the frame. The block size is specified by
      csv_column: ''
      group: ''
      dependency: ''
    RoiCuCtrlVer:
      description: 0, 3..7 The format version of the CU control map. This option determines
        the format used in the file specified
      csv_column: ''
      group: ''
      dependency: ''
      range: [3, 7]
    roiMapDeltaQpBinFile:
      description: The path to the file that contains the QP map in binary format
        for pass-2 encoding. Each byte in the file indicates the delta QP of a block
        unit.
      csv_column: ''
      group: ''
      dependency: ''
    RoimapCuCtrlIndexBinFile:
      description: (Reserved) The path to the CU control map index binary file. The
        file specifies the ROIs for which the ROI information
      csv_column: ''
      group: ''
      dependency: ''
  Skip-Mode Map:
    csv_column: ''
    skipMapEnable:
      description: Whether to enable the skip-mode map. [0] 0 - disable 1 - enable
        To enable skip-mode map, you also need to specify
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
      range: [0, 1]
    skipMapBlockUnit:
      description: The block size for the skip-mode map. [0] 0 - 64x64 pixels 1 -
        32x32 pixels 2 - 16x16 pixels For HEVC (H.265), only 64x64 and 32x32 pixel
        blocks are supported.
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
      range: [0, 2]
    skipMapFile:
      description: The path to the file that contains the skip-mode map. The skip-mode
        map defines whether each block in the frame is force encoded in skip mode.
        - Value 0 indicates not to force encode the block in skip mode. - Value 1
        indicates to force encode the block in skip mode. The block width and height
        are calculated based on the picture width and height after alignment to 64
        pixels should be used.
      csv_column: ''
      group: ''
      dependency: ''
  RDOQ Map:
    csv_column: ''
    rdoqMapEnable:
      description: Whether to enable the RDOQ map. [0] 0 - disable 1 - enable
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
      range: [0, 1]
Coding Statistics Output:
  csv_column: 'statistics_cfg'
  enableOutputCuInfo:
    description: Whether to enable CU/MB statistics output to DDR. [0] 0 - disable
      1 - enable 2 - enable and write the statistics to the cuInfo.txt file.
    csv_column: ''
    group: ''
    dependency: ''
    default: 0
    range: [0, 2]
  cuInfoVersion:
    description: The CU/MB statistics format. [-1] -1 - format automatically decided
      based on software and hardware support.by VCE and HW support. 0 - format 0,
      which is available only if the value of EWLHwConfig_t.cuInforVersion is 0. 1
      - format 1, which is available if the value of EWLHwConfig_t.cuInforVersion
      is 1 or 2. 2 - format 2, which is available only if the value of EWLHwConfig_t.cuInforVersion
      is 2.
    csv_column: ''
    group: ''
    dependency: ''
    default: -1
    range: [0, 2]
  enableOutputCtbBits:
    description: Whether to enable bit statistics output to DDR. [0] 0 - disable.
      1 - enable with 2-byte data per CTB, in raster scan order of CTBs 2 - enable
      and write the statistics to the ctbBits.txt file.
    csv_column: ''
    group: ''
    dependency: ''
    default: 0
    range: [0, 2]
  hashtype:
    description: The method to calculate the hash value of the output stream. [0]
      0 - hash value calculation disabled 1 - standard CRC32 2 - 32-bit checksum
    csv_column: ''
    group: ''
    dependency: ''
    default: 0
    range: [0, 1, 2, 32]
  ssim:
    description: Whether to enable SSIM calculation. [1] 0 - disable 1 - enable
    csv_column: ''
    group: ''
    dependency: ''
    default: 1
    range: [0, 1]
  psnr:
    description: Whether to enable PSNR calculation. [1] 0 - disable 1 - enable
    csv_column: ''
    group: ''
    dependency: ''
    default: 1
    range: [0, 1]
  enableVuiTimingInfo:
    description: Whether to write VUI timing information in SPS. [1] 0 - disable 1
      - enable
    csv_column: ''
    group: ''
    dependency: ''
    default: 1
    range: [0, 1]
  enableFrameInfoVersion:
    description: Whether to enable frame statistics and luma statistics output to
      DDR. [0] 0 - disable 1 - frame information 2 - luma information and  frame information
    csv_column: ''
    group: ''
    dependency: ''
    default: 0
    range: [0, 2]
  sse0Enable:
    description: Whether to enable region 0 for SSE statistics. 0 - disable 1 - enable
    csv_column: ''
    group: ''
    dependency: ''
    range: [0, 1]
  sse1Enable:
    description: Whether to enable region 1 for SSE statistics. 0 - disable 1 - enable
    csv_column: ''
    group: ''
    dependency: ''
    range: [0, 1]
  sse2Enable:
    description: Whether to enable region 2 for SSE statistics. 0 - disable 1 - enable
    csv_column: ''
    group: ''
    dependency: ''
    range: [0, 1]
  sse3Enable:
    description: Whether to enable region 3 for SSE statistics. 0 - disable 1 - enable
    csv_column: ''
    group: ''
    dependency: ''
    range: [0, 1]
  sse4Enable:
    description: Whether to enable region 4 for SSE statistics. 0 - disable 1 - enable
    csv_column: ''
    group: ''
    dependency: ''
    range: [0, 1]
  sse5Enable:
    description: Whether to enable region 5 for SSE statistics. 0 - disable 1 - enable
    csv_column: ''
    group: ''
    dependency: ''
    range: [0, 1]
  sse6Enable:
    description: Whether to enable region 6 for SSE statistics. 0 - disable 1 - enable
    csv_column: ''
    group: ''
    dependency: ''
    range: [0, 1]
  sse7Enable:
    description: Whether to enable region 7 for SSE statistics. 0 - disable 1 - enable
    csv_column: ''
    group: ''
    dependency: ''
    range: [0, 1]
  sse0Rect:
    description: Region 0 for SSE statistics. 0 - disable 1 - enable
    csv_column: ''
    group: ''
    dependency: ''
    range: [0, 1]
  sse1Rect:
    description: Region 1 for SSE statistics. 0 - disable 1 - enable
    csv_column: ''
    group: ''
    dependency: ''
    range: [0, 1]
  sse2Rect:
    description: Region 2 for SSE statistics. 0 - disable 1 - enable
    csv_column: ''
    group: ''
    dependency: ''
    range: [0, 1]
  sse3Rect:
    description: Region 3 for SSE statistics. 0 - disable 1 - enable
    csv_column: ''
    group: ''
    dependency: ''
    range: [0, 1]
  sse4Rect:
    description: Region 4 for SSE statistics. 0 - disable 1 - enable
    csv_column: ''
    group: ''
    dependency: ''
    range: [0, 1]
  sse5Rect:
    description: Region 5 for SSE statistics. 0 - disable 1 - enable
    csv_column: ''
    group: ''
    dependency: ''
    range: [0, 1]
  sse6Rect:
    description: Region 6 for SSE statistics. 0 - disable 1 - enable
    csv_column: ''
    group: ''
    dependency: ''
    range: [0, 1]
  sse7Rect:
    description: Region 7 for SSE statistics. 0 - disable 1 - enable
    csv_column: ''
    group: ''
    dependency: ''
    range: [0, 1]
Peripheral and Hardware Setup:
  csv_column: ''
  Reference Frame Compression:
    csv_column: 'rfc_cfg'
    compressor:
      description: Whether to enable embedded reference frame compression. [0] 0 -
        disable compression 3 - enable compression for both luma and chroma data
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
      range: [0, 3]
    enableP010Ref:
      description: Whether to store reference frames in tiled or raster P010 format
        in buffers. [0] 0 - tiled 1 - raster
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
      range: [0, 1]
  OSD & Mosaic:
    csv_column: ''
    OSD Control:
      csv_column: 'osd_cfg'
      overlayEnables:
        description: 'Each bit indicates whether to enable the corresponding overlay
          region. [0] For example: - Value 1 indicates region 1 enabled. - Value 2
          indicates region 2 enabled. - Value 3 indicates regions 1 and 2 enabled.'
        csv_column: ''
        group: 'ol_input'
        dependency: ''
        default: 0
      olInputN:
        description: The path to the file that contains the input file to each OSD
          region. [olInputi.yuv]
        csv_column: ''
        group: 'ol_input'
        dependency: ''
        default: olInputi.yuv
      olFormatN:
        description: The input format of each OSD region. [0] 0 - ARGB8888 1 - NV12
          2 - bitmap
        csv_column: ''
        group: 'ol_input'
        dependency: ''
        default: 0
      olSuperTileN:
        description: 'Whether the OSD input data is organized in supertile mode. [0]
          0 - non-supertile mode. 1 - X-major supertile mode. 2 - Y-major supertile
          mode. NOTE: super tile is only valid when input format is ARGB8888.'
        csv_column: ''
        group: ''
        dependency: ''
        default: 0
        range: [0, 2]
      olAlphaN:
        description: 0..255 The global alpha value for the OSD region. [0] This option
          is invalid for ARGB8888.
        csv_column: ''
        group: 'ol_input'
        dependency: ''
        default: 0
        range: [0, 255]
        test_range: [0]
      olWidthN:
        description: The width of each OSD input. [0] If a region is enabled, this
          option must be specified for the region. The option value must be 8 aligned
          for the bitmap format.
        csv_column: ''
        group: 'ol_input'
        dependency: ''
        default: 0
      olHeightN:
        description: The height of each OSD input. [0] If a region is enabled, this
          option must be specified for the region. The option value must be 8 aligned
          for the bitmap format.
        csv_column: ''
        group: 'ol_input'
        dependency: ''
        default: 0
      olXoffsetN:
        description: The horizontal offset, in pixels, of the top-left corner of each
          OSD region relative to the encoder picture. [0] The option value must be
          2 aligned.
        csv_column: ''
        group: 'ol_input'
        dependency: ''
        default: 0
      olYoffsetN:
        description: The vertical offset, in pixels, of the top-left corner of each
          OSD region relative to the encoder picture. [0] The option value must be
          2 aligned.
        csv_column: ''
        group: 'ol_input'
        dependency: ''
        default: 0
      olYStrideN:
        description: The Y stride of each OSD input, in bytes. For ARGB8888, the default
          value is (olWidthi * 4). For NV12, the default value is olWidthi For bitmap,
          the default value is (olWidthi/8).
        csv_column: ''
        group: 'ol_input'
        dependency: ''
      olUVStrideN:
        description: The UV stride of each OSD input, in bytes. The default value
          depends on the Y stride.
        csv_column: ''
        group: 'ol_input'
        dependency: ''
      olCropXoffsetN:
        description: The horizontal offset, in pixels, of the top-left corner of the
          crop area in each OSD input relative to the overlay input picture. [0] The
          option value must be 8 aligned for the bitmap format, and 2 aligned for
          other formats.
        csv_column: ''
        group: 'ol_input'
        dependency: ''
        default: 0
      olCropYoffsetN:
        description: The vertical offset, in pixels, of the top-left corner of the
          crop area in each OSD input relative to the overlay input picture. [0] The
          option value must be 8 aligned for the bitmap format, and 2 aligned for
          other formats.
        csv_column: ''
        group: 'ol_input'
        dependency: ''
        default: 0
      olCropWidthN:
        description: The width of the crop area in each OSD input, which is the width
          of the final OSD region. [olWidthi] The option value must be 8 aligned for
          the bitmap format.
        csv_column: ''
        group: 'ol_input'
        dependency: ''
        default: 64
      olCropHeightN:
        description: The height of the crop area in each OSD input, which is the height
          of the final OSD region. [olHeighti] The option value must be 8 aligned
          for the bitmap format.
        csv_column: ''
        group: 'ol_input'
        dependency: ''
        default: 80
      olBitmapYN:
        description: The Y value for the bitmap format. [0]
        csv_column: ''
        group: 'ol_bitmap'
        dependency: 'ol_input'
        default: 0
      olBitmapUN:
        description: The U value for the bitmap format. [0]
        csv_column: ''
        group: 'ol_bitmap'
        dependency: 'ol_input'
        default: 0
      olBitmapVN:
        description: The V value for the bitmap format. [0]
        csv_column: ''
        group: 'ol_bitmap'
        dependency: 'ol_input'
        default: 0
    OSD Map:
      csv_column: 'osd_map_cfg'
      osdMapEnable:
        description: The OSD map enable. [0] 0:disable 1:enable
        csv_column: ''
        group: ''
        dependency: ''
        default: 0
      osdMapInput:
        description: The input file for the OSD map. [NULL]
        csv_column: ''
        group: ''
        dependency: ''
        default: 'NULL'
      osdMapStride:
        description: The OSD map stride in bytes. [0] The stride must be greater than
          or equal to (codingWidth + 15[or 7]) >> 4[or 3] where codingWidth equals
        csv_column: ''
        group: ''
        dependency: ''
        default: 0
      osdMapBlockSize:
        description: OSD Map different block size in pixel. [0] 8:8x8 4:4x4
        csv_column: ''
        group: ''
        dependency: ''
        default: 0
      osdMapAlphaN:
        description: 0..255 The alpha value for color N. [0]
        csv_column: ''
        group: ''
        dependency: ''
        default: 0
        range: [0, 255]
        test_range: [0]
      osdMapYN:
        description: The Y value of the OSD map color N. [0]
        csv_column: ''
        group: ''
        dependency: ''
        default: 0
      osdMapUN:
        description: The U value of the OSD map color N. [0]
        csv_column: ''
        group: ''
        dependency: ''
        default: 0
      osdMapVN:
        description: The V value of the OSD map color N. [0]
        csv_column: ''
        group: ''
        dependency: ''
        default: 0
    Mosaic Control:
      csv_column: 'mosaic_cfg'
      mosaicEnables:
        description: 'Each bit indicates whether to enable the corresponding mosaic
          region. [0] For example: - Value 1 indicates region 1 enabled. - Value 2
          indicates region 2 enabled. - Value 3 indicates regions 1 and 2 enabled.'
        csv_column: ''
        group: ''
        dependency: ''
        default: 0
      mosSizeIndex:
        description: different Mosaic size.[0] 0:8x8 1:16x16 2:32x32 3:64x64 4:128x128
        csv_column: ''
        group: ''
        dependency: ''
        default: 0
      mosAreaN:
        description: 'Mosaic region N. - left: the leftmost pixel inside the region.
          - top: the top pixel inside the region. - right: the rightmost pixel inside
          the region. - bottom: the bottom pixel inside the region. Make sure that
          all mosaic regions are aligned to CTB.'
        csv_column: ''
        group: ''
        dependency: ''
  Low-Latency Encoding:
    csv_column: ''
    inputLineBufferMode:
      description: The low-latency encoding mode. [0] 0 - disable low-latency encoding
        1 - enable low-latency encoding with software handshaking and loopback enabled.
        2 - enable low-latency encoding with hardware handshaking and loopback enabled.(effective
        only when the upstream IP is used, only VCE IP cannot be tested) 3 - enable
        low-latency encoding with software handshaking enabled and loopback disabled.
        4 - enable low-latency encoding with hardware handshaking enabled and loopback
        disabled.(effective only when the upstream IP is used, oingle VCE IP cannot
        be tested)
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
      range: [0, 4]
    inputLineBufferDepth:
      description: 0..511 The number of CTB/MB rows to control loopback and handshaking.
        [1] If the loopback mode is enabled, there are two continuous ping-pong input
        line buffers. Each contains inputLineBufferDepth CTB or MB rows. If hardware
        handshaking is enabled, handshaking signals are processed per inputLineBufferDepth
        CTB/MB rows. If software handshaking is enabled, IRQ is issued and the read
        count register is updated each time inputLineBufferDepth CTB/MB rows are read.
      csv_column: ''
      group: ''
      dependency: ''
      default: 1
      range: [0, 511]
      test_range: [1]
    inputLineBufferAmountPerLoopback:
      description: 0..1023 The number of handshake synchronizations every loopback.
        [0] If the option is set to 0, input line buffer is disabled.
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
      range: [0, 1023]
      test_range: [0]
    segmentUnitHeight:
      description: 8, 16 The height of each FLEXA SBI segment unit. [16] This option
        is valid only if the value of EWLHwConfig_t.prpSbiSupport is 1.
      csv_column: ''
      group: ''
      dependency: ''
      default: 16
    sliceIrqEnable:
      description: Whether to enable the hardware to issue an interrupt each time
        it finishes encoding a slice.  [0] 0 - disable 1 - enable
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
      range: [0, 1]
    inputSliceInfoEn:
      description: Whether to enable low-latency encoding in DDR mode. 0 - disable
        1 - enable If low-latency encoding in DDR mode is enabled, the hardware polls
        a 64-byte DDR space for slice information.
      csv_column: ''
      group: ''
      dependency: ''
      range: [0, 1, 64]
    lowlatGatingDisable:
      description: '0..1 Lowlatency handshake auto gating disable[1] 0: enable lowlatency
        handshake auto gating. 1: disalbe lowlatency handshake auto gating.'
      csv_column: ''
      group: ''
      dependency: ''
      default: 1
      range: [0, 1]
    lowlatGatingType:
      description: '0..1 Lowlatency handshake auto gating type.[0] 0: check bus idle
        counter. 1: check emc, recon idle.'
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
      range: [0, 1]
    lowlatGatingCyc:
      description: '0..255 Bus idle counter max value. It would be (sw_enc_lowlat_gating_cyc
        + 1) * 256 cycles when counting. default: hevc - [31], h264 - [7]'
      csv_column: ''
      group: ''
      dependency: ''
      default: 31
      range: [0, 255]
      test_range: [31]
  Input Frame and Reference Frame Buffer Alignment:
    csv_column: 'alignment_cfg'
    inputAlignmentExp:
      description: The input frame buffer alignment. [6] 0 - alignment disabled 4..12
        - The base address and each line of the input frame buffer are aligned to
        two to the power of inputAlignmentExp.
      csv_column: ''
      group: ''
      dependency: ''
      default: 6
      range: [4, 12]
      test_range: [6]
    refAlignmentExp:
      description: The reference frame buffer alignment. [6] 0 - alignment disabled
        4..12 - The base address and each line of the reference frame buffer are aligned
        to two to the power of refAlignmentExp.
      csv_column: ''
      group: ''
      dependency: ''
      default: 6
      range: [4, 12]
      test_range: [6]
    refChromaAlignmentExp:
      description: The alignment of the chroma reference frame buffer. [6] 0 - alignment
        disabled 4..12 - The base address and each line of the chroma reference frame
        buffer are aligned to two to the power of refChromaAlignmentExp.
      csv_column: ''
      group: ''
      dependency: ''
      default: 6
      range: [4, 12]
      test_range: [6]
    aqInfoAlignmentExp:
      description: The alignment of the adaptive quantization output buffer. [0] 0
        - alignment disabled 4..12 - The base address and each line of the adaptive
        quantization output buffer are aligned to two to the power of aqInfoAlignmentExp.
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
      range: [4, 12]
      test_range: [0]
    tileStreamAlignmentExp:
      description: 0..15 The tile stream buffer alignment. [0] The base address and
        size of the tile stream buffer are
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
      range: [0, 15]
      test_range: [0]
  Output Stream Buffer:
    csv_column: 'io_cfg'
    streamBufChain:
      description: 'Whether to enable one or two output stream buffers. [0] 0 - one
        output stream buffer 1 - two chained output stream buffers NOTE: The minimum
        allowed size of the first stream buffer is 11 KB.'
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
      range: [0, 1]
    outBufSizeMax:
      description: 'For each encoded frame, the max stream buffer size is outBufSizeMax
        * 1024 * 1024. [0] The value will be reset depending on the resolution of
        input image if outBufSizeMax=0 during program exexution, like this: no more
        than 2K: 12 more than 2K but no more than 4K: 20 more than 4K but no more
        than 8K: 80'
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
  (Reserved) Multi-Segment Stream Output:
    csv_column: ''
    streamMultiSegmentMode:
      description: (Reserved) The multi-segment mode of output stream buffers. [0]
        0 - single-segment mode 1 - multi-segment mode with software handshaking disabled
        and loopback enabled 2 - multi-segment mode with software handshaking and
        loopback enabled.
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
      range: [0, 2]
    streamMultiSegmentAmount:
      description: (Reserved) 2..16 The number of segments to control loopback, software
        handshaking, and IRQ. [4]
      csv_column: ''
      group: ''
      dependency: ''
      default: 4
      range: [2, 16]
      test_range: [4]
  Parallel Flow Control:
    csv_column: 'parallel_cfg'
    parallelCoreNum:
      description: 1..4 The number of cores that run in parallel at the frame level.
        [1] If the number of tile columns for picture division is set to 1
      csv_column: ''
      group: ''
      dependency: ''
      default: 1
      range: [1, 4]
      test_range: [1]
    batchEnable:
      description: 0..1 enable or disable multi-frame aggregation. [0] 0 - disable
        batch mode. 1 - enable batch mode.
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
      range: [0, 1]
    batchCount:
      description: 1..[parallelCoreNum-1] frame numbers for aggregation encoding.
        It should be smaller than parallelCoreNum. It can be modified during the encoding
        process by setting after @frame_number
      csv_column: ''
      group: ''
      dependency: ''
      default: parallelCoreNum-1
  AXI Settings:
    csv_column: 'axi_cfg'
    AXIAlignment:
      description: 'The AXI alignment in hexadecimal format. [data from fuse for each
        bit] - Bits 35:32: AXI_burst_align_wr_cuinfo - Bits 31:28: AXI_burst_align_wr_common
        - Bits 27:24: AXI_burst_align_wr_stream - Bits 23:20: AXI_burst_align_wr_chroma_ref
        - Bits 19:16: AXI_burst_align_wr_luma_ref - Bits 15:12: AXI_burst_align_rd_common
        - Bits 11:8: AXI_burst_align_rd_prp - Bits 7:4: AXI_burst_align_rd_ch_ref_prefetch
        - Bits 3:0: AXI_burst_align_rd_lu_ref_prefetch'
      csv_column: ''
      group: ''
      dependency: ''
      default: data from fuse for each bit
    AXIAlignment2:
      description: 'The AXI alignment 2 in hexadecimal foramt. - Bits 7:4: AXI_burst_align_wr_4n_recon
        - Bits 3:0: AXI_burst_align_rd_4n_ref'
      csv_column: ''
      group: ''
      dependency: ''
    burstMaxLength:
      description: The maximum AXI burst length, in the unit of AXI bus width. [16]
      csv_column: ''
      group: ''
      dependency: ''
      default: 16
  IRQ Control:
    csv_column: 'irq_cfg'
    IRQ Type Mask of Encoder:
      csv_column: ''
      irqTypeMask:
        description: 'The IRQ type mask setting in binary format. [11111110000] -
          Bit 10: irq_type_burst_cnt_underflow_mask - Bit  9: irq_type_burst0_req_mask
          - Bit  8: irq_type_sw_reset_mask - Bit  7: irq_type_fuse_error_mask - Bit  6:
          irq_type_buffer_full_mask - Bit  5: irq_type_bus_error_mask - Bit  4: irq_type_timeout_mask
          - Bit  3: irq_type_strm_segment_mask - Bit  2: irq_type_line_buffer_mask
          - Bit  1: irq_type_slice_rdy_mask - Bit  0: irq_type_frame_rdy_mask'
        csv_column: ''
        group: ''
        dependency: ''
        default: 11111110000
    IRQ Type Mask of CuTree:
      csv_column: ''
      irqTypeCutreeMask:
        description: The IRQ type mask setting of CuTree, in binary format. [111110000]
          - irq_type_sw_reset_mask - irq_type_fuse_error_mask - irq_type_buffer_full_mask
          - irq_type_bus_error_mask - irq_type_timeout_mask - irq_type_strm_segment_mask
          - irq_type_line_buffer_mask - irq_type_slice_rdy_mask - irq_type_frame_rdy_mask
        csv_column: ''
        group: ''
        dependency: ''
        default: 111110000
  (Only for C-Model) Peripheral Control:
    csv_column: ''
    useVcmd:
      description: (Only valid fo CModel) Whether to enable VCMD. 0 - disable 1 -
        enable
      csv_column: ''
      group: ''
      dependency: ''
      range: [0, 1]
    useDec400:
      description: (Reserved) Whether to enable DEC400. 0 - disable 1 - enable
      csv_column: ''
      group: ''
      dependency: ''
      range: [0, 1]
    useL2Cache:
      description: (Reserved) Whether to enable L2Cache. 0 - disable 1 - enable
      csv_column: ''
      group: ''
      dependency: ''
      range: [0, 1]
  External SRAM:
    csv_column: ''
    extSramLumHeightBwd:
      description: The capacity of external SRAM for luma backward reference. 0 -
        no external SRAM 1..16 - the number of lines = 4 * extSramLumHeightBwd The
        default value is 16 for HEVC (H.265) and 12 for H.264 (AVC).
      csv_column: ''
      group: ''
      dependency: ''
      range: [1, 16]
      test_range: []
    extSramChrHeightBwd:
      description: The capacity of external SRAM for chroma backward reference. 0
        - no external SRAM 1..16 - the number of lines = 4 * extSramChrHeightBwd The
        default value is 8 for HEVC (H.265) and 6 for H.264 (AVC)
      csv_column: ''
      group: ''
      dependency: ''
      range: [1, 16]
      test_range: []
    extSramLumHeightFwd:
      description: The capacity of external SRAM for luma forward reference. 0 - no
        external SRAM 1..16 - the number of lines = 4 * extSramLumHeightFwd The default
        value is 16 for HEVC (H.265) and 12 for H.264 (AVC).
      csv_column: ''
      group: ''
      dependency: ''
      range: [1, 16]
      test_range: []
    extSramChrHeightFwd:
      description: The capacity of external SRAM for chroma forward reference. 0 -
        no external SRAM 1..16 - the number of lines = 4 * extSramChrHeightFwd The
        default value is 8 for HEVC (H.265) and 6 for H.264 (AVC).
      csv_column: ''
      group: ''
      dependency: ''
      range: [1, 16]
      test_range: []
Debugging and Testing:
  csv_column: 'debug_cfg'
  Debugging:
    csv_column: ''
    dumpRegister:
      description: Whether to dump register values. [0] 0 - do not dump 1 - dump
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
      range: [0, 1]
    rasterscan:
      description: Whether to dump reconstructed YUV frames in tiled or raster format
        when the encoder runs on FPGA and hardware. [0] 0 - tiled format 1 - raster
        format
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
      range: [0, 1]
  Testing:
    csv_column: ''
    multimode:
      description: The parallel running mode. [0] 0 - disable 1 - multi-thread mode
        2 - multi-process mode
      csv_column: 'parallel_cfg'
      group: ''
      dependency: ''
      default: 0
      range: [0, 2]
    streamcfg:
      description: The path to the file that contains encoder options.
      csv_column: ''
      group: ''
      dependency: ''
  vcmd priority and core bit mask:
    csv_column: ''
    priority:
      description: The priority of current instance in vcmd mode. [0] 0 - normal priority
        1 - high priority
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
      range: [0, 1]
    core_mask:
      description: 'The core bit mask of current instance. [0] 0 - not specify, anycore.
        1 - bit[0]=1: specify core 0. 2 - bit[1]=1: specify core 1.'
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
      range: [0, 2]
  Log Output Control:
    csv_column: ''
    logOutDir:
      description: The log output mode. [0] 0 - outputs all logs to stdout 1 - outputs
        all logs to the same file 2 - outputs logs of each thread to a separate log
        file
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
      range: [0, 2]
    logOutLevel:
      description: 'The log level. [3] 0 - no logs output 1 - the fatal level 2 -
        the error level 3 - the warning level 4 - the infomation level 5 - the debugging
        level: 6 - all logs output'
      csv_column: ''
      group: ''
      dependency: ''
      default: 3
      range: [0, 6]
    logTraceMap:
      description: Whether to output each type of trace logs. [63]=b'0111111 Bit 0
        - encoder API call logs Bit 1 - register configuration logs Bit 2 - EWL API
        call logs Bit 3 - memory usage logs Bit 4 - rate control status logs Bit 5
        - command line logs Bit 6 - performance logs Value 0 indicates not to output
        the type of logs. Value 1 indicates to output the type of logs.
      csv_column: ''
      group: ''
      dependency: ''
      default: 63
      range: [0, 6]
    logCheckMap:
      description: (Reserved) Whether to output each type of check logs. [1]=b`00001
        Bit 0 - reconstructed YUV data Bit 1 - PSNR/SSIM for each frame Bit 2 - VBV
        information for rate control checking Bit 3 - rate control information for
        rate control profiling Bit 4 - feature information for coverage checking Value
        0 indicates not to output the type of logs. Value 1 indicates to output the
        type of logs.
      csv_column: ''
      group: ''
      dependency: ''
      default: 1
      range: [0, 4]
  (Obsoleted) Runtime Log Output Control:
    csv_column: ''
    verbose:
      description: (Obsoleted) The log printing mode. [0] 0 - prints brief information
        1 - prints detailed information
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
      range: [0, 1]
Internal Use:
  csv_column: ''
  testId:
    description: The internal test ID. [0]
    csv_column: ''
    group: ''
    dependency: ''
    default: 0
  rdLog:
    description: Whether to ouput rate distortion logs to the profile.log file. 0
      - do not output 1 - output
    csv_column: ''
    group: ''
    dependency: ''
    range: [0, 1]
  TxTypeSearchEnable:
    description: (For AV1 only) Whether to enable TX type search. [0] 0 - disable
      1 - enable
    csv_column: ''
    group: ''
    dependency: ''
    default: 0
    range: [0, 1]
  av1InterFiltSwitch:
    description: (For AV1 only) Whether to enable interpolation filter switch. [1]
      0 - disable 1 - enable
    csv_column: ''
    group: ''
    dependency: ''
    default: 1
    range: [0, 1]
  replaceMvFile:
    description: 'The path to the file that contains ME1N MVs. Data in the file is
      organized in raster scan of 32x32 pixel blocks. For each block, the file contins
      21 MVs at the following positions: 32x32(0,0), 16x16(0,0), 16x16(16,0), 16x16(0,16),
      16x16(16,16), 8x8(0,0), 8x8(8,0), 8x8(16,0), 8x8(24,0), 8x8(0,8), 8x8(8,8),
      8x8(16,8), 8x8(24,8), 8x8(0,16), 8x8(8,16), 8x8(16,16), 8x8(24,16), 8x8(0,24),
      8x8(8,24), 8x8(16,24), and 8x8(24,24). For each MV, (Hor + Ver) * sizeof(i16)
      * (L0 + L1) = 8 bytes'
    csv_column: ''
    group: ''
    dependency: ''
  SRDEnable:
    description: Detect static region and reduce number of bits spent in these regions.
      [0] 0 = Disable SRD. 1 = Enable SRD.
    csv_column: ''
    group: ''
    dependency: ''
    default: 0
  SRDThreshold:
    description: 0..10 Threshold for static region detection [4] Bigger threshold
      value cause more regions detected as static.
    csv_column: ''
    group: ''
    dependency: ''
    default: 4
    range: [0, 10]
    test_range: [4]
