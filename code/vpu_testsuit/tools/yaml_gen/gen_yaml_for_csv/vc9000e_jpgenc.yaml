Help Information:
  csv_column: ''
  help:
    description: Display help information.
    csv_column: ''
    group: ''
    dependency: ''
Pre-processing Frames:
  csv_column: ''
  Input Frame Resolutions and Cropping:
    csv_column: 'io_cfg'
    input:
      description: Reads input from file. [input.yuv]
      csv_column: 'io_cfg'
      group: 'base'
      dependency: ''
      default: input_176x144_420.yuv
    firstPic:
      description: First picture of input file. [0]
      csv_column: 'io_cfg'
      group: 'base'
      dependency: ''
    lastPic:
      description: Last picture of input file. [0]
      csv_column: 'io_cfg'
      group: 'base'
      dependency: ''
    lumWidthSrc:
      description: Source image width. [176]
      csv_column: 'io_cfg'
      group: 'base'
      dependency: ''
      default: 176
    lumHeightSrc:
      description: Source image height. [144]
      csv_column: 'io_cfg'
      group: 'base'
      dependency: ''
      default: 144
    width:
      description: Output image width. [lumWidthSrc]
      csv_column: 'pp_cfg'
      group: 'vceInput'
      dependency: ''
      default: 176
    height:
      description: Output image height. [lumHeightSrc]
      csv_column: 'pp_cfg'
      group: 'vceInput'
      dependency: ''
      default: 144
    horOffsetSrc:
      description: Horizontal offset of output image. [0]
      csv_column: 'pp_cfg'
      group: 'vceInput'
      dependency: ''
      default: 0
    verOffsetSrc:
      description: Vertical offset of output image. [0]
      csv_column: 'pp_cfg'
      group: 'vceInput'
      dependency: ''
      default: 0
    write:
      description: Whether to write output. [1]  <br>0 - NO <br>1 - YES
      csv_column: ''
      group: ''
      dependency: ''
      default: 1
      range: [0, 1]
  Input Picture Format and Controls:
    csv_column: 'pp_cfg'
    frameType:
      description: Input color format. [0] 0 - YUV420P8b_Raster_A16N (YUV 4:2:0 planar(IYUV/I420))
        1 - YUV420SP8b_Raster_A16N (YUV 4:2:0 semi-planar(NV12)) 2 - YVU420SP8b_Raster_A16N
        (YUV 4:2:0 semi-planar(NV21)) 3 - YUV422YUYV8b_Raster_A16N (YUYV422 interleaved
        (YUYV/YUY2) 4 - YUV422UYVY8b_Raster_A16N (UYVY422 interleaved (UYVY/Y422)
        5 - RGB565_Raster_A16N (RGB565) 6 - BGR565_Raster_A16N (BGR565) 7 - XRGB1555_Raster_A16N
        (RGB555) 8 - XBGR1555_Raster_A16N (BGR555) 9 - XRGB4444_Raster_A16N (RGB444)
        10 - XBGR4444_Raster_A16N (BGR444) 11 - XRGB8888_Raster_A16N (RGB888) 12 -
        XBRG8888_Raster_A16N (BGR888) 13 - XRGB2101010_Raster_A16N (RGB101010) 14
        - XBGR2101010_Raster_A16N (BGR101010) 15 - YUV420P10bWL_Raster_A16N (I010)
        16 - YUV420SP10bWH_Raster_A16N (P010) 17 - YUV420P10b_Raster_A16N 18 - YUV420Y0L210b_Raster_A16N
        19 - YUV420P8b_Tile32x32_A16N 20 - YUV420P8b_Tile16x16_A16N 21 - YUV420SP8b_YuvSp4x4_A16N
        (YUV 4:2:0 semi-planar tile) 22 - YVU420SP8b_YuvSp4x4_A16N (YUV 4:2:0 semi-planar
        tile)
      csv_column: 'io_cfg'
      group: 'base'
      dependency: ''
      default: 0
      range: [0, 22]
      test_range: [0]
    colorConversion:
      description: RGB-to-YUV color conversion type. [0] 0 - conversion to YUV values
        without any range change according to Rec. ITU-R BT.601. 1 - conversion to
        YUV values without any range change according to Rec. ITU-R BT.709. 2 - conversion
        using custom coefficients. 3 - conversion according to Rec. ITU-R BT.2020.
        4 - conversion from full range RGB to limited range YUV according to Rec.
        ITU-R BT.601. 5 - (just for test) conversion from RGB in range of (0~219)
        to limited range YUV according to Rec. ITU-R BT.601. 6 - conversion from full
        range RGB to limited range YUV according to Rec. ITU-R BT.709.
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
      range: [0, 6]
    rotation:
      description: Input image rotation mode. [0] 0 - Disabled 1 - 90 degrees right
        2 - 90 degrees left 3 - 180 degrees
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
      range: [0, 3]
    mirror:
      description: Whether to enable horizontal mirroring for input image. [0] 0 -
        Disable 1 - Enable
      csv_column: ''
      group: ''
      dependency: ''
    inputAlignmentExp:
      description: Alignment value of input frame buffer. [4] 0 - Disable alignment
        4 to 12 - Base address of input frame buffer and each line are aligned to
        2^inputAlignmentExp
      csv_column: ''
      group: ''
      dependency: ''
      default: 4
      range: [0, 12]
      test_range: [0, 4, 12]
    enableConstChroma:
      description: 'Whether to set chroma to a constant pixel value. [0] Value range:
        0 and 1 0 - Disable 1 - Enable'
      csv_column: ''
      group: 'ConstChroma'
      dependency: ''
      default: 0
      range: [0, 1]
    constCb:
      description: 'Constant pixel value for the U component. [128]    <br> Value
        range: 0 to 255'
      csv_column: ''
      group: 'ConstChroma'
      dependency: ''
      default: 128
      range: [0, 255]
    constCr:
      description: 'Constant pixel value for the V component. [128]    <br> Value
        range: 0 to 255'
      csv_column: ''
      group: 'ConstChroma'
      dependency: ''
      default: 128
      range: [0, 255]
    scanType:
      description: The scan type of input image. [0] 0 - raster 1 - supertileX
      csv_column: ''
      group: ''
      dependency: ''
  OSD Overlay Control:
    csv_column: 'osd_cfg'
    overlayEnables:
      description: 'Overlay region status, with 12 bits representing 12 regions respectively.
        [0] 1: Region 1 enabled 2: Region 2 enabled 3: Region 1 and 2 enabled and
        so on.'
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
    olInputN:
      description: Input file for overlay region. [olInputi.yuv]
      csv_column: ''
      group: ''
      dependency: ''
      default: olInputi.yuv
    olFormatN:
      description: 'Overlay input format. [0] Value range: 0 to 2 0: ARGB8888 1: NV12
        2: Bitmap'
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
    olAlphaN:
      description: 'Global alpha value for NV12 and bitmap overlay format. [0] <br>
        Value range: 0 to 255'
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
    olWidthN:
      description: Overlay region width. It can only be set when a region is enabled.
        [0] It must be under eight-pixel aligned for bitmap format.
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
    olHeightN:
      description: Overlay region height. It can only be set when a region is enabled.
        [0]
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
    olXoffsetN:
      description: Horizontal offset of overlay region top-left pixel. [0] It must
        be two-pixel aligned. [0]
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
    olYoffsetN:
      description: Vertical offset of overlay region top left pixel. [0] It must be
        two-pixel aligned. [0]
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
    olYStrideN:
      description: 'Luma stride in bytes. The default value depends on format. <br>Value
        range: [olWidthi * 4] when the format is ARGB8888. [olWidthi] when the format
        is NV12. [olWidthi / 8] when the format is bitmap.'
      csv_column: ''
      group: ''
      dependency: ''
      default: olWidthi * 4
    olUVStrideN:
      description: Chroma stride in bytes. The default value depends on the luma stride.
      csv_column: ''
      group: ''
      dependency: ''
    olCropXoffsetN:
      description: Top left horizontal offset for OSD cropping. [0] For non-bitmap
        formats, it must be under two-pixel aligned. For bitmap format, it must be
        under eight-pixel aligned.
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
    olCropYoffsetN:
      description: Top left vertical offset for OSD cropping. [0] For non-bitmap formats,
        it must be under two-pixel aligned. For bitmap format, it must be under eight-pixel
        aligned.
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
    olCropWidthN:
      description: OSD cropping width. [olWidthi] For bitmap format, it must be under
        eight-pixel aligned.
      csv_column: ''
      group: ''
      dependency: ''
      default: olWidthi
    olCropHeightN:
      description: OSD cropping height. [olHeighti]
      csv_column: ''
      group: ''
      dependency: ''
      default: olHeighti
    olBitmapYN:
      description: Y value of the OSD bitmap format. [0]
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
    olBitmapUN:
      description: U value of the OSD bitmap format. [0]
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
    olBitmapVN:
      description: V value of the OSD bitmap format. [0]
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
    olSuperTileN:
      description: Whether the OSD input data is organized in supertile mode. [0]
        0 - non-supertile mode. 1 - X-major supertile mode. 2 - Y-major supertile
        mode.
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
      range: [0, 2]
    olScaleWidthN:
      description: The width of each OSD input. [0] If a region is enabled, this option
        must be specified for the region. The option value must be 8 aligned for the
        bitmap format.
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
    olScaleHeightN:
      description: The height of each OSD input. [0] If a region is enabled, this
        option must be specified for the region.
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
  Mosaic Area Control:
    csv_column: 'mosaic_cfg'
    mosaicEnables:
      description: 'Enabling status of mosaic regions, with one bit for one region.
        (Not support Monochrome encoding if enable) [0] 1: Region 1 enabled 2: Region
        2 enabled 3: Region 1 and 2 enabled and so on.'
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
    mosSizeIndex:
      description: 'Different Mosaic size.[0] 0: 8x8 1: 16x16 2: 32x32 3: 64x64 4:
        128x128'
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
    mosAreaN:
      description: Mosaic region parameters, including  coordinates of the left, top,
        right, and bottom pixels. All coordinates must be CTB/MCU aligned.
      csv_column: ''
      group: ''
      dependency: ''
  OSDMap Controls:
    csv_column: 'osd_map_cfg'
    osdMapEnable:
      description: The OSD map enable. [0] 0:disable 1:enable
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
    osdMapInput:
      description: input file for OSD Map. [NULL]
      csv_column: ''
      group: ''
      dependency: ''
      default: 'NULL'
    osdMapStride:
      description: OSD Map stride in byte. [width]
      csv_column: ''
      group: ''
      dependency: ''
      default: width
    osdMapBlockSize:
      description: OSD Map different block size in pixel. [0] 8:8x8 4:4x4
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
    osdMapAlphaN:
      description: 0..255 Specify the alpha value for color[i]. [0]
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
      range: [0, 255]
      test_range: [0]
    osdMapYN:
      description: OSD Map format color[1] Y value. [0]
      csv_column: ''
      group: ''
      dependency: ''
      default: 1
    osdMapUN:
      description: OSD Map format color[1] U value. [0]
      csv_column: ''
      group: ''
      dependency: ''
      default: 1
    osdMapVN:
      description: OSD Map format color[1] V value. [0]
      csv_column: ''
      group: ''
      dependency: ''
      default: 1
  DEC400 Tile Status:
    csv_column: 'dec400_cfg'
    dec400TableInput:
      description: DEC400 compressed table input from file. [dec400CompTableinput.bin]
      csv_column: ''
      group: ''
      dependency: ''
      default: dec400CompTableinput.bin
    osdDec400TableInputN:
      description: The file from which the video encoder reads the DEC400 compression
        table for OSD input. [NULL]
      csv_column: ''
      group: ''
      dependency: ''
      default: 'NULL'
    dec400TileSize:
      description: 0 - default 1 - 128byte. 2 - 512byte. 3 - 256byte.
      csv_column: ''
      group: ''
      dependency: ''
      range: [0, 3]
    dec400TileMode:
      description: 0 - default.[0] 1 - TILE_8x8_x. 2 - TILE_8x8_y. 3 - TILE_16x4.
        4 - TILE_8x4. 5 - TILE_4x8. 6 - RASTER_16x4. 7 - TILE_64x4. 8 - TILE_32x4.
        9 - RASTER256x1. 10 - RASTER128x1. 11 - RASTER64x1. 12 - TILE_16x8. 13 - RASTER32x4.
        14 - RASTER32x1. 15 - RASTER16x1. 16 - TILE_8x4_s. 17 - TILE_16x4_s. 18 -
        TILE_32x4_s. 19 - TILE_32x8.
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
      range: [0, 19]
      test_range: [0]
    dec400DataAlignment:
      description: 0 - default 1 - 32byte. 2 - 64byte. 3 - 1byte. 4 - 16byte.
      csv_column: ''
      group: ''
      dependency: ''
      range: [0, 4]
    dec400RGBAX:
      description: 0 - ARGB.[0] 1 - XRGB.
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
      range: [0, 1]
    dec400RGBFormat:
      description: 0 - default 1 - RGB8. 2 - RGB10. 3 - RGB4. 4 - RGB1555. 5 - RGB565.
      csv_column: ''
      group: ''
      dependency: ''
      range: [0, 5]
    dec400TSHeaderEnable:
      description: Whether to process dec400 tile status header buffer.(128byte) 0
        - disable 1 - enable
      csv_column: ''
      group: ''
      dependency: ''
      range: [0, 1]
  UFBC Parameters:
    csv_column: 'ufbc_cfg'
    ufbcMode:
      description: The core mode of UFBC. 0 - disable 1 - afbc (only support 32x8
        pixels) - Version 1.0 2 - afbc (support 32x8 and 16x16 pixels) - Version 1.1
        3 - dec400 - Version 4.0 4 - pvric - Version 2.0 5 - afbc (support tile format)
        - Version 1.2
      csv_column: ''
      group: ''
      dependency: ''
      range: [0, 5]
    ufbcYuvTrans:
      description: Whether to enable interal YUV transformation. [0] 0 - disable 1
        - enable
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
      range: [0, 1]
    ufbcHeaderTile:
      description: Is the header in tile format. [0] Can only be configured in Version
        1.2.(ufbcMode = 5) 0 - disable 1 - enable
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
      range: [0, 1]
    ufbcPayloadAlign:
      description: Payload address and length byte align. [0] Can only be configured
        in Version 1.2.(ufbcMode = 5) 0 - 16 byte 1 - 32 byte 2 - 64 byte 3 - 128
        byte
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
      range: [0, 3]
    ufbcHeaderAlign:
      description: Header address and length byte align. [0] Can only be configured
        in Version 1.2.(ufbcMode = 5) 0 - 16 byte 1 - 32 byte 2 - 64 byte 3 - 128
        byte
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
      range: [0, 3]
    ufbcBlockType:
      description: The size of the superblock. Version 1.x 0 - 32x8 pixels 1 - 16x16
        pixels Version 2.x 0 - 8x8 pixels 1 - 16x4 pixels 2 - 32x2 pixels
      csv_column: ''
      group: ''
      dependency: ''
      range: [0, 2]
    ufbcBlockSplit:
      description: Whether to enable the superblock split mode for version 1.x. 0
        - disable 1 - enable
      csv_column: ''
      group: ''
      dependency: ''
      range: [0, 1]
    ufbcConstantVal:
      description: The constant color for version 2.x.
      csv_column: ''
      group: ''
      dependency: ''
Coding Tools and Syntax Control:
  csv_column: 'bs_fmt_cfg'
  Coding Modes and Tools:
    csv_column: ''
    restartInterval:
      description: Restart interval in MCU rows. [0]
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
      range: [0, 3]
    quality:
      description: Set quality factor instead of qLevel. [-1] -1 - use quantization
        table defined by qLevel or fixedQP. 1..100 - use quantization table generated
        accordingly. Value 100 stands for the best quality.
      csv_column: ''
      group: ''
      dependency: ''
      default: -1
      range: [-1, 100]
      test_range: [-1, 1, 50, 100]
    qLevel:
      description: 'Quantization scale. [1] Value range: 0 to 10 The value 10 indicates
        the testbench-defined quantization table is used.'
      csv_column: ''
      group: ''
      dependency: ''
      default: 1
    qTableFile:
      description: External quantization file to be read when quantization level is
        10. The file has 16 lines separated by space to specify two quantization tables,
        with the first 8 lines indicating luma table, and the rest 8 lines indicating
        chroma table. Each line has 8 values ranging from 1 to 255.
      csv_column: ''
      group: ''
      dependency: ''
    codingType:
      description: Encoding type. [0] 0 - Whole frame encoding 1 - Partial frame encoding
      csv_column: ''
      group: 'codingType'
      dependency: ''
      default: 0
      range: [0, 1]
    codingMode:
      description: Encoding mode. [0] 0 - YUV420 1 - YUV422 (only for input YUV422SP
        and YVU422SP) 2 - Monochrome
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
      range: [0, 2]
    markerType:
      description: Quantization/Huffman table markers. [0] 0 - Single marker 1 - Multiple
        markers
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
      range: [0, 1]
  APP0 information:
    csv_column: ''
    units:
      description: Unit of X- and Y-density. [0] 0 - Pixel aspect ratio 1 - Dots per
        inch 2 - Dots per centimetre
      csv_column: ''
      group: 'APP0_density'
      dependency: ''
      default: 0
      range: [0, 2]
    xdensity:
      description: X-density to APP0 header. [1]
      csv_column: ''
      group: 'APP0_density'
      dependency: ''
      default: 1
    ydensity:
      description: Y-density to APP0 header. [1]
      csv_column: ''
      group: 'APP0_density'
      dependency: ''
      default: 1
    inputThumb:
      description: Reads thumbnail input from file. [thumbnail.jpg]
      csv_column: ''
      group: 'thumbnail'
      dependency: ''
      default: thumbnail.jpg
    thumbnail:
      description: Thumbnail to stream. [0] 0 - NO 1 - JPEG 2 - RGB8 3 - RGB24
      csv_column: ''
      group: 'thumbnail'
      dependency: ''
      default: 0
      range: [0, 3]
    widthThumb:
      description: Thumbnail output image width. [32]
      csv_column: ''
      group: 'thumbnail'
      dependency: ''
      default: 32
    heightThumb:
      description: Thumbnail output image height. [32]
      csv_column: ''
      group: 'thumbnail'
      dependency: ''
      default: 32
    comLength:
      description: Comment header data length. [0]
      csv_column: ''
      group: 'comment_file'
      dependency: ''
      default: 0
    comFile:
      description: Comment header data file. [com.txt]
      csv_column: ''
      group: 'comment_file'
      dependency: ''
      default: com.txt
  Lossless Encoding:
    csv_column: ''
    lossless:
      description: 'Lossless encoding mode. [0]   <br> Value range: 0 to 7 0 - Disable
        1 to 7 - Enable, with selected prediction modes 1 to 7; not supported by TGU02' 
      csv_column: ''
      group: ''
      dependency: ''
    ptrans:
      description: 'Point transform value for lossless encoding. [0] <br> Value range:
        0 to 7; not supported by TGU02'
      csv_column: ''
      group: ''
      dependency: ''
  ROI Map:
    csv_column: 'roi_cfg'
    roimapFile:
      description: Input file for ROI map region. [NULL] NULL - Disable ROI Map. "roimap.roi"
        - text file name to describe ROI regions
      csv_column: ''
      group: ''
      dependency: ''
      default: 'NULL'
    nonRoiFilter:
      description: Input file for non-ROI map region filter. [NULL] NULL - NO exteranl
        filter, use pre-defined filter level "filter.txt" - specify external filter
      csv_column: ''
      group: ''
      dependency: ''
      default: 'NULL'
    nonRoiLevel:
      description: 'Non-ROI filter level [5] Value range: 0 to 9. from strong to weak.'
      csv_column: ''
      group: ''
      dependency: ''
      default: 5
  Motion JPEG and Rate Control:
    csv_column: 'rc_cfg'
    mjpeg:
      description: Whether to enable motion JPEG [0] 0 - Disable 1 - Enable
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
      range: [0, 1]
    bitPerSecond:
      description: Target bits per second. [0] 0 - Disable RC None-zero value - RC
        enabled with the specified target bits per second.
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
      range: [0, 0]
    frameRateNum:
      description: 'Output frame rate numerator. [30] Value range: 1 to 1048575'
      csv_column: ''
      group: ''
      dependency: ''
      default: 30
    frameRateDenom:
      description: 'Output frame rate denominator. [1] Value range: 1 to 1048575'
      csv_column: ''
      group: ''
      dependency: ''
      default: 1
    rcMode:
      description: 'JPEG/MJPEG RC mode. [1] Value range: 0 to 2 0 - Single frame RC
        mode. 1 - Video RC with CBR. 2 - Video RC with VBR.'
      csv_column: ''
      group: ''
      dependency: ''
      default: 1
      range: [0, 2]
    picQpDeltaRange:
      description: 'QP delta range in picture-level rate control. Min: Minimum Qp_Delta
        in picture RC. [-2]  <br> Value range: -10 to -1 Max: Maximum Qp_Delta in
        picture RC. [3]  <br> Value range: 1 to 10 The value ranges only apply to
        two neighboring frames.'
      csv_column: ''
      group: ''
      dependency: ''
      default: -2
    qpMin:
      description: 'Minimum frame QP. [0] <br> Value range: 0 to 51'
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
    qpMax:
      description: 'Maximum frame QP. [51] <br> Value range: 0 to 51'
      csv_column: ''
      group: ''
      dependency: ''
      default: 51
    fixedQP:
      description: 'Fixed QP for every frame. [-1] Value range: -1 to 51 -1   - Disable
        fixed QP mode. 0-51 - Fixed QP value.'
      csv_column: ''
      group: ''
      dependency: ''
      default: -1
      range: [0, 1, 51]
  SRAM Control:
    csv_column: ''
    sramPowerdownDisable:
      description: Whether to disenableSRAM power down [0] 0 - Enable sram power down
        1 - Disable sram power down
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
      range: [0, 1]
    sramPowerdownMode:
      description: SRAM hw model [0] 0 - Mode 00 1 - Mode 01 2 - Mode 10 3 - Mode
        11
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
      range: [0, 3]
Stream Output Control:
  csv_column: ''
  output:
    description: Writes output to file. [stream.jpg]
    csv_column: 'io_cfg'
    group: 'base'
    dependency: ''
    default: stream.jpg
  streamBufChain:
    description: 'Stream buffer output modes. [0] 0 - Single output stream buffer.
      1 - Two output stream buffers chained together. <b>Note</b>: Minimum size of
      the first stream buffer is 1KB plus the thumbnail data size.'
    csv_column: ''
    group: ''
    dependency: ''
    default: 0
    range: [0, 1]
  hashtype:
    description: The method to calculate the hash value of the output stream. [0]
      0 - hash value calculation disabled 1 - standard CRC32 2 - 32-bit checksum
    csv_column: ''
    group: ''
    dependency: ''
    default: 0
    range: [0, 1, 2]
  Stream Multi-Segment for Output Low Latency:
    csv_column: ''
    streamMultiSegmentMode:
      description: 'Stream multi-segment mode. [0]  <br> Value range: 0 to 3 0 - Disable
        1 (Reserved) - Enable (hardware handshaking, loop-back enabled) 2 (Reserved)
        - Enable (software handshaking, loop-back enabled) 3 - Enable (IRQ only, no
        loop-back)'
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
      range: [0, 3]
    streamMultiSegmentSize:
      description: 'Segment size in byte. [1024] <br> Value range: 512 byte to 16KB
        It should be a multiple of 16 bytes.'
      csv_column: ''
      group: ''
      dependency: ''
      default: 1024
    streamMultiSegmentAmount:
      description: 'Total amount of segments. [4] <br> Value range: 2 to 1024 0 -
        Full image size = Width x Height x 2'
      csv_column: ''
      group: ''
      dependency: ''
      default: 4
      range: [0, 0]
Input Low Latency Mode:
  csv_column: 'lowlatency_cfg'
  inputLineBufferMode:
    description: 'Input line buffer mode. [0] Value range: 0 to 4 0 = Disable 1 =
      Enable (software handshaking, loopback enabled) 2 = Enable (hardware handshaking,
      loopback enabled) (effective only when the upstream IP is used, only VCE IP
      cannot be tested) 3 = Enable (software handshaking, loopback disabled) 4 = Enable
      (hardware handshaking, loopback disabled) (effective only when the upstream
      IP is used, only VCE IP cannot be tested)'
    csv_column: ''
    group: ''
    dependency: ''
    default: 0
  inputLineBufferDepth:
    description: 'Number of MCU rows to control loop-back or handshaking [1] Value
      range: 0 to 511 - When loop-back is enabled, each of the two continuous ping-pong
      input buffers contains MCU rows specified by this option. - When hardware handshaking
      is enabled, handshaking signal is processed per CTB/MCU rows specified by this
      option. - When software handshaking is enabled, IRQ is sent and Read Count Register
      is updated each time a number of MCU rows specified by this option have been
      read. <b>Note</b>: This option can be set to 0 only when inputLineBufferMode
      is set to 3. In this case, IRQ is not sent and Read Count Register is not updated.
      loop-back not supported by TGU02'
    csv_column: ''
    group: ''
    dependency: ''
  inputLineBufferAmountPerLoopback:
    description: 'Line buffer amount in the case of buffer read/write address loopback.
      [0] <br> Value range: 0 to 1023; not supported by TGU02'
    csv_column: ''
    group: ''
    dependency: ''
  segmentUnitHeight:
    description: 'Segment unit height when low latency is in SBI mode. [16] Value
      range: 8 and 16; low latency mode need upstream IP support'
    csv_column: ''
    group: ''
    dependency: ''
    default: 16
  inputSliceInfoEn:
    description: 'Control DDR low-latency mode. [0] 0 - Disable 1 - Enable <b>Note</b>:
      HW will poll slice info structure saved in DDR, which in a 64 byte ddr space'
    csv_column: ''
    group: ''
    dependency: ''
    default: 0
    range: [0, 1]
Hardware Control:
  csv_column: ''
  AXI Interface:
    csv_column: 'axi_cfg'
    AXIAlignment:
      description: AXI alignment setting (in hexadecimal format). [0] bit[31:28] AXI_burst_align_wr_common
        bit[27:24] AXI_burst_align_wr_stream bit[23:20] AXI_burst_align_wr_chroma_ref
        bit[19:16] AXI_burst_align_wr_luma_ref bit[15:12] AXI_burst_align_rd_common
        bit[11:8] AXI_burst_align_rd_prp bit[7:4] AXI_burst_align_rd_ch_ref_prefetch
        bit[3:0] AXI_burst_align_rd_lu_ref_prefetch
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
    burstMaxLength:
      description: Maximum AXI burst length. [16]
      csv_column: ''
      group: ''
      dependency: ''
      default: 16
  IRQ Type:
    csv_column: 'irq_cfg'
    irqTypeMask:
      description: IRQ type mask setting (in binary format). [11111110000] swreg1
        bit28 irq_type_burst_cnt_underflow_mask, default 1 swreg1 bit27 irq_type_burst0_req_mask,
        default 1 swreg1 bit24 irq_type_sw_reset_mask, default 1 swreg1 bit23 irq_type_fuse_error_mask,
        default 1 swreg1 bit22 irq_type_buffer_full_mask, default 1 swreg1 bit21 irq_type_bus_error_mask,
        default 1 swreg1 bit20 irq_type_timeout_mask, default 1 swreg1 bit19 irq_type_strm_segment_mask,
        default 0 swreg1 bit18 irq_type_line_buffer_mask, default 0 swreg1 bit17 irq_type_slice_rdy_mask,
        default 0 swreg1 bit16 irq_type_frame_rdy_mask, default 0
      csv_column: ''
      group: ''
      dependency: ''
      default: 11111110000
  Peripherals Control (C-Model Only):
    csv_column: ''
    useVcmd:
      description: (Only valid fo CModel) Whether to enable VCMD. 0 - Disable 1 -
        Enable
      csv_column: ''
      group: ''
      dependency: ''
      range: [0, 1]
  Low latency gating:
    csv_column: ''
    lowlatGatingDisable:
      description: '0..1 Lowlatency handshake auto gating disable[1] 0: enable lowlatency
        handshake auto gating. 1: disalbe lowlatency handshake auto gating. If enable,
        check emc and  recon idle.'
      csv_column: ''
      group: ''
      dependency: ''
      default: 1
      range: [0, 1]
Debugging:
  csv_column: 'debug_cfg'
  Dump Registers:
    csv_column: ''
    dumpRegister:
      description: 'Whether to dump register. [0] Value range: 0 - Dump no register.
        1 - Dump print register. When it is set to 1, software dumps register values
        to'
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
      range: [0, 1]
  Log and Print Message Control:
    csv_column: ''
    logOutDir:
      description: Log message output directory. [0] 0 - All logs output to stdout.
        1 - One log file for all logs. 2 - One log file of each instance thread.
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
      range: [0, 2]
    logOutLevel:
      description: Log message output level. [3] 0 -"quiet", no output 1 - "fatal",
        serious errors 2 - "error", error occurs 3 - "warn", warning 4 - "info", general
        information 5 - "debug", debug information 6 - "all", all log information
      csv_column: ''
      group: ''
      dependency: ''
      default: 3
      range: [0, 6]
    logTraceMap:
      description: Trace information of log message output for prompting and debugging.
        [63]=b`0111111 Bit 0 - Dump API call. Bit 1 - Dump registers. Bit 2 - Dump
        EWL. Bit 3 - Dump memory usage. Bit 4 - Dump Rate Control Status Bit 5 (This
        bit is reserved for future use) - Output full command line Bit 6 - Output
        performance information
      csv_column: ''
      group: ''
      dependency: ''
      default: 63
      range: [0, 1, 2, 3, 4, 6]
  Testing:
    csv_column: ''
    trigger:
      description: Logic analyzer trigger for the nth picture. [-1] -1 - Disable the
        trigger.
      csv_column: ''
      group: ''
      dependency: ''
      default: -1
      range: [1, 1]
    XformCustomerPrivateFormat:
      description: Customer private format to be converted from YUV420. [-1] -1 -
        Disable conversion 0 - Customer private tile format for HEVC 1 - Customer
        private tile format for H.264 2 - Customer private YUV422SP_888 3 - Common
        data 8-bit tile 4x4 4 - Common data 10-bit tile 4x4 5 - Customer private tile
        format for JPEG
      csv_column: ''
      group: ''
      dependency: ''
      default: -1
      range: [0, 1, 2, 3, 4, 5, 8, 10]
      test_range: [-1]
    osdDec400TableInput:
      description: OSD DEC400 compressed table input from file. [osdDec400CompTableinput.bin]
      csv_column: ''
      group: ''
      dependency: ''
  vcmd priority and core bit mask:
    csv_column: ''
    priority:
      description: The priority of current instance in vcmd mode. [0] 0 - normal priority
        1 - high priority
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
      range: [0, 1]
    core_mask:
      description: 'The core bit mask of current instance. [0] 0 - not specify, anycore.
        1 - bit[0]=1: specify core 0. 2 - bit[1]=1: specify core 1.'
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
      range: [0, 2]
