Help Info Options:
  csv_column: ''
  help:
    description: Print command line parameters help info.
    csv_column: ''
    group: ''
    dependency: ''
Testbench Options:
  csv_column: ''
  tb-cfg:
    description: Specify the path of tb.cfg file, e.g. ./tb.cfg, e.g. ./xxx.cfg
    csv_column: ''
    group: ''
    dependency: ''
  Input Options:
    csv_column: 'io_cfg'
    INPUT_BS:
      description: self-defined parameter for input file name.
      csv_column: ''
      group: 'base'
      dependency: ''
      default:
    input-format:
      description: Force input file format interpretation. Format can be one of the
        following. bs - bytestream format ivf - IVF file format webm - WebM file format
      csv_column: ''
      group: ''
      dependency: 'md5'
      default: bs
    packet-by-packet:
      description: Packetize input bitstream
      csv_column: ''
      group: ''
      dependency: 'md5'
      default:
      test_range: ['packet-by-packet']
    full-stream:
      description: Read full-stream into single buffer. Only with bytestream.
      csv_column: ''
      group: ''
      dependency: 'md5'
    nalu:
      description: NALU input bitstream (without start code)
      csv_column: ''
      group: ''
      dependency: 'md5'
      test_range: ['nalu']
    non-ringbuffer:
      description: Disable ringbuffer mode for stream input buffer. By default testbench
        may store input stream in a ring buffer mode.
      csv_column: ''
      group: ''
      dependency: 'md5'
      test_range: ['non-ringbuffer']
  Threading Options:
    csv_column: 'parallel_cfg'
    separate-output-thread:
      description: Run output handling in separate thread.
      csv_column: ''
      group: ''
      dependency: ''
      test_range: ['separate-output-thread']
    multimode:
      description: Temporary testing multiple decoder instances in multi-thread mode
        or multi-process mode (only valid for g2dec). [0] Specify decoders running
        in parallel in multi-thread or multi- process mode. 0 - Disable 1 - Multi-thread
        mode 2 - Multi-process mode (reserved)
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
      range: [0, 2]
    streamcfg:
      description: Specify the filename storing decoder options.
      csv_column: ''
      group: ''
      dependency: ''
      default: 'streamcfg.txt'
  RTL Simulation Options:
    csv_column: ''
    trace-files:
      description: Generate hardware trace files.
      csv_column: ''
      group: ''
      dependency: ''
    rtl-trace:
      description: Trace file format for RTL (extra CU ctrl).
      csv_column: ''
      group: ''
      dependency: ''
  Common Output Options:
    csv_column: 'io_cfg'
    disable-display-order:
      description: Output in decoding order(useless under pp standalone mode).
      csv_column: ''
      group: ''
      dependency: 'md5'
      test_range: ['disable-display-order']
    align:
      description: Set the stride alignment of output buffer to n bytes. [16] [1,8,16,32,64,128,256,512,1024,2048]
      csv_column: ''
      group: ''
      dependency: 'md5'
      default: 16
      range: [1,8,16,32,64,128,256,512,1024,2048]
    no-write:
      description: Disable output writing.
      csv_column: ''
      group: ''
      dependency: 'md5'
      test_range: ['no-write']
    output-file:
      description: Write output to the specified file.
      csv_column: ''
      group: 'base'
      dependency: ''
      default:
    single-frames-out:
      description: Output single frames.
      csv_column: ''
      group: ''
      dependency: 'md5'
      test_range: ['single-frames-out']
    num-pictures:
      description: Stop after outputting [n] frames.
      csv_column: ''
      group: 'base'
      dependency: ''
      default: 100000
    md5:
      description: Write MD5 sum to output instead of yuv.
      csv_column: ''
      group: ''
      dependency: 'base'
      test_range: ['md5']
    md5-per-pic:
      description: 'Write MD5 sum for each picture to output instead of yuv. Note:
        -m (-M) not guarantee sync up with the third md5 tools.'
      csv_column: ''
      group: ''
      dependency: 'base'
      test_range: ['md5-per-pic']
  Logmsg Options:
    csv_column: 'debug_cfg'
    logoutlevel:
      description: 0..4 Control log message output level. [1] 0 - quiet 1 - error
        2 - info 3 - debug 4 - all
      csv_column: ''
      group: ''
      dependency: ''
      default: 1
      range: [0, 4]
    logtracemap:
      description: Indicate what kind of log should be output for debug. [API]
      csv_column: ''
      group: ''
      dependency: ''
      default: 'API'
      range: ['CFG', 'API', 'DWL', 'MEM', 'DPB', 'REGS', 'VCMD', 'IRQ', 'PERF', 'ALL']
    logoutdir:
      description: 0..3 Control log message output dir. [0] 0 - all log output to
        stdout 1 - all log use one log file 2 - each thread of instance has its own
        file 3 - all log output to stderr
      csv_column: ''
      group: ''
      dependency: ''
      default: 0
      range: [0, 3]
Decoding Flow Options:
  csv_column: 'vcd_cfg'
  low-latency:
    description: Enable low latency platform running flag.
    csv_column: ''
    group: ''
    dependency: ''
    test_range: ['low-latency']
  secure:
    description: Enable secure mode flag. not supported by TGU02.
    csv_column: ''
    group: ''
    dependency: ''
  mc:
    description: Enable multi-core decoding (frame-based for H264/HEVC/VVC/AVS2/AVS3,
      while tile-based for AV1/VP9). not supported by TGU02.
    csv_column: ''
    group: ''
    dependency: ''
  intra-only:
    description: Decode intra frames only.
    csv_column: ''
    group: ''
    dependency: ''
    test_range: ['intra-only']
  partial:
    description: Enable partial decoding. Not supported by VC9000D.
    csv_column: ''
    group: ''
    dependency: ''
Driver Selection Options:
  csv_column: ''
  dec-dev:
    description: Set decoder device file name.  (/tmp/dev/hantrodec)
    csv_column: ''
    group: ''
    dependency: ''
  mem-dev:
    description: Set memalloc device file name. (/tmp/dev/memalloc)
    csv_column: ''
    group: ''
    dependency: ''
Decoding Options:
  csv_column: 'vcd_cfg'
  compress-bypass:
    description: Bypass reference frame compression.
    csv_column: ''
    group: ''
    dependency: ''
    test_range: ['compress-bypass']
  num-buffers:
    description: 1..15 Set [n] frame buffers when initialization and then start decoding.
    csv_column: ''
    group: ''
    dependency: ''
    default: 1
    range: [1, 15]
    test_range: [1, 5, 15]
  skip-frame:
    description: Skip some frames when decoding. non_ref_recon - Decode all frame,
      but decoder HW doesn't write recon data of non-reference frames. Only for hevc
      & h264_high10. non_ref - Don't decode non-reference frames. none - Close this
      feature (don't skip anything).
    csv_column: ''
    group: ''
    dependency: ''
    range: ['non_ref_recon', 'non_ref', 'none']
  index-file:
    description: Set the length of data that demuxer transmits to decoder via index
      file. Set [path/to/file] as the path of index file. Use macro USE_DUMP_INPUT_STREAM
      to get index file. [1,8,16,32,64,128,256,512,1024,2048]
    csv_column: ''
    group: ''
    dependency: ''
  align-height:
    description: '[1,8,16,32,64,128] Set height aligned to n pixel lines.'
    csv_column: ''
    group: ''
    dependency: ''
    default: 1
    range: [1,8,16,32,64,128]
  auxinfo:
    description: 0..3 Specify auxiliary info dumping type (HEVC/H264). [0] 0 - Disable
      1 - QP dumping 2 - MV dumping 3 - Both QP and MV dumping
    csv_column: 'debug_cfg'
    group: ''
    dependency: ''
    default: 0
    range: [0, 3]
  tlayer:
    description: -1..7 Maximum Temporal Layer to be decoded. -1 means to decode all
      temporal layers.
    csv_column: ''
    group: ''
    dependency: ''
    default: -1
    range: [-1, 7]
    test_range: [-1, 0, 1, 7]
  add-buf-dyn:
    description: Enable the alloc flow of the output and dpb buffer dynamically, support
      all advanced formats of g2dec. Decode can work only one out buf added wihout
      pp, or one out buf add one dpb buf added if pp enable.
    csv_column: ''
    group: ''
    dependency: ''
    test_range: ['add-buf-dyn']
  Error Concealment Options:
    csv_column: ''
    error-ratio:
      description: Tolerable error ratio [n], the frame with an error ratio less than
        or equal to n% will be output. Only used when \-\-ec=tolerant_error mode.
      csv_column: ''
      group: ''
      dependency: 'ec_base'
      default: 50
    ec:
      description: 'Set error handling policy: tolerant_error - Balanced strategy
        ([1]) ignore_error - Fluency first strategy [2] no_error - Clarity first strategy
        [3]'
      csv_column: ''
      group: 'ec_base'
      dependency: ''
      default: tolerant_error
      range: ['tolerant_error', 'ignore_error', 'no_error']
      test_range: ['tolerant_error', 'ignore_error', 'no_error']
    dis-ec:
      description: Disable HW error concealment (currently for HEVC/H264/VVC only).
      csv_column: ''
      group: ''
      dependency: ''
      test_range: ['dis-ec']
    dis-slice:
      description: Disable slice decoding (currently for HEVC/H264/VVC only).
      csv_column: ''
      group: ''
      dependency: ''
      test_range: ['dis-slice']
Post Processing Options:
  csv_column: 'pp_cfg'
  Crop/Scale Options:
    csv_column: ''
    crop-win:
      description: 1..4 Specifies the ID of the cropping region which the following
        options set(only supported under pp standalone mode). [1] E.g., the following
        options set cropping region 3
      csv_column: ''
      group: 'pp_standalone_cfg'
      dependency: ''
      default: 1
      range: [1, 4]
    crop:
      description: 'For long options, s is in format like wxh@(x,y) crop a rectangle
        with size wxh from (x,y), where x, y, w, h are all integers. For short option,
        it''s one of the 4 options below, specifying either the offset or width /
        height of the cropped window. -Cx[n] - left offset [n] from left boundary
        -Cy[n] - top offset [n] from top boundary -Cw[n] - width of the cropping window
        is [n] -Ch[n] - height of the cropping window is [n] E.g., -Cx8 -Cy16 : Crop
        from (8, 16) -Cw720 -Ch480 : Crop size  720x480'
      csv_column: ''
      group: ''
      dependency: ''
      default: 720x480@(16, 32)
    down-scale:
      description: 'Set down scale ratios. (same as --scale=[-dx<:y>]) x, y are integers
        from [1,127], which means to be scaled to 1/x, 1/y in horizontal/vertical
        respectively. E.g.,: -d1 : Just enable pp without down-scaling (1:1 output)
        -d2 : Down-scaling to 1/2 in both horizontal/vertical directions -d2:4 : Down-scaling
        to 1/2 in horizontal direction, and 1/2 in vertical;maximum downscaling ratio for TGU02: 1/32'
      csv_column: ''
      group: ''
      dependency: 'pp-filter'
      default: 1
      test_range: ['3', '32', '2', '1', '2:4']
    scale:
      description: '[s] is in format wxh, specifying PP output size wxh (either by
        down-scaling or up-scaling), where both w and h are integers. E.g., -D1280x720
        : Set PP output size to 1280x720; maximum upscaling ratio for TGU02: 3x'
      csv_column: ''
      group: ''
      dependency: 'antialias'
      default:
      test_range: ['1920x1080', '8192x8192']
    second-crop:
      description: Enable the crop after scale, followed by crop parameter -C[s]. 
                   not supported by TGU02
      csv_column: ''
      group: ''
      dependency: 'crop'
  Filter Options:
    csv_column: ''
    pp-filter:
      description: Specify filtering algothrim for scaling. [LANCZOS] VSI_LINEAR LANCZOS
        NEAREST BILINEAR BICUBIC SPLINE BOX AREA
      csv_column: ''
      group: ''
      dependency: ''
      default: LANCZOS
      test_range: ['LANCZOS', 'VSI_LINEAR', 'NEAREST', 'BILINEAR', 'BICUBIC', 'SPLINE', 'BOX', 'AREA']
    filter-param:
      description: Specify the fitering window size mxn. (ONLY 2x2 is allowed).
      csv_column: ''
      group: ''
      dependency: ''
      default: 2x2
    pp-src-sel:
      description: Specify how to select the source for scaling. [DOWN_ROUND] DOWN_ROUND
        NO_ROUND UP_ROUND
      csv_column: ''
      group: ''
      dependency: ''
      default: DOWN_ROUND
      test_range: ['DOWN_ROUND', 'NO_ROUND', 'UP_ROUND']
    scaling-pad-yuv:
      description: Set the values for source pixels out of picture when scaling.
      csv_column: ''
      group: ''
      dependency: ''
      test_range: ['"(128,128,128)"']
    antialias:
      description: 'Specifies whether to enable anti-aliasing effect for scaling.
        [1] Valid values: 0 - disables anti-aliasing by using a fixed bicubic or bilinear
        filter size. This is the similiar behaviour to OpenCV and TorchVision. 1 -
        enables anti-aliasing by using a flexible bicubic or bilinear filter size,
        which is relevant to scaling ratio. This is the similiar behaviour with FFmpeg.'
      csv_column: ''
      group: ''
      dependency: 'pp-filter'
      default: 1
      range: [0, 1]
  Output Format Options:
    csv_column: ''
    pp:
      description: Enable pp channel [0].
      csv_column: ''
      group: 'base'
      dependency: ''
      default: 0
    dec400-enable:
      description: Enable dec400 of current pp channel if dec400 is configured in.
      csv_column: 'dec400_cfg'
      group: ''
      dependency: ''
    dec400-align:
      description: 'Sets the alignment for DEC400 compressed data. Valid values: 32
        - 32-byte alignment. 64 - 64-byte alignment.'
      csv_column: 'dec400_cfg'
      group: ''
      dependency: ''
      range: [32, 64]
      test_range: []
    enable:
      description: 'Set hw output format to one of following: rs - Raster scan conversion
        (a.k.a., semi-planar) pack10 - YUV420pack10 for 10-bit and 12-bit stream p010
        - P010 format for 10-bit and 12-bit stream I010 - I010 format for 10-bit and
        12-bit stream L010 - L010 format for 10-bit and 12-bit stream 1010 - 1010
        format for 10-bit and 12-bit stream; 10-bit not supported by TGU02'
      csv_column: ''
      group: ''
      dependency: ''
      range: ['rs']
    force-8bits:
      description: Force output in 8 bits per pixel for 10-bit stream.
      csv_column: ''
      group: ''
      dependency: ''
      test_range: ['force-8bits']
    cr-first:
      description: PP outputs chroma in CrCb order
      csv_column: ''
      group: ''
      dependency: ''
      test_range: ['cr-first']
    pp-shaper:
      description: Enable shaper for pp <n>. (Default support)
      csv_column: ''
      group: ''
      dependency: ''
      default:
      test_range: ['pp-shaper']
    pp-shaper-dis:
      description: disable shaper for pp <n>. (Default support)
      csv_column: ''
      group: ''
      dependency: ''
      default: 
      test_range: ['pp-shaper-dis']
    pp-shaper-no-pad:
      description: Shaper no need to pad for frame right edge.
      csv_column: ''
      group: ''
      dependency: ''
      test_range: ['pp-shaper-no-pad']
    pp-ycbcr:
      description: Set the pp output chroma format. By default CbCr are stored in
      csv_column: ''
      group: ''
      dependency: ''
      test_range: ['YUV420', 'YUV444']
    pp-planar:
      description: Enable PP output in planar format. Default is semi-planar.
      csv_column: ''
      group: ''
      dependency: ''
      test_range: ['pp-planar']
    pp-tiled-out:
      description: Enable PP output in tile format. Default is 4x4 tile.
      csv_column: ''
      group: ''
      dependency: ''
    pp-luma-only:
      description: Enable PP output YUV400, a.k.a., monochroma.
      csv_column: ''
      group: ''
      dependency: ''
      test_range: ['pp-luma-only']
    pp-comp:
      description: Enable compression to PP output. not suported by TGU02
      csv_column: ''
      group: ''
      dependency: ''
    tiled-mode:
      description: Specify the tile mode/size. Always enabled for FBC. TILED8x8 TILED16x16
        TILED32x8 TILED128x2 TILED64x64 (a.k.a., SuperTileX, store RGB data), not supported by TGU02
      csv_column: ''
      group: ''
      dependency: ''
    pp-packed-mode:
      description: Specify the YUV packed mode for customed format. YUYV UYVY
      csv_column: ''
      group: ''
      dependency: ''
      test_range: ['YUYV', 'UYVY']
    out_stride:
      description: 'Set PP stride of y/c plane to [n]. For short option, [s] is one
        of y[n] or c[n]. E.g., -sy720 -sc360 : Set stride of luma/chroma to 720 and
        360.'
      csv_column: ''
      group: ''
      dependency: ''
      default: yc16
    lc-stripe:
      description: Set the lines number increasement to trigger updating to line counter
        register. Set n = 0 to disable line count update.
      csv_column: 'debug_cfg'
      group: ''
      dependency: ''
      default: 0
  Delogo Options (Optional):
    csv_column: 'Delogo'
    delogo:
      description: '0..1 Specify the delogo area. There are two delogo area supported.
        Need to configure following parameters for a given delogo area:'
      csv_column: ''
      group: ''
      dependency: ''
      range: [0, 1]
    pos:
      description: 'Set delog rectangle area parameter, which is in format wxh@(x,y),
        - w: The width of the delogo area. - h: The height of the delogo area. - x:
        The offset of the delogo area to the left image edge. - y: The offset of the
        delogo area to the top image edge.'
      csv_column: ''
      group: ''
      dependency: ''
    show:
      description: 0..1 Show the delogo border. 0 Hide the delogo border. 1 Show the
        delogo border.
      csv_column: ''
      group: ''
      dependency: ''
      range: [0, 1]
    mode:
      description: Select the delogo mode. PIXEL_INTERPOLATION - Delogo with interpolated
        pixels.
      csv_column: ''
      group: ''
      dependency: ''
    YUV:
      description: Set the replacing value if PIXEL_REPLACE mode is enabled.
      csv_column: ''
      group: ''
      dependency: ''
  YUV2RGB Options:
    csv_column: ''
    pp-rgb:
      description: Enable compact YUV2RGB.
      csv_column: ''
      group: ''
      dependency: ''
      test_range: ['pp-rgb']
    pp-rgb-planar:
      description: Enable planar YUV2RGB, with each component in separted planes.
      csv_column: ''
      group: ''
      dependency: ''
      test_range: ['pp-rgb-planar']
    rgb-fmat:
      description: Set the RGB output format. RGB888/BGR888 (Packed mode) R16G16B16/B16G16R16
        (Packed mode) RGB888_P/BGR888_P (Planar mode) R16G16B16_P/B16G16R16_P (Planar
        mode) ARGB888/ABGR888/RGBA888/BGRA888 (Packed mode) A2R10G10B10/A2B10G10R10
        (Packed mode) R10G10B10A2/B10G10R10A2 (Packed mode) XRGB888/XBGR888 (Packed
        mode) X2R10G10B10/X2B10G10R10 (Packed mode)
      csv_column: ''
      group: ''
      dependency: ''
      range: ['RGB888', 'BGR888', 'RGB888_P', 'BGR888_P', 'ARGB888', 'ABGR888', 'RGBA888', 'BGRA888', 'XRGB888', 'XBGR888']
    rgb-std:
      description: Set the standard coeff to do YUV2RGB. BT601 BT601_L BT709 BT709_L
        BT2020 BT2020_L
      csv_column: ''
      group: ''
      dependency: ''
      range: ['BT601', 'BT601_L', 'BT709', 'BT709_L', 'BT2020', 'BT2020_L']
    rgb-alpha:
      description: Set the alpha value to [n] when output is in ARGB format.
      csv_column: ''
      group: ''
      dependency: 'rgb-fmat'
      default: 128
  Range Mapping:
    csv_column: ''
    source-range:
      description: 'Sets the source YUV or RGB range for range mapping. Valid values:
        FULL and LIMITED.'
      csv_column: ''
      group: ''
      dependency: ''
      test_range: ['FULL', 'LIMITED']
    target-range:
      description: 'Sets the target YUV or RGB range for range mapping. Valid values:
        FULL and LIMITED.'
      csv_column: ''
      group: ''
      dependency: ''
      test_range: ['FULL', 'LIMITED']
  3D LUT:
    csv_column: ''
    3dlut:
      description: Enables 3D LUT based color remapping for RGB output.
      csv_column: ''
      group: ''
      dependency: ''
    table-3dlut:
      description: 'Specifies the path to the 3D LUT table file. The 3D LUT table
        must be a .txt file that contains 17x17x17 lines, with each line in the following
        format: index0,index1,index2:R,G,B, The index increase order is that index2
        (B dimension) first increases from 0 to 16, then index1 (G dimension) increases
        from 0 to 16, and finally index0 (R dimension) increases from 0 to 16.'
      csv_column: ''
      group: ''
      dependency: ''
  PVFBC:
    csv_column: ''
    libfile:
      description: Specifies the input PVFBC libfile. The file is used only for software
        testing.
      csv_column: ''
      group: ''
      dependency: ''
    const-pix:
      description: Sets the constant pixel value in format luma:luma:chroma:chroma.
        For example, you can specify -\-const-pix=100:20:128:200.
      csv_column: ''
      group: ''
      dependency: ''
Format Specific Options:
  csv_column: 'codec_cfg'
  H264 Only Features:
    csv_column: ''
    mvc:
      description: Enable MVC decoding (H264 only).
      csv_column: ''
      group: ''
      dependency: ''
      test_range: ['mvc']
    is-asofmo:
      description: Input file may include ASO/FMO feature.
      csv_column: ''
      group: ''
      dependency: ''
      test_range: ['is-asofmo']
  AV1 Only Features:
    csv_column: ''
    oppoint:
      description: Just decode operating point 0. If not specified, all the layers
        will be decoded.
      csv_column: ''
      group: ''
      dependency: ''
  JPEG Only Features:
    csv_column: ''
    full-only:
      description: Force to full resolution decoding only and ignore thumbnail.
      csv_column: ''
      group: ''
      dependency: ''
      test_range: ['full-only']
    ri-mc:
      description: Enable restart interval based multicore decoding.
      csv_column: ''
      group: ''
      dependency: ''
    instant-buffer:
      description: Output buffer provided by user. (History Customized)
      csv_column: ''
      group: ''
      dependency: ''
  VP8 Only Features:
    csv_column: ''
    snap-shot:
      description: Set decode format to webp.
      csv_column: ''
      group: ''
      dependency: ''
    extra-bits:
      description: Add n bytes of extra space after stream buffer for decoder.
      csv_column: ''
      group: ''
      dependency: ''
    user-mem-alloc:
      description: User allocates picture buffers.
      csv_column: ''
      group: ''
      dependency: ''
  VP6 Only Features:
    csv_column: ''
    alpha:
      description: Stream contains alpha channel.
      csv_column: ''
      group: ''
      dependency: ''
  MPEG4 Only Features:
    csv_column: ''
    strm-sorenson:
      description: Decode Sorenson Spark stream.
      csv_column: ''
      group: ''
      dependency: ''
    strm-custom:
      description: Decode DivX4 or DivX5 stream.
      csv_column: ''
      group: ''
      dependency: ''
    custom:
      description: Set DivX3 stream resolution. [s] is in format wxh, with w, h both
        integers.
      csv_column: ''
      group: ''
      dependency: ''
  VC1 Only Features:
    csv_column: ''
    long-stream:
      description: Enable support for long streams.
      csv_column: ''
      group: ''
      dependency: ''
    frame-picture:
      description: Enable frame picture writing in multiresolutin output.
      csv_column: ''
      group: ''
      dependency: ''
  RV Only Features:
    csv_column: ''
    rv-input:
      description: Specify that input file is in RealVideo format.
      csv_column: ''
      group: ''
      dependency: ''
Post Processing (Standalone) Testbenh Features:
  csv_column: 'pp_standalone_cfg'
  Testbench Input Options:
    csv_column: ''
    in-stride:
      description: Set input stream stride to n bytes.
      csv_column: ''
      group: ''
      dependency: ''
    width:
      description: Set input stream width to n pixels (MUST).
      csv_column: ''
      group: ''
      dependency: ''
    height:
      description: Set input stream height to n pixels (MUST).
      csv_column: ''
      group: ''
      dependency: ''
  Input Format Options:
    csv_column: ''
    in-fmt:
      description: 'Specify input format: NV12, NV21, 420P, 422YUYV, 422YVYU, 422UYVY,
        422VYUY NV12_P010, NV21_P010, 420P_P010 TILED4x4, TILED4x4_P010 TILED8x8,
        TILED8x8_P010 (Only supported in dec400d) TILED8x8_YUV400, TILED8x8_P010_YUV400
        (Only supported in dec400d) TILED64x64_ARGB888 (Only supported in dec400d)
        TILED64x64_A2R10G10B10 (Only supported in dec400d) RGB888/BGR888 (Packed mode)
        RGB888_P/BGR888_P (Planar mode) ARGB888/ABGR888 (Packed mode) A2R10G10B10/A2B10G10R10
        (Packed mode) X2R10G10B10/X2B10G10R10 (Packed mode) R16G16B16_P/B16G16R16_P
        (Planar mode)'
      csv_column: ''
      group: ''
      dependency: ''
    inI010:
      description: Specify input YUV in I010 format (10-bit pixel in LSB of 16 bits).
      csv_column: ''
      group: ''
      dependency: ''
      range: [10, 10]
    in400:
      description: Specify input chroma format in 400.
      csv_column: ''
      group: ''
      dependency: ''
    in420:
      description: Specify input chroma format in 420.
      csv_column: ''
      group: ''
      dependency: ''
  DEC400 Input Options:
    csv_column: 'dec400_cfg'
    indec400:
      description: Specify input dec400 data.
      csv_column: ''
      group: ''
      dependency: ''
    indec400-align:
      description: '[32,64] Alignment set for dec400d: 32 - 32B 64 - 64B'
      csv_column: ''
      group: ''
      dependency: ''
      default: 32,64
      range: [32, 64]
      test_range: ['32,64']
    TS:
      description: Specify input ts table for dec400 decompress.
      csv_column: ''
      group: ''
      dependency: ''
  RFC Input Options:
    csv_column: ''
    IL:
      description: Specify input luma RFC data.
      csv_column: ''
      group: ''
      dependency: ''
    IC:
      description: Specify input chroma RFC data.
      csv_column: ''
      group: ''
      dependency: ''
    SL:
      description: 'Set input luma RFC data stride. Luma_stride calculation: align
        = alignment of input data pixel_width = (bit_depth_luma == 8 && bit_depth_chroma
        == 8) ? 8 : 10; luma_stride = NextMultiple(8 * frame_width * pixel_width ,
        align * 8) / 8;'
      csv_column: ''
      group: ''
      dependency: ''
    SC:
      description: 'Set input chroma RFC data stride. Chroma_stride calculation: align
        = alignment of input data pixel_width = (bit_depth_luma == 8 && bit_depth_chroma
        == 8) ? 8 : 10; chroma_stride = NextMultiple(4 * frame_width * pixel_width,
        align * 8) / 8;'
      csv_column: ''
      group: ''
      dependency: ''
    LT:
      description: 'Specify input luma table file. Luma table size calculation: pic_width_in_cbs
        = (pic_width + 8 - 1) / 8; pic_height_in_cbs = pic_height / 8; table_stride
        = NextMultiple(pic_width_in_cbs, 16); luma_table_size = table_stride * pic_height_in_cbs;'
      csv_column: ''
      group: ''
      dependency: ''
      range: [8, 8]
    CT:
      description: 'Specify input chroma table file. Chroma table size calculation:
        pic_width_in_cbs = (pic_width + 16 - 1) / 16 pic_height_in_cbs =  pic_height
        / 8  ; table_stride = NextMultiple(pic_width_in_cbs, 16); chroma_table_size
        = table_stride * pic_height_in_cbs;'
      csv_column: ''
      group: ''
      dependency: ''
      range: [16, 16]
    AT:
      description: Specify input luma table + input chroma table.
      csv_column: ''
      group: ''
      dependency: ''
    LB:
      description: Specify input luma bitdepth.
      csv_column: ''
      group: ''
      dependency: ''
    CB:
      description: Specify input chroma bitdepth.
      csv_column: ''
      group: ''
      dependency: ''
  Normalization:
    csv_column: ''
    norm:
      description: Enables normalization for RGB or monochroma output.
      csv_column: ''
      group: ''
      dependency: ''
    norm-filter:
      description: 'Specifies the normalization algorithm. Valid values: ZSCORE -
        normal z-score normalization. VSI_ZSCORE - z-score normalization with a scaling
        factor'
      csv_column: ''
      group: ''
      dependency: ''
    norm-fmat:
      description: 'Specifies the normalized output precision. Valid values: FP32,
        FP16, INT16, INT8, and UINT8.'
      csv_column: ''
      group: ''
      dependency: ''
    norm-mean:
      description: Specifies the mean value. For example, you can specify -\-norm-mean=(0.485,0.456,0.406).
      csv_column: ''
      group: ''
      dependency: ''
    norm-std:
      description: Specifies the standard value. For example, you can specify -\-norm-std=(0.229,0.224,0.225).
      csv_column: ''
      group: ''
      dependency: ''
    norm-quant-step:
      description: Specifies the quantization step. For example, you can specify -\-norm-quant-step=(0,255).
      csv_column: ''
      group: ''
      dependency: ''
