--- vc9000e.yaml	2025-05-30 03:05:49.633668452 +0000
+++ /root/workspace/code/vpu_testsuit/tools/yaml_gen/gen_yaml_for_csv/vc9000e_vcenc.yaml	2025-05-29 02:24:04.811041326 +0000
@@ -1,44 +1,40 @@
 Encoder Input and Output:
-  csv_column: ''
+  csv_column: 'io_cfg'
   input:
     description: The file from which the video encoder reads input video sequence.
       [input.yuv]
     csv_column: ''
-    group: ''
+    group: 'base'
     dependency: ''
-    default: input.yuv
+    default: qcif_176x144_420.yuv
   output:
     description: The file to which the video encoder writes output stream. [stream.hevc]
     csv_column: ''
-    group: ''
+    group: 'base'
     dependency: ''
-    default: stream.hevc
+    default: qcif_176x144_420.bin
   firstPic:
     description: The first picture to be encoded in the input file. [0]
     csv_column: ''
     group: ''
     dependency: ''
-    default: 0
   lastPic:
     description: The last picture to be encoded in the input file. [100]
     csv_column: ''
     group: ''
     dependency: ''
-    default: 100
   outReconFrame:
     description: Whether to output reconstructed frames. [1] 0 - do not output reconstructed
       frames. 1 - output reconstructed frames.
     csv_column: ''
-    group: ''
-    dependency: ''
-    default: 1
-    range: [0, 1]
+    group: 'recon'
+    dependency: 'base'
   inputRateNumer:
     description: 1..1048575 The numerator used to calculate the input frame rate.
       [30]
     csv_column: ''
-    group: ''
-    dependency: ''
+    group: 'FrameRate'
+    dependency: 'base'
     default: 30
     range: [1, 1048575]
     test_range: [30]
@@ -46,8 +42,8 @@
     description: 1..1048575 The denominator used to calculate the input frame rate.
       [1]
     csv_column: ''
-    group: ''
-    dependency: ''
+    group: 'FrameRate'
+    dependency: 'base'
     default: 1
     range: [1, 1048575]
     test_range: [1]
@@ -55,69 +51,67 @@
     description: 1..1048575 The numerator used to calculate the output frame rate.
       [Same as input]
     csv_column: ''
-    group: ''
-    dependency: ''
-    default: Same as input
+    group: 'FrameRate'
+    dependency: 'base'
+    default: '--inputRateNumer'
     range: [1, 1048575]
-    test_range: [Same as input]
+    test_range: ['--inputRateNumer']
   outputRateDenom:
     description: 1..1048575 The denominator used to calculate the output frame rate.
       [Same as input]
     csv_column: ''
-    group: ''
-    dependency: ''
-    default: Same as input
+    group: 'FrameRate'
+    dependency: 'base'
+    default: '--inputRateDenom'
     range: [1, 1048575]
-    test_range: [Same as input]
+    test_range: ['--inputRateDenom']
   writeReconToDDR:
     description: Whether to write reconstructed frames to DDR. [1] 0 - do not write
       reconstructed frames to DDR. 1 - write reconstructed frames to DDR. The option
       is valid only for I-frame only encoding.
     csv_column: ''
-    group: ''
-    dependency: ''
-    default: 1
-    range: [0, 1]
+    group: 'recon'
+    dependency: 'base'
   Input and Encoded Frame Resolutions and Cropping:
     csv_column: ''
     lumWidthSrc:
       description: The width of the input picture, in pixels. [176]
       csv_column: ''
-      group: ''
+      group: 'base'
       dependency: ''
       default: 176
     lumHeightSrc:
       description: The height of the input picture, in pixels. [144]
       csv_column: ''
-      group: ''
+      group: 'base'
       dependency: ''
       default: 144
     width:
       description: The width of the encoded picture, in pixels. [--lumWidthSrc]
-      csv_column: ''
-      group: ''
+      csv_column: 'pp_cfg'
+      group: 'vceInput'
       dependency: ''
-      default: --lumWidthSrc
+      default: 176
     height:
       description: The height of the encoded picture, in pixels. [--lumHeightSrc]
-      csv_column: ''
-      group: ''
+      csv_column: 'pp_cfg'
+      group: 'vceInput'
       dependency: ''
-      default: --lumHeightSrc
+      default: 144
     horOffsetSrc:
       description: The horizontal offset, in pixels, of the top-left corner of the
         encoded picture relative to the input picture. [0] The option value must be
         an even integer.
-      csv_column: ''
-      group: ''
+      csv_column: 'pp_cfg'
+      group: 'vceInput'
       dependency: ''
       default: 0
     verOffsetSrc:
       description: The vertical offset, in pixels, of the top-left corner of the encoded
         picture relative to the input picture. [0] The option value must be an even
         integer.
-      csv_column: ''
-      group: ''
+      csv_column: 'pp_cfg'
+      group: 'vceInput'
       dependency: ''
       default: 0
     inputFileList:
@@ -127,10 +121,11 @@
         -w: the picture width in pixels -h: the picture height in pixels -o: the path
         to the output file'
       csv_column: ''
-      group: ''
-      dependency: ''
+      group: 'inputFileList'
+      dependency: 'inputFileList'
+      default: inputFileList.txt
 Pre-Processing:
-  csv_column: ''
+  csv_column: 'pp_cfg'
   inputFormat:
     description: The input color format. [0] 0 - YUV420P8b_Raster_A16N 1 - YUV420SP8b_Raster_A16N
       2 - YVU420SP8b_Raster_A16N 3 - YUV422YUYV8b_Raster_A16N 4 - YUV422UYVY8b_Raster_A16N
@@ -162,8 +157,8 @@
       65 - Y10b_Raster_A16N 66 - YUV422P8b_Raster_A16N 67 - ARGB8888_Raster_A16N 68
       - ARGB2101010_Raster_A16N For details about the color formats, see "Hantro VC9x00E
       Series Memory Buffer and Format Organization".
-    csv_column: ''
-    group: ''
+    csv_column: 'io_cfg'
+    group: 'base'
     dependency: ''
     default: 0
     range: [0, 68]
@@ -184,12 +179,10 @@
     range: [0, 6]
   mirror:
     description: The mirroring mode to pre-process the input image. [0] 0 - do not
-      mirror 1 - mirror
+      mirror 1 - mirror ; mirror not supported by TGU02
     csv_column: ''
     group: ''
     dependency: ''
-    default: 0
-    range: [0, 1]
   rotation:
     description: The rotation mode to pre-process the input image. [0] 0 - do not
       rotate 1 - rotate 90 degrees clockwise 2 - roatate 90 degrees counterclockwise
@@ -211,42 +204,34 @@
     range: [0, 1]
   scaledWidth:
     description: 0..width The width of the down-scaled picture. [0] The value must
-      a multiple of 4.
+      a multiple of 4. (not supported by TGU02)
     csv_column: ''
     group: ''
     dependency: ''
-    default: 0
   scaledHeight:
     description: 0..height The height of the down-scaled picture. [0] The value must
-      a multiple of 4.
+      a multiple of 4.(not supported by TGU02)
     csv_column: ''
     group: ''
     dependency: ''
-    default: 0
   scaledOutputFormat:
     description: The color format of the output down-scaled picture. [0] 0 - YUV422
-      interleaved 1 - YUV420 semiplanar (NV12)
+      interleaved 1 - YUV420 semiplanar (NV12) (not supported by TGU02)
     csv_column: ''
     group: ''
     dependency: ''
-    default: 0
-    range: [0, 1]
   interlacedFrame:
     description: Whether the input frames are progressive or interlaced. [0] 0 - progressive
-      frame input 1 - interlaced framed input
+      frame input 1 - interlaced framed input (not supported by TGU02)
     csv_column: ''
     group: ''
     dependency: ''
-    default: 0
-    range: [0, 1]
   fieldOrder:
     description: The interlaced field order. [0] 0 - bottom field first 1 - top field
-      first
+      first (not supported by TGU02)
     csv_column: ''
     group: ''
     dependency: ''
-    default: 0
-    range: [0, 1]
   codedChromaIdc:
     description: The chroma sampling modes relative to luma sampling. [1] 0 - 4:0:0
       for HEVC (H.265) and H.264 (AVC) only 1 - 4:2:0 2 - 4:2:2 (under development)
@@ -257,13 +242,13 @@
     dependency: ''
     default: 1
     range: [0, 3]
+    test_range: [0, 1]
   scanType:
     description: The scan type of input image. [0] 0 - raster 1 - supertileX
+                 not supported by TGU02;
     csv_column: ''
     group: ''
     dependency: ''
-    default: 0
-    range: [0, 1]
   Input Conversion to Customized Formats:
     csv_column: ''
     formatCustomizedType:
@@ -276,11 +261,8 @@
       csv_column: ''
       group: ''
       dependency: ''
-      default: -1
-      range: [-1, 13]
-      test_range: [-1]
   DEC400 Compression Table (Tile Status):
-    csv_column: ''
+    csv_column: 'dec400_cfg'
     dec400TableInput:
       description: The file from which the video encoder reads the DEC400 compression
         table for video layer input. [dec400CompTableinput.bin]
@@ -340,7 +322,7 @@
       dependency: ''
       range: [0, 1]
   UFBC Parameters:
-    csv_column: ''
+    csv_column: 'ufbc_cfg'
     ufbcMode:
       description: The core mode of UFBC 0 - disable 1 - afbc (only support 32x8 pixels)
         - Version 1.0 2 - afbc (support 32x8 and 16x16 pixels) - Version 1.1 3 - dec400
@@ -406,7 +388,7 @@
 Stream Coding Tools:
   csv_column: ''
   Output Stream Format:
-    csv_column: ''
+    csv_column: 'bs_fmt_cfg'
     codecFormat:
       description: The video codec standard. [0] 0 or hevc - HEVC (H.265) 1 or h264
         - H.264 (AVC) 2 or av1 - AV1 3 or vp9 - VP9
@@ -454,36 +436,35 @@
       csv_column: ''
       group: ''
       dependency: ''
-      default: 180
+      default: 0xFFFF
       range: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 20, 21, 22, 30, 31, 32,
         40, 41, 42, 50, 51, 52, 60, 61, 62, 63, 90, 93, 99, 120, 123, 150, 153, 156,
         180, 183, 186]
-      test_range: [180]
+      test_range: [0xFFFF]
     tier:
       description: The stream tier for HEVC (H.265) or AV1. [0] 0 - Main tier 1 -
-        High tier
+        High tier; High tier is not supported by TGU02
       csv_column: ''
       group: ''
       dependency: ''
       default: 0
-      range: [0, 1]
     bitDepthLuma:
       description: The bit depth of luma samples in the encoded stream. [8] 8 - 8-bit
         luma samples 10 - 10-bit luma samples
       csv_column: ''
-      group: ''
+      group: 'bitDepth'
       dependency: ''
       default: 8
-      range: [8, 10]
+      range: ['8', '10']
     bitDepthChroma:
       description: The bit depth of chroma samples in the encoded stream. [8] 8 -
         8-bit Chroma samples 10 - 10-bit Chroma samples The value of bitDepthChroma
         must be the same as that of bitDepthLuma.
       csv_column: ''
-      group: ''
+      group: 'bitDepth'
       dependency: ''
       default: 8
-      range: [8, 10]
+      range: ['8', '10']
     byteStream:
       description: (Only for HEVC/H.264) The stream type. [1] 0 - NAL unit stream.
         NAL sizes are stored in the nal_sizes.txt file. 1 - Byte stream. For AV1,
@@ -551,12 +532,10 @@
       range: [0, 1]
     enableTS:
       description: Disable/Enable HEVC Transform Skip [0] 0 - disable hevc transform
-        skip 1 - enable hevc transform skip
+        skip 1 - enable hevc transform skip ; not supported by TGU02
       csv_column: ''
       group: ''
       dependency: ''
-      default: 0
-      range: [0, 1]
     log2MaxTSBlockSizeMinus2:
       description: 0..2 Max transform skip size minus 2 [0]
       csv_column: ''
@@ -670,12 +649,10 @@
       default: 0:16:12
     psyFactor:
       description: 0..4.0 The strength of psycho-visual encoding. <float> [0] If the
-        option is set to 0, psycho-visual encoding is disabled.
+        option is set to 0, psycho-visual encoding is disabled. not supported by TGU02
       csv_column: ''
       group: ''
       dependency: ''
-      default: 0
-      range: [0, '4.0']
     layerInRefIdc:
       description: (Only for H.264) Whether to enable layer information in the nal_ref_idc
         syntax element. 0 - disable. In this case, the value of nal_ref_idc can be
@@ -704,11 +681,10 @@
     enableTMVP:
       description: Whether to enable temporal motion vector prediction (TMVP) in inter
         prediction. 0 - disable 1 - enable The default value is 1 for HEVC, AV1, and
-        VP9 if TMVP is supported, and 0 in other cases.
+        VP9 if TMVP is supported, and 0 in other cases. not supported by TGU02
       csv_column: ''
       group: ''
       dependency: ''
-      range: [0, 1]
     Heifcfg:
       description: The path to the HEIF configuration file. ( Only support codec format
         HEVC and AV1 )
@@ -746,8 +722,8 @@
           0 - do not encode with a constant U or V value 1 - encode with a constant
           U or V value
         csv_column: ''
-        group: ''
-        dependency: ''
+        group: 'ConstChroma'
+        dependency: 'bitDepth'
         default: 0
         range: [0, 1]
       constCb:
@@ -757,9 +733,9 @@
           The default value is 512. This option is valid only if enableConstChroma
           is set to 1.
         csv_column: ''
-        group: ''
-        dependency: ''
-        range: [8, 10]
+        group: 'ConstChroma'
+        dependency: 'bitDepth'
+        range: [0, 255, 1023]
       constCr:
         description: The constant V value. The option value range for 8-bit encoding
           color space is from 0 to 255, inclusive. The default value is 128. The option
@@ -767,11 +743,11 @@
           The default value is 512. This option is valid only if enableConstChroma
           is set to 1.
         csv_column: ''
-        group: ''
-        dependency: ''
-        range: [8, 10]
+        group: 'ConstChroma'
+        dependency: 'bitDepth'
+        range: [0, 255, 1023]
   GOP Structure:
-    csv_column: ''
+    csv_column: 'gop_cfg'
     smoothPsnrInGOP:
       description: (Obsoleted) Whether to enable smooth PSNR for frames in a GOP.
         [0] 0 - disable 1 - enable This option is valid only if gopSize is set to
@@ -779,8 +755,6 @@
       csv_column: ''
       group: ''
       dependency: ''
-      default: 0
-      range: [0, 1]
     gopSize:
       description: 0..8 The size of the GOP structure. [0] If the option is set to
         0, the encoder adaptively selects a GOP structure size. If the option is set
@@ -791,15 +765,15 @@
       dependency: ''
       default: 0
       range: [0, 8]
-      test_range: [0]
+      test_range: [0, 8]
     gopMaxBSize:
       description: 0..7[default -255, disable] The GOP max B frame number for AGOP
       csv_column: ''
       group: ''
-      dependency: ''
-      default: default -255, disable
+      dependency: 'gopSize'
+      default: -255
       range: [0, 7]
-      test_range: ['default -255, disable']
+      test_range: [-255, 0, 1, 2, 3, 4, 5, 6, 7]
     gopConfig:
       description: 'The path to a custom GOP configuration file, which defines the
         GOP structure table. If the gopConfig option is not specified, the default
@@ -846,9 +820,9 @@
         field indicates the POC delta between two adjacent frames with the same LTR
         index.'
       csv_column: ''
-      group: ''
+      group: 'custom_gop'
       dependency: ''
-      default: -255
+      default: 'custom_gop.yaml'
     gopLowdelay:
       description: Whether to use the default low-delay GOP configuration. [0] 0 -
         do not use 1 - use
@@ -879,7 +853,7 @@
         1 for GOP size > 1 and 2pass
       csv_column: ''
       group: ''
-      dependency: ''
+      dependency: 'gopSize'
       range: [0, 1]
     bFrameQpDelta:
       description: -1..51 The QP delta of B-frames to the target QP. [-1] If the option
@@ -895,12 +869,10 @@
         - disable 1 - enable The option is valid for P or I frame encoding only. Since
         only one reference frame is stored, some coding tools are invalid, such as
         lookahead, reEncode, B frame, two reference P frame, multi-core and parallel
-        encoding, LTR.
+        encoding, LTR. not supported by TGU02
       csv_column: ''
       group: ''
-      dependency: ''
-      default: 0
-      range: [0, 1]
+      dependency: 'gopSize'
     enableLeadingPictures:
       description: enable leading pictures (RASL/RADL) [NONE]. NONE - disable RADL
         - enable RADL RASL - enable RASL BOTH - enable RADL+RASL
@@ -914,8 +886,9 @@
       group: ''
       dependency: ''
       default: 0
+      range: [0, 1]
   Rate Distortion Optimization:
-    csv_column: ''
+    csv_column: 'rdo_cfg'
     preset:
       description: The pre-defined RDO scheme. A higher value leads to higher quality
         but worse performance, and vice versa. To use this option, explicitly claim
@@ -930,6 +903,7 @@
       group: ''
       dependency: ''
       default: 1
+      range: [1, 3]
     enableDynamicRdo:
       description: Whether to enable dynamic RDO level selection. [0] 0 - disable
         1 - enable
@@ -987,7 +961,7 @@
   VUI and SEI:
     csv_column: ''
     SEI:
-      csv_column: ''
+      csv_column: 'sei_cfg'
       sei:
         description: (Only for HEVC/H.264) Whether to insert picture timing and buffering
           period SEI messages into the stream. [0] 0 - do not insert 1 - insert
@@ -1031,7 +1005,7 @@
         group: ''
         dependency: ''
     VUI:
-      csv_column: ''
+      csv_column: 'vui_cfg'
       vuiAspectRatio:
         description: 'The sample aspect ratio in the VUI. [0:0] - aspectratioWidth:
           The sample aspect ratio in horizontal direction, in an arbitrary unit. The
@@ -1088,7 +1062,7 @@
         range: [709, 2020]
         test_range: [2]
     HDR10:
-      csv_column: ''
+      csv_column: 'hdr10_cfg'
       writeOnceHDR10:
         description: Whether to write HDR10 information only before the first IDR
           frame. [0] 0 - write before each IDR frame. 1 - write only before the first
@@ -1126,7 +1100,7 @@
         group: ''
         dependency: ''
   (Obsoleted) Noise Reduction:
-    csv_column: ''
+    csv_column: 'nr_cfg'
     noiseReductionEnable:
       description: (Obsoleted) Whether to enable noise reduction (3DNR). [0] 0 - disable
         3DNR 1 - enable 3DNR
@@ -1206,7 +1180,7 @@
       range: [0, 15]
       test_range: [4]
   (Obsoleted) Smart Background Detection:
-    csv_column: ''
+    csv_column: 'sbd_cfg'
     smartConfig:
       description: (Obsoleted) The path to the custom configuration file for the smart
         algorithm.
@@ -1214,7 +1188,7 @@
       group: ''
       dependency: ''
   End-of-Sequence Disabling:
-    csv_column: ''
+    csv_column: 'eos_cfg'
     disableEOS:
       description: Whether to write end-of-sequence (EOS) bytes before stream is closed.
         [0] 0 - do not write 1 - write
@@ -1228,7 +1202,7 @@
     intraReconEnable:
       description: Enable/disable intra recon when HW support. [1] forced to 0 when
         HW_INTRARECON not supported. 0 = Disable intraRecon. 1 = Enable intraRecon.
-      csv_column: ''
+      csv_column: 'intraRecon_cfg'
       group: ''
       dependency: ''
       default: 1
@@ -1241,7 +1215,7 @@
       dependency: ''
       default: 0
   AV1 Verification for FFmpeg Framework:
-    csv_column: ''
+    csv_column: 'av1_cfg'
     modifiedTileGroupSize:
       description: (obsoleted)Whether to modify the space size of OBU_TILE_GROUP coded
         by the hardware. [0] 0 - do not modify 1 - modify Use this opion for AV1 verifiction
@@ -1265,7 +1239,7 @@
       default: 0
       range: [0, 1]
   Motion Estimation and Global Motion:
-    csv_column: ''
+    csv_column: 'me_cfg'
     gmvFile:
       description: '(Reserved) The path to a file that contains ME search range offsets
         for reference list 0. In the file, the search offsets of each frame must be
@@ -1325,7 +1299,7 @@
       dependency: ''
       default: 0
 Rate Control:
-  csv_column: ''
+  csv_column: 'rc_cfg'
   Rate Control Mode:
     csv_column: ''
     rcMode:
@@ -1333,9 +1307,10 @@
         rate (CVBR) mode The CVBR mode reduces encoded bits in slow-motion and simple
         scenes, and uses the reduced bits for complex scenes to improve overall quality.
       csv_column: ''
-      group: ''
+      group: 'rc_mode'
       dependency: ''
-      default: 0
+      default: cvbr
+      range: [cvbr, cbr, vbr, abr, crf, cqp]
   Output Stream Bit Rate:
     csv_column: ''
     intraPicRate:
@@ -1346,6 +1321,8 @@
       group: ''
       dependency: ''
       default: 0
+      range: [0, 10000000]
+      test_range: [0, 5, 30]
     intraPeriod:
       description: The interval between two non-IDR intra frames. [0] Starting from
         an IDR frame, the encoder forces a frame to be encoded as an intra frame every
@@ -1354,6 +1331,8 @@
       group: ''
       dependency: ''
       default: 0
+      range: [0, 10000000]
+      test_range: [0, 5, 30]
     bitPerSecond:
       description: The target bit rate for rate control, in bits per second. [1000000]
         The option value range is from 10000 to the maximum limited by the stream
@@ -1418,7 +1397,7 @@
         to 51, inclusive. If the option is set to -1, the constant rate factor mode
         is disabled.
       csv_column: ''
-      group: ''
+      group: 'rc_mode'
       dependency: ''
       default: -1
     picRc:
@@ -1426,7 +1405,7 @@
         pictures. [0] when rcMode = CVBR/CBR/VBR/ABR, picRc will be set to 1. 0 -
         disable 1 - enable
       csv_column: ''
-      group: ''
+      group: 'rc_mode'
       dependency: ''
       default: 0
       range: [0, 1]
@@ -1584,7 +1563,7 @@
       description: Whether to enable variable bit rate control based on the minimum
         QP allowed. [0]
       csv_column: ''
-      group: ''
+      group: 'rc_mode'
       dependency: ''
       default: 0
     sceneChange:
@@ -1893,18 +1872,16 @@
       default: 1
       range: [0, 1]
 Visual Quality:
-  csv_column: ''
+  csv_column: 'vq_cfg'
   Parameters Affecting Visual Tools:
     csv_column: ''
     ctbRcTrailStrengthMax:
       description: 0..3 Trailing area detection max strength. [0] 0 turns off trailing
         detection. Suggested value is [3] Only available with GOP size 1 and ctbRc
-        Version=2
+        Version=2; not supported by TGU02
       csv_column: ''
       group: ''
       dependency: ''
-      default: 0
-      range: [0, 3]
     ctbRcTrailDeltaQp:
       description: -7..0 Trailing area base qp delta. [-4] QP delta for 16x16 trailing
         CU, adjusted by CU size
@@ -1966,7 +1943,7 @@
       dependency: ''
       default: 0
   Dynamic Frame Rate:
-    csv_column: ''
+    csv_column: 'frame_rate_cfg'
     fpsAdjust:
       description: 'Frame rate update setting.(allow repeat) - frameCnt: at which
         frame to update - numer: updated frame rate numerator - denom: updated frame
@@ -1988,7 +1965,7 @@
       dependency: ''
       default: 1
 ROI:
-  csv_column: ''
+  csv_column: 'roi_cfg'
   ROI-Based Coding Control:
     csv_column: ''
     cir:
@@ -2341,7 +2318,7 @@
       default: 0
       range: [0, 1]
 Coding Statistics Output:
-  csv_column: ''
+  csv_column: 'statistics_cfg'
   enableOutputCuInfo:
     description: Whether to enable CU/MB statistics output to DDR. [0] 0 - disable
       1 - enable 2 - enable and write the statistics to the cuInfo.txt file.
@@ -2508,7 +2485,7 @@
 Peripheral and Hardware Setup:
   csv_column: ''
   Reference Frame Compression:
-    csv_column: ''
+    csv_column: 'rfc_cfg'
     compressor:
       description: Whether to enable embedded reference frame compression. [0] 0 -
         disable compression 3 - enable compression for both luma and chroma data
@@ -2516,42 +2493,40 @@
       group: ''
       dependency: ''
       default: 0
-      range: [0, 3]
+      range: ['0', '3']
     enableP010Ref:
       description: Whether to store reference frames in tiled or raster P010 format
-        in buffers. [0] 0 - tiled 1 - raster
+        in buffers. [0] 0 - tiled 1 - raster; raster is not supported by TGU02
       csv_column: ''
       group: ''
       dependency: ''
       default: 0
-      range: [0, 1]
   OSD & Mosaic:
     csv_column: ''
     OSD Control:
-      csv_column: ''
+      csv_column: 'osd_cfg'
       overlayEnables:
         description: 'Each bit indicates whether to enable the corresponding overlay
           region. [0] For example: - Value 1 indicates region 1 enabled. - Value 2
           indicates region 2 enabled. - Value 3 indicates regions 1 and 2 enabled.'
         csv_column: ''
-        group: ''
+        group: 'ol_input'
         dependency: ''
         default: 0
       olInputN:
         description: The path to the file that contains the input file to each OSD
           region. [olInputi.yuv]
         csv_column: ''
-        group: ''
+        group: 'ol_input'
         dependency: ''
         default: olInputi.yuv
       olFormatN:
         description: The input format of each OSD region. [0] 0 - ARGB8888 1 - NV12
           2 - bitmap
         csv_column: ''
-        group: ''
+        group: 'ol_input'
         dependency: ''
         default: 0
-        range: [0, 2]
       olSuperTileN:
         description: 'Whether the OSD input data is organized in supertile mode. [0]
           0 - non-supertile mode. 1 - X-major supertile mode. 2 - Y-major supertile
@@ -2559,13 +2534,11 @@
         csv_column: ''
         group: ''
         dependency: ''
-        default: 0
-        range: [0, 2]
       olAlphaN:
         description: 0..255 The global alpha value for the OSD region. [0] This option
           is invalid for ARGB8888.
         csv_column: ''
-        group: ''
+        group: 'ol_input'
         dependency: ''
         default: 0
         range: [0, 255]
@@ -2575,7 +2548,7 @@
           option must be specified for the region. The option value must be 8 aligned
           for the bitmap format.
         csv_column: ''
-        group: ''
+        group: 'ol_input'
         dependency: ''
         default: 0
       olHeightN:
@@ -2583,7 +2556,7 @@
           option must be specified for the region. The option value must be 8 aligned
           for the bitmap format.
         csv_column: ''
-        group: ''
+        group: 'ol_input'
         dependency: ''
         default: 0
       olXoffsetN:
@@ -2591,7 +2564,7 @@
           OSD region relative to the encoder picture. [0] The option value must be
           2 aligned.
         csv_column: ''
-        group: ''
+        group: 'ol_input'
         dependency: ''
         default: 0
       olYoffsetN:
@@ -2599,7 +2572,7 @@
           OSD region relative to the encoder picture. [0] The option value must be
           2 aligned.
         csv_column: ''
-        group: ''
+        group: 'ol_input'
         dependency: ''
         default: 0
       olYStrideN:
@@ -2607,13 +2580,13 @@
           value is (olWidthi * 4). For NV12, the default value is olWidthi For bitmap,
           the default value is (olWidthi/8).
         csv_column: ''
-        group: ''
+        group: 'ol_input'
         dependency: ''
       olUVStrideN:
         description: The UV stride of each OSD input, in bytes. The default value
           depends on the Y stride.
         csv_column: ''
-        group: ''
+        group: 'ol_input'
         dependency: ''
       olCropXoffsetN:
         description: The horizontal offset, in pixels, of the top-left corner of the
@@ -2621,7 +2594,7 @@
           option value must be 8 aligned for the bitmap format, and 2 aligned for
           other formats.
         csv_column: ''
-        group: ''
+        group: 'ol_input'
         dependency: ''
         default: 0
       olCropYoffsetN:
@@ -2630,7 +2603,7 @@
           option value must be 8 aligned for the bitmap format, and 2 aligned for
           other formats.
         csv_column: ''
-        group: ''
+        group: 'ol_input'
         dependency: ''
         default: 0
       olCropWidthN:
@@ -2638,37 +2611,37 @@
           of the final OSD region. [olWidthi] The option value must be 8 aligned for
           the bitmap format.
         csv_column: ''
-        group: ''
+        group: 'ol_input'
         dependency: ''
-        default: olWidthi
+        default: 64
       olCropHeightN:
         description: The height of the crop area in each OSD input, which is the height
           of the final OSD region. [olHeighti] The option value must be 8 aligned
           for the bitmap format.
         csv_column: ''
-        group: ''
+        group: 'ol_input'
         dependency: ''
-        default: olHeighti
+        default: 64
       olBitmapYN:
         description: The Y value for the bitmap format. [0]
         csv_column: ''
-        group: ''
-        dependency: ''
+        group: 'ol_bitmap'
+        dependency: 'ol_input'
         default: 0
       olBitmapUN:
         description: The U value for the bitmap format. [0]
         csv_column: ''
-        group: ''
-        dependency: ''
+        group: 'ol_bitmap'
+        dependency: 'ol_input'
         default: 0
       olBitmapVN:
         description: The V value for the bitmap format. [0]
         csv_column: ''
-        group: ''
-        dependency: ''
+        group: 'ol_bitmap'
+        dependency: 'ol_input'
         default: 0
     OSD Map:
-      csv_column: ''
+      csv_column: 'osd_map_cfg'
       osdMapEnable:
         description: The OSD map enable. [0] 0:disable 1:enable
         csv_column: ''
@@ -2721,7 +2694,7 @@
         dependency: ''
         default: 0
     Mosaic Control:
-      csv_column: ''
+      csv_column: 'mosaic_cfg'
       mosaicEnables:
         description: 'Each bit indicates whether to enable the corresponding mosaic
           region. [0] For example: - Value 1 indicates region 1 enabled. - Value 2
@@ -2831,7 +2804,7 @@
       range: [0, 255]
       test_range: [31]
   Input Frame and Reference Frame Buffer Alignment:
-    csv_column: ''
+    csv_column: 'alignment_cfg'
     inputAlignmentExp:
       description: The input frame buffer alignment. [6] 0 - alignment disabled 4..12
         - The base address and each line of the input frame buffer are aligned to
@@ -2882,7 +2855,7 @@
       range: [0, 15]
       test_range: [0]
   Output Stream Buffer:
-    csv_column: ''
+    csv_column: 'io_cfg'
     streamBufChain:
       description: 'Whether to enable one or two output stream buffers. [0] 0 - one
         output stream buffer 1 - two chained output stream buffers NOTE: The minimum
@@ -2924,7 +2897,7 @@
       range: [2, 16]
       test_range: [4]
   Parallel Flow Control:
-    csv_column: ''
+    csv_column: 'parallel_cfg'
     parallelCoreNum:
       description: 1..4 The number of cores that run in parallel at the frame level.
         [1] If the number of tile columns for picture division is set to 1
@@ -2933,6 +2906,7 @@
       dependency: ''
       default: 1
       range: [1, 4]
+      test_range: [1]
     batchEnable:
       description: 0..1 enable or disable multi-frame aggregation. [0] 0 - disable
         batch mode. 1 - enable batch mode.
@@ -2950,7 +2924,7 @@
       dependency: ''
       default: parallelCoreNum-1
   AXI Settings:
-    csv_column: ''
+    csv_column: 'axi_cfg'
     AXIAlignment:
       description: 'The AXI alignment in hexadecimal format. [data from fuse for each
         bit] - Bits 35:32: AXI_burst_align_wr_cuinfo - Bits 31:28: AXI_burst_align_wr_common
@@ -2975,7 +2949,7 @@
       dependency: ''
       default: 16
   IRQ Control:
-    csv_column: ''
+    csv_column: 'irq_cfg'
     IRQ Type Mask of Encoder:
       csv_column: ''
       irqTypeMask:
@@ -3060,7 +3034,7 @@
       range: [1, 16]
       test_range: []
 Debugging and Testing:
-  csv_column: ''
+  csv_column: 'debug_cfg'
   Debugging:
     csv_column: ''
     dumpRegister:
@@ -3084,7 +3058,7 @@
     multimode:
       description: The parallel running mode. [0] 0 - disable 1 - multi-thread mode
         2 - multi-process mode
-      csv_column: ''
+      csv_column: 'parallel_cfg'
       group: ''
       dependency: ''
       default: 0
