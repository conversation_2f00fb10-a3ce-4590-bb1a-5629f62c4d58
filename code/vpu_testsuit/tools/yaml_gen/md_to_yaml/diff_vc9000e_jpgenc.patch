--- vc9000e_jpeg.yaml	2025-05-30 03:12:24.338273556 +0000
+++ /root/workspace/code/vpu_testsuit/tools/yaml_gen/gen_yaml_for_csv/vc9000e_jpgenc.yaml	2025-05-29 10:49:04.375918354 +0000
@@ -8,59 +8,57 @@
 Pre-processing Frames:
   csv_column: ''
   Input Frame Resolutions and Cropping:
-    csv_column: ''
+    csv_column: 'io_cfg'
     input:
       description: Reads input from file. [input.yuv]
-      csv_column: ''
-      group: ''
+      csv_column: 'io_cfg'
+      group: 'base'
       dependency: ''
-      default: input.yuv
+      default: input_176x144_420.yuv
     firstPic:
       description: First picture of input file. [0]
-      csv_column: ''
-      group: ''
+      csv_column: 'io_cfg'
+      group: 'base'
       dependency: ''
-      default: 0
     lastPic:
       description: Last picture of input file. [0]
-      csv_column: ''
-      group: ''
+      csv_column: 'io_cfg'
+      group: 'base'
       dependency: ''
-      default: 0
     lumWidthSrc:
       description: Source image width. [176]
-      csv_column: ''
-      group: ''
+      csv_column: 'io_cfg'
+      group: 'base'
       dependency: ''
       default: 176
     lumHeightSrc:
       description: Source image height. [144]
-      csv_column: ''
-      group: ''
+      csv_column: 'io_cfg'
+      group: 'base'
       dependency: ''
       default: 144
     width:
       description: Output image width. [lumWidthSrc]
-      csv_column: ''
-      group: ''
+      csv_column: 'pp_cfg'
+      group: 'vceInput'
       dependency: ''
-      default: lumWidthSrc
+      default: 176
     height:
       description: Output image height. [lumHeightSrc]
-      csv_column: ''
-      group: ''
+      csv_column: 'pp_cfg'
+      group: 'vceInput'
       dependency: ''
-      default: lumHeightSrc
+      default: 144
     horOffsetSrc:
       description: Horizontal offset of output image. [0]
-      csv_column: ''
-      group: ''
+      csv_column: 'pp_cfg'
+      group: 'vceInput'
       dependency: ''
       default: 0
     verOffsetSrc:
       description: Vertical offset of output image. [0]
-      csv_column: ''
-      group: ''
+      csv_column: 'pp_cfg'
+      group: 'vceInput'
       dependency: ''
       default: 0
     write:
@@ -71,7 +69,7 @@
       default: 1
       range: [0, 1]
   Input Picture Format and Controls:
-    csv_column: ''
+    csv_column: 'pp_cfg'
     frameType:
       description: Input color format. [0] 0 - YUV420P8b_Raster_A16N (YUV 4:2:0 planar(IYUV/I420))
         1 - YUV420SP8b_Raster_A16N (YUV 4:2:0 semi-planar(NV12)) 2 - YVU420SP8b_Raster_A16N
@@ -86,8 +84,8 @@
         19 - YUV420P8b_Tile32x32_A16N 20 - YUV420P8b_Tile16x16_A16N 21 - YUV420SP8b_YuvSp4x4_A16N
         (YUV 4:2:0 semi-planar tile) 22 - YVU420SP8b_YuvSp4x4_A16N (YUV 4:2:0 semi-planar
         tile)
-      csv_column: ''
-      group: ''
+      csv_column: 'io_cfg'
+      group: 'base'
       dependency: ''
       default: 0
       range: [0, 22]
@@ -120,8 +118,6 @@
       csv_column: ''
       group: ''
       dependency: ''
-      default: 0
-      range: [0, 1]
     inputAlignmentExp:
       description: Alignment value of input frame buffer. [4] 0 - Disable alignment
         4 to 12 - Base address of input frame buffer and each line are aligned to
@@ -131,12 +127,12 @@
       dependency: ''
       default: 4
       range: [0, 12]
-      test_range: [4]
+      test_range: [0, 4, 12]
     enableConstChroma:
       description: 'Whether to set chroma to a constant pixel value. [0] Value range:
         0 and 1 0 - Disable 1 - Enable'
       csv_column: ''
-      group: ''
+      group: 'ConstChroma'
       dependency: ''
       default: 0
       range: [0, 1]
@@ -144,79 +140,79 @@
       description: 'Constant pixel value for the U component. [128]    <br> Value
         range: 0 to 255'
       csv_column: ''
-      group: ''
+      group: 'ConstChroma'
       dependency: ''
       default: 128
+      range: [0, 255]
     constCr:
       description: 'Constant pixel value for the V component. [128]    <br> Value
         range: 0 to 255'
       csv_column: ''
-      group: ''
+      group: 'ConstChroma'
       dependency: ''
       default: 128
+      range: [0, 255]
     scanType:
       description: The scan type of input image. [0] 0 - raster 1 - supertileX
       csv_column: ''
       group: ''
       dependency: ''
-      default: 0
-      range: [0, 1]
   OSD Overlay Control:
-    csv_column: ''
+    csv_column: 'osd_cfg'
     overlayEnables:
       description: 'Overlay region status, with 12 bits representing 12 regions respectively.
         [0] 1: Region 1 enabled 2: Region 2 enabled 3: Region 1 and 2 enabled and
         so on.'
       csv_column: ''
-      group: ''
+      group: 'ol_input'
       dependency: ''
       default: 0
     olInputN:
       description: Input file for overlay region. [olInputi.yuv]
       csv_column: ''
-      group: ''
+      group: 'ol_input'
       dependency: ''
       default: olInputi.yuv
     olFormatN:
       description: 'Overlay input format. [0] Value range: 0 to 2 0: ARGB8888 1: NV12
         2: Bitmap'
       csv_column: ''
-      group: ''
+      group: 'ol_input'
       dependency: ''
       default: 0
     olAlphaN:
       description: 'Global alpha value for NV12 and bitmap overlay format. [0] <br>
         Value range: 0 to 255'
       csv_column: ''
-      group: ''
+      group: 'ol_input'
       dependency: ''
       default: 0
     olWidthN:
       description: Overlay region width. It can only be set when a region is enabled.
         [0] It must be under eight-pixel aligned for bitmap format.
       csv_column: ''
-      group: ''
+      group: 'ol_input'
       dependency: ''
       default: 0
     olHeightN:
       description: Overlay region height. It can only be set when a region is enabled.
         [0]
       csv_column: ''
-      group: ''
+      group: 'ol_input'
       dependency: ''
       default: 0
     olXoffsetN:
       description: Horizontal offset of overlay region top-left pixel. [0] It must
         be two-pixel aligned. [0]
       csv_column: ''
-      group: ''
+      group: 'ol_input'
       dependency: ''
       default: 0
     olYoffsetN:
       description: Vertical offset of overlay region top left pixel. [0] It must be
         two-pixel aligned. [0]
       csv_column: ''
-      group: ''
+      group: 'ol_input'
       dependency: ''
       default: 0
     olYStrideN:
@@ -224,20 +220,19 @@
         range: [olWidthi * 4] when the format is ARGB8888. [olWidthi] when the format
         is NV12. [olWidthi / 8] when the format is bitmap.'
       csv_column: ''
-      group: ''
+      group: 'ol_input'
       dependency: ''
-      default: olWidthi * 4
     olUVStrideN:
       description: Chroma stride in bytes. The default value depends on the luma stride.
       csv_column: ''
-      group: ''
+      group: 'ol_input'
       dependency: ''
     olCropXoffsetN:
       description: Top left horizontal offset for OSD cropping. [0] For non-bitmap
         formats, it must be under two-pixel aligned. For bitmap format, it must be
         under eight-pixel aligned.
       csv_column: ''
-      group: ''
+      group: 'ol_input'
       dependency: ''
       default: 0
     olCropYoffsetN:
@@ -245,39 +240,39 @@
         it must be under two-pixel aligned. For bitmap format, it must be under eight-pixel
         aligned.
       csv_column: ''
-      group: ''
+      group: 'ol_input'
       dependency: ''
       default: 0
     olCropWidthN:
       description: OSD cropping width. [olWidthi] For bitmap format, it must be under
         eight-pixel aligned.
       csv_column: ''
-      group: ''
+      group: 'ol_input'
       dependency: ''
-      default: olWidthi
+      default: 64
     olCropHeightN:
       description: OSD cropping height. [olHeighti]
       csv_column: ''
-      group: ''
+      group: 'ol_input'
       dependency: ''
-      default: olHeighti
+      default: 64
     olBitmapYN:
       description: Y value of the OSD bitmap format. [0]
       csv_column: ''
-      group: ''
-      dependency: ''
+      group: 'ol_bitmap'
+      dependency: 'ol_input'
       default: 0
     olBitmapUN:
       description: U value of the OSD bitmap format. [0]
       csv_column: ''
-      group: ''
-      dependency: ''
+      group: 'ol_bitmap'
+      dependency: 'ol_input'
       default: 0
     olBitmapVN:
       description: V value of the OSD bitmap format. [0]
       csv_column: ''
-      group: ''
-      dependency: ''
+      group: 'ol_bitmap'
+      dependency: 'ol_input'
       default: 0
     olSuperTileN:
       description: Whether the OSD input data is organized in supertile mode. [0]
@@ -286,25 +281,23 @@
       csv_column: ''
       group: ''
       dependency: ''
-      default: 0
-      range: [0, 2]
     olScaleWidthN:
       description: The width of each OSD input. [0] If a region is enabled, this option
         must be specified for the region. The option value must be 8 aligned for the
         bitmap format.
       csv_column: ''
-      group: ''
-      dependency: ''
-      default: 0
+      group: 'ol_scale'
+      dependency: 'ol_input'
+      default: 128
     olScaleHeightN:
       description: The height of each OSD input. [0] If a region is enabled, this
         option must be specified for the region.
       csv_column: ''
-      group: ''
-      dependency: ''
-      default: 0
+      group: 'ol_scale'
+      dependency: 'ol_input'
+      default: 128
   Mosaic Area Control:
-    csv_column: ''
+    csv_column: 'mosaic_cfg'
     mosaicEnables:
       description: 'Enabling status of mosaic regions, with one bit for one region.
         (Not support Monochrome encoding if enable) [0] 1: Region 1 enabled 2: Region
@@ -327,7 +320,7 @@
       group: ''
       dependency: ''
   OSDMap Controls:
-    csv_column: ''
+    csv_column: 'osd_map_cfg'
     osdMapEnable:
       description: The OSD map enable. [0] 0:disable 1:enable
       csv_column: ''
@@ -379,7 +372,7 @@
       dependency: ''
       default: 1
   DEC400 Tile Status:
-    csv_column: ''
+    csv_column: 'dec400_cfg'
     dec400TableInput:
       description: DEC400 compressed table input from file. [dec400CompTableinput.bin]
       csv_column: ''
@@ -438,7 +431,7 @@
       dependency: ''
       range: [0, 1]
   UFBC Parameters:
-    csv_column: ''
+    csv_column: 'ufbc_cfg'
     ufbcMode:
       description: The core mode of UFBC. 0 - disable 1 - afbc (only support 32x8
         pixels) - Version 1.0 2 - afbc (support 32x8 and 16x16 pixels) - Version 1.1
@@ -502,7 +495,7 @@
       group: ''
       dependency: ''
 Coding Tools and Syntax Control:
-  csv_column: ''
+  csv_column: 'bs_fmt_cfg'
   Coding Modes and Tools:
     csv_column: ''
     restartInterval:
@@ -511,6 +504,7 @@
       group: ''
       dependency: ''
       default: 0
+      range: [0, 3]
     quality:
       description: Set quality factor instead of qLevel. [-1] -1 - use quantization
         table defined by qLevel or fixedQP. 1..100 - use quantization table generated
@@ -519,8 +513,8 @@
       group: ''
       dependency: ''
       default: -1
-      range: [1, 100]
-      test_range: [-1]
+      range: [-1, 100]
+      test_range: [-1, 1, 50, 100]
     qLevel:
       description: 'Quantization scale. [1] Value range: 0 to 10 The value 10 indicates
         the testbench-defined quantization table is used.'
@@ -539,7 +533,7 @@
     codingType:
       description: Encoding type. [0] 0 - Whole frame encoding 1 - Partial frame encoding
       csv_column: ''
-      group: ''
+      group: 'codingType'
       dependency: ''
       default: 0
       range: [0, 1]
@@ -565,79 +559,75 @@
       description: Unit of X- and Y-density. [0] 0 - Pixel aspect ratio 1 - Dots per
         inch 2 - Dots per centimetre
       csv_column: ''
-      group: ''
+      group: 'APP0_density'
       dependency: ''
       default: 0
       range: [0, 2]
     xdensity:
       description: X-density to APP0 header. [1]
       csv_column: ''
-      group: ''
+      group: 'APP0_density'
       dependency: ''
       default: 1
     ydensity:
       description: Y-density to APP0 header. [1]
       csv_column: ''
-      group: ''
+      group: 'APP0_density'
       dependency: ''
       default: 1
     inputThumb:
       description: Reads thumbnail input from file. [thumbnail.jpg]
       csv_column: ''
-      group: ''
+      group: 'thumbnail'
       dependency: ''
       default: thumbnail.jpg
     thumbnail:
       description: Thumbnail to stream. [0] 0 - NO 1 - JPEG 2 - RGB8 3 - RGB24
       csv_column: ''
-      group: ''
+      group: 'thumbnail'
       dependency: ''
       default: 0
       range: [0, 3]
     widthThumb:
       description: Thumbnail output image width. [32]
       csv_column: ''
-      group: ''
+      group: 'thumbnail'
       dependency: ''
       default: 32
     heightThumb:
       description: Thumbnail output image height. [32]
       csv_column: ''
-      group: ''
+      group: 'thumbnail'
       dependency: ''
       default: 32
     comLength:
       description: Comment header data length. [0]
       csv_column: ''
-      group: ''
+      group: 'comment_file'
       dependency: ''
       default: 0
     comFile:
       description: Comment header data file. [com.txt]
       csv_column: ''
-      group: ''
+      group: 'comment_file'
       dependency: ''
       default: com.txt
   Lossless Encoding:
     csv_column: ''
     lossless:
       description: 'Lossless encoding mode. [0]   <br> Value range: 0 to 7 0 - Disable
-        1 to 7 - Enable, with selected prediction modes 1 to 7'
+        1 to 7 - Enable, with selected prediction modes 1 to 7; not supported by TGU02' 
       csv_column: ''
       group: ''
       dependency: ''
-      default: 0
-      range: [0, 7]
-      test_range: [0]
     ptrans:
       description: 'Point transform value for lossless encoding. [0] <br> Value range:
-        0 to 7'
+        0 to 7; not supported by TGU02'
       csv_column: ''
       group: ''
       dependency: ''
-      default: 0
   ROI Map:
-    csv_column: ''
+    csv_column: 'roi_cfg'
     roimapFile:
       description: Input file for ROI map region. [NULL] NULL - Disable ROI Map. "roimap.roi"
         - text file name to describe ROI regions
@@ -659,7 +649,7 @@
       dependency: ''
       default: 5
   Motion JPEG and Rate Control:
-    csv_column: ''
+    csv_column: 'rc_cfg'
     mjpeg:
       description: Whether to enable motion JPEG [0] 0 - Disable 1 - Enable
       csv_column: ''
@@ -746,8 +736,8 @@
   csv_column: ''
   output:
     description: Writes output to file. [stream.jpg]
-    csv_column: ''
-    group: ''
+    csv_column: 'io_cfg'
+    group: 'base'
     dependency: ''
     default: stream.jpg
   streamBufChain:
@@ -766,7 +756,7 @@
     group: ''
     dependency: ''
     default: 0
-    range: [0, 1, 2, 32]
+    range: [0, 1, 2]
   Stream Multi-Segment for Output Low Latency:
     csv_column: ''
     streamMultiSegmentMode:
@@ -795,7 +785,7 @@
       default: 4
       range: [0, 0]
 Input Low Latency Mode:
-  csv_column: ''
+  csv_column: 'lowlatency_cfg'
   inputLineBufferMode:
     description: 'Input line buffer mode. [0] Value range: 0 to 4 0 = Disable 1 =
       Enable (software handshaking, loopback enabled) 2 = Enable (hardware handshaking,
@@ -815,22 +805,20 @@
       option. - When software handshaking is enabled, IRQ is sent and Read Count Register
       is updated each time a number of MCU rows specified by this option have been
       read. <b>Note</b>: This option can be set to 0 only when inputLineBufferMode
-      is set to 3. In this case, IRQ is not sent and Read Count Register is not updated.'
+      is set to 3. In this case, IRQ is not sent and Read Count Register is not updated.
+      loop-back not supported by TGU02'
     csv_column: ''
     group: ''
     dependency: ''
-    default: 1
-    range: [511, 511]
   inputLineBufferAmountPerLoopback:
     description: 'Line buffer amount in the case of buffer read/write address loopback.
-      [0] <br> Value range: 0 to 1023'
+      [0] <br> Value range: 0 to 1023; not supported by TGU02'
     csv_column: ''
     group: ''
     dependency: ''
-    default: 0
   segmentUnitHeight:
     description: 'Segment unit height when low latency is in SBI mode. [16] Value
-      range: 8 and 16'
+      range: 8 and 16; low latency mode need upstream IP support'
     csv_column: ''
     group: ''
     dependency: ''
@@ -846,7 +834,7 @@
 Hardware Control:
   csv_column: ''
   AXI Interface:
-    csv_column: ''
+    csv_column: 'axi_cfg'
     AXIAlignment:
       description: AXI alignment setting (in hexadecimal format). [0] bit[31:28] AXI_burst_align_wr_common
         bit[27:24] AXI_burst_align_wr_stream bit[23:20] AXI_burst_align_wr_chroma_ref
@@ -864,7 +852,7 @@
       dependency: ''
       default: 16
   IRQ Type:
-    csv_column: ''
+    csv_column: 'irq_cfg'
     irqTypeMask:
       description: IRQ type mask setting (in binary format). [11111110000] swreg1
         bit28 irq_type_burst_cnt_underflow_mask, default 1 swreg1 bit27 irq_type_burst0_req_mask,
@@ -898,7 +886,7 @@
       default: 1
       range: [0, 1]
 Debugging:
-  csv_column: ''
+  csv_column: 'debug_cfg'
   Dump Registers:
     csv_column: ''
     dumpRegister:
@@ -967,7 +955,6 @@
       csv_column: ''
       group: ''
       dependency: ''
-      default: osdDec400CompTableinput.bin
   vcmd priority and core bit mask:
     csv_column: ''
     priority:
