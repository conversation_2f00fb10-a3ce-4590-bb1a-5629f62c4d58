#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""Generate Mosaic YAML config files from a template, handling mosaic enables and area configurations."""

from __future__ import print_function
import os
import sys
import yaml
import re
import copy
from collections import OrderedDict

"""Usage: python <template_yaml> [--video-configs] """

# User configurable mosaic table - modify this to enable different mosaics
# Each entry represents which mosaics to enable (1-16), can enable multiple mosaics
MOSAIC_CONFIGS = [
    # Example configurations - modify as needed
    [1],           # Enable only mosaic 1
    [1, 2],        # Enable mosaics 1 and 2
    [1, 3, 5],     # Enable mosaics 1, 3, and 5
    [2, 4, 6, 8],  # Enable mosaics 2, 4, 6, and 8
    # Add more configurations as needed
]

# Video-specific mosaic configurations organized by categories
VIDEO_MOSAIC_CONFIGS = {
    'size': {
        'size_8x8': [1],                     # Single mosaic with 8x8 size
        'size_16x16': [1],                   # Single mosaic with 16x16 size
        'size_32x32': [1],                   # Single mosaic with 32x32 size
    },
    'area': {
        'area_small': [1],                   # Single mosaic with small area
        'area_medium': [1, 2],               # Two mosaics with medium area
        'area_large': [1, 2, 3, 4],          # Four mosaics with large area
    },
    'region': {
        'region_single': [1],                # Single mosaic region
        'region_quad': [1, 2, 3, 4],         # Four mosaic regions
        'region_full': list(range(1, 17)),   # All 16 mosaics enabled
    }
}

# Parameter configuration ranges and value extraction rules
PARAMETER_CONFIGS = {
    'mosSizeIndex': {
        'range': [0, 7],
        'keywords': {
            'small': 0,    # 8x8
            'medium': 1,   # 16x16
            'large': 2,    # 32x32
            'max': 7,
        }
    },
    'mosAreaN': {
        'range': ['0:0:8:8', '0:0:1920:1080'],
        'keywords': {
            'small': '0:0:8:8',
            'medium': '0:0:64:64',
            'large': '0:0:128:128',
            'full': '0:0:1920:1080',
        }
    },
    # Add more parameter ranges as needed
}

def extract_parameter_values(config_name):
    """Extract parameter values from config name based on keywords."""
    param_values = {}

    if not config_name:
        return param_values

    # Handle size parameters
    if 'size' in config_name:
        for keyword in ['small', 'medium', 'large', 'max']:
            if keyword in config_name:
                if 'mosSizeIndex' in PARAMETER_CONFIGS:
                    param_values['mosSizeIndex'] = PARAMETER_CONFIGS['mosSizeIndex']['keywords'].get(keyword, 0)
                break
        return param_values

    # Handle area parameters
    if 'area' in config_name:
        for keyword in ['small', 'medium', 'large', 'full']:
            if keyword in config_name:
                if 'mosAreaN' in PARAMETER_CONFIGS:
                    param_values['mosAreaN'] = PARAMETER_CONFIGS['mosAreaN']['keywords'].get(keyword, '0:0:8:8')
                break
        return param_values

    return param_values

def parse_area_string(area_str):
    """Parse area string like '0:0:8:8' into x, y, width, height."""
    try:
        parts = area_str.split(':')
        if len(parts) == 4:
            return int(parts[0]), int(parts[1]), int(parts[2]), int(parts[3])
    except (ValueError, IndexError):
        pass
    return 0, 0, 8, 8  # Default values

def calculate_mosaic_enables(mosaic_list):
    """Calculate mosaicEnables value based on list of enabled mosaics (1-16)."""
    enables = 0
    for mosaic_num in mosaic_list:
        if 1 <= mosaic_num <= 16:
            enables |= (1 << (mosaic_num - 1))  # bit 0 for mosaic 1, bit 1 for mosaic 2, etc.
    return enables

def generate_mosaic_keys(mosaic_list, template, config_name=None):
    """Generate mosaic key configurations for enabled mosaics."""
    config = copy.deepcopy(template)

    # Apply parameter configurations if config_name is provided
    if config_name:
        param_values = extract_parameter_values(config_name)
        for param_key, param_value in param_values.items():
            config[param_key] = param_value

    # Calculate mosaicEnables value
    config['mosaicEnables'] = calculate_mosaic_enables(mosaic_list)

    # Generate area configurations for each enabled mosaic
    base_area = template.get('mosAreaN', '0:0:8:8')
    x, y, width, height = parse_area_string(base_area)

    # Apply parameter-specific area if available
    if config_name:
        param_values = extract_parameter_values(config_name)
        if 'mosAreaN' in param_values:
            base_area = param_values['mosAreaN']
            x, y, width, height = parse_area_string(base_area)

    # Generate mosaic areas with automatic positioning to avoid overlap
    for i, mosaic_num in enumerate(mosaic_list):
        key_suffix = '{:02d}'.format(mosaic_num)  # 01, 02, ..., 16

        # Calculate position for this mosaic to avoid overlapping
        # Arrange in a grid pattern (4x4 for up to 16 mosaics)
        grid_x = i % 4
        grid_y = i // 4

        mosaic_x = x + grid_x * width
        mosaic_y = y + grid_y * height

        area_str = '{}:{}:{}:{}'.format(mosaic_x, mosaic_y, width, height)
        config['mosArea{}'.format(key_suffix)] = area_str

    # Remove template keys with N suffix
    keys_to_remove = [k for k in config.keys() if k.endswith('N')]
    for key in keys_to_remove:
        del config[key]

    return config

def ordered_load(stream):
    """Custom YAML loader that preserves field order using OrderedDict."""
    class OrderedLoader(yaml.SafeLoader):
        pass

    def construct_mapping(loader, node):
        loader.flatten_mapping(node)
        return OrderedDict(loader.construct_pairs(node))

    OrderedLoader.add_constructor(
        yaml.resolver.BaseResolver.DEFAULT_MAPPING_TAG,
        construct_mapping)

    return yaml.load(stream, OrderedLoader)

def ordered_dump(data, stream=None, **kwargs):
    """Custom YAML dumper that preserves field order and uses block style."""
    class OrderedDumper(yaml.SafeDumper):
        pass

    def _dict_representer(dumper, data):
        return dumper.represent_dict(data.items())

    OrderedDumper.add_representer(OrderedDict, _dict_representer)

    return yaml.dump(data, stream, Dumper=OrderedDumper, **kwargs)

def get_output_root(template_path, user_output_dir=None):
    """Determine output root directory based on user input or template file name (removing 'default')."""
    if user_output_dir:
        return os.path.abspath(user_output_dir)

    template_dir = os.path.dirname(os.path.abspath(template_path))
    base_name = os.path.basename(template_path)
    base_no_ext = os.path.splitext(base_name)[0]
    cleaned_name = base_no_ext.replace('default', '').strip('_-')
    return os.path.join(template_dir, cleaned_name)

def generate_video_configs(template_path, output_dir=None):
    """Generate YAML configs for video-specific mosaic configurations."""
    if not os.path.exists(template_path):
        print('Error: Template YAML not found: {}'.format(template_path))
        return

    with open(template_path, 'r') as f:
        try:
            template = ordered_load(f)
        except Exception as e:
            print('Error parsing YAML: {}'.format(e))
            return

    if output_dir is None:
        # Use template directory and append video suffix
        template_dir = os.path.dirname(os.path.abspath(template_path))
        output_root = os.path.join(template_dir, 'mosaic_cfg_mos_cfg_video')
    else:
        output_root = os.path.abspath(output_dir)

    if not os.path.exists(output_root):
        os.makedirs(output_root)

    # Generate configs for each video mosaic configuration category
    for category, configs in VIDEO_MOSAIC_CONFIGS.items():
        # Create category subdirectory with mosaic_cfg_ prefix
        category_dir_name = 'mosaic_cfg_{}'.format(category)
        category_output_dir = os.path.join(output_root, category_dir_name)
        if not os.path.exists(category_output_dir):
            os.makedirs(category_output_dir)

        for config_name, mosaic_list in configs.items():
            config = generate_mosaic_keys(mosaic_list, template, config_name)

            # Create output filename based on config name
            output_filename = 'mosaic_cfg_{}.yaml'.format(config_name)
            output_path = os.path.join(category_output_dir, output_filename)

            with open(output_path, 'w') as out_f:
                ordered_dump(config, out_f, default_flow_style=False)

            # Show mosaic info for each mosaic
            mosaic_info = []
            for mosaic_num in mosaic_list:
                key_suffix = '{:02d}'.format(mosaic_num)
                area = config.get('mosArea{}'.format(key_suffix), '0:0:8:8')
                mosaic_info.append('{}:{}'.format(mosaic_num, area))

            print('Generated: {} (mosaics: {}, enables: 0x{:04x})'.format(
                output_path, mosaic_list, config['mosaicEnables']))
            print('  Mosaic details: {}'.format(', '.join(mosaic_info)))

def generate_configs(template_path, output_dir=None):
    """Generate YAML configs for all mosaic configurations."""
    if not os.path.exists(template_path):
        print('Error: Template YAML not found: {}'.format(template_path))
        return

    with open(template_path, 'r') as f:
        try:
            template = ordered_load(f)
        except Exception as e:
            print('Error parsing YAML: {}'.format(e))
            return

    output_root = get_output_root(template_path, output_dir)
    if not os.path.exists(output_root):
        os.makedirs(output_root)

    # Generate configs for each mosaic configuration
    for i, mosaic_list in enumerate(MOSAIC_CONFIGS):
        config = generate_mosaic_keys(mosaic_list, template)

        # Create output filename based on enabled mosaics
        mosaic_str = '_'.join('{:02d}'.format(num) for num in sorted(mosaic_list))
        output_filename = 'mosaic_cfg_mosaic_{}.yaml'.format(mosaic_str)
        output_path = os.path.join(output_root, output_filename)

        with open(output_path, 'w') as out_f:
            ordered_dump(config, out_f, default_flow_style=False)

        # Show mosaic info for each mosaic
        mosaic_info = []
        for mosaic_num in mosaic_list:
            key_suffix = '{:02d}'.format(mosaic_num)
            area = config.get('mosArea{}'.format(key_suffix), '0:0:8:8')
            mosaic_info.append('{}:{}'.format(mosaic_num, area))

        print('Generated: {} (mosaics: {}, enables: 0x{:04x})'.format(
            output_path, mosaic_list, config['mosaicEnables']))
        print('  Mosaic details: {}'.format(', '.join(mosaic_info)))

def main():
    """CLI entry point for the script."""
    if len(sys.argv) < 2:
        print('Usage: {} <template_yaml> [options]'.format(sys.argv[0]))
        print('Example: {} mosaic_cfg_mos_cfg_default_video.yaml'.format(sys.argv[0]))
        print('')
        print('Options:')
        print('  --output-dir=<dir>     Specify output directory')
        print('  --video-configs        Generate video-specific configurations')
        print('')
        print('Supported mosaic parameters:')
        print('  mosaicEnables: Controls which mosaics are enabled (bit mask)')
        print('  mosSizeIndex: Mosaic size index (0=8x8, 1=16x16, 2=32x32, etc.)')
        print('  mosAreaN: Mosaic area in format x:y:width:height')
        print('')
        print('Parameter ranges:')
        for param_name, param_config in PARAMETER_CONFIGS.items():
            param_range = param_config.get('range', [])
            keywords = param_config.get('keywords', {})
            keyword_str = ', '.join(['{}: {}'.format(k, v) for k, v in keywords.items()])
            print('  {}: {} [{}]'.format(param_name, param_range, keyword_str))
        print('')
        print('Current mosaic configurations:')
        for i, mosaic_list in enumerate(MOSAIC_CONFIGS):
            enables = calculate_mosaic_enables(mosaic_list)
            print('  Config {}: mosaics {} (enables: 0x{:04x})'.format(i+1, mosaic_list, enables))
        print('')
        print('Video mosaic configurations:')
        for category, configs in VIDEO_MOSAIC_CONFIGS.items():
            print('  Category: {}'.format(category))
            for config_name, mosaic_list in configs.items():
                enables = calculate_mosaic_enables(mosaic_list)
                param_info = ""
                param_values = extract_parameter_values(config_name)
                if param_values:
                    param_list = ['{}: {}'.format(k, v) for k, v in param_values.items()]
                    param_info = " [{}]".format(', '.join(param_list))
                print('    {}: mosaics {} (enables: 0x{:04x}){}'.format(config_name, mosaic_list, enables, param_info))
        sys.exit(1)

    template_yaml = sys.argv[1]

    # Parse arguments
    output_dir = None
    video_configs = False

    for arg in sys.argv[2:]:
        if arg.startswith('--output-dir='):
            output_dir = arg.split('=', 1)[1]
        elif arg == '--video-configs':
            video_configs = True

    if video_configs:
        generate_video_configs(template_yaml, output_dir)
    else:
        generate_configs(template_yaml, output_dir)

if __name__ == '__main__':
    main()
