#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""Generate YAML config files from a template for each YUV sequence, preserving folder structure."""

from __future__ import print_function
import os
import sys
import yaml
import re
import copy
from collections import OrderedDict

def extract_resolution(filename):
    """Extract resolution (e.g., 1920x1080) from filename using regex."""
    match = re.search(r'(\d+)[xX](\d+)', filename)
    if match:
        return int(match.group(1)), int(match.group(2))
    return None, None

def extract_format(filename):
    """Extract pixel format keyword from filename using known format list."""
    # Put more specific formats first to ensure correct matching
    known_formats = [
        '420p10', '420jpeg', '420mpeg2', '420', '422', '444', 'mono',
        'i420', 'nv12', 'nv21', 'nv12tile', 'nv21tile', 'yuy2', 'uyvy',
        'i010', 'p010', 'yol2', 'p010tile', 'rgb444', 'rgb565', 'bgr565',
        'rgb888', 'rbg888', 'bgr888', 'brg888', 'gbr888', 'grb888',
        'xrgb8888', 'xbgr8888', 'x2r10g10b10', 'x2b10g10r10',
        'bgr101010', 'rgb101010', 'rgb555', 'xrgb4444', 'xbgr4444',
        'xrgb1555', 'xbgr1555', 'nv16', 'p210', 'p010be'
    ]
    pattern = r'(' + '|'.join(re.escape(f) for f in known_formats) + r')'
    match = re.search(pattern, filename, re.IGNORECASE)
    if match:
        return match.group(1).lower()
    return None

def get_input_format(format_str):
    """Map extracted format string to corresponding integer inputFormat value."""
    format_map = {
        '420': 0,
        '420mpeg2': 0,
        '420jpeg': 0,
        '422': 3,
        '444': 47,
        'mono': 55,
        '420p10': 15,
        'i420': 0,
        'nv12': 1,
        'nv16': 25,
        'p210': 46,
        'nv21': 2,
        'nv12tile': 21,
        'nv21tile': 22,
        'yuy2': 3,
        'uyvy': 4,
        'i010': 15,
        'p010': 16,
        'p010be': 16,
        'yol2': 18,
        'p010tile': 23,
        'rgb444': 9, # not listed in support list
        'rgb565': 5,
        'bgr565': 6,
        'rgb888': 40,
        'rbg888': 42,
        'bgr888': 41,
        'brg888': 44,
        'gbr888': 43,
        'grb888': 45,
        'xrgb8888': 11,
        'xbgr8888': 12,
        'x2r10g10b10': 13,
        'x2b10g10r10': 14,
        'bgr101010': 14, # not listed in support list
        'rgb101010': 13, # not listed in support list
        'rgb555': 7, # not listed in support list
        'xrgb4444': 9,
        'xbgr4444': 10,
        'xrgb1555': 7,
        'xbgr1555': 8
    }
    return format_map.get(format_str)

def resolve_references(config):
    """Resolve fields in the config that reference other fields using the syntax --<field>."""
    resolved = OrderedDict()
    for key, value in config.items():
        if isinstance(value, str) and value.startswith('--'):
            ref_key = value[2:]
            resolved[key] = config.get(ref_key, value)
        else:
            resolved[key] = value
    return resolved

def get_output_root(template_path, user_output_dir=None):
    """Determine output root directory based on user input or template file name (removing 'default')."""
    if user_output_dir:
        return os.path.abspath(user_output_dir)

    template_dir = os.path.dirname(os.path.abspath(template_path))
    base_name = os.path.basename(template_path)
    base_no_ext = os.path.splitext(base_name)[0]
    cleaned_name = base_no_ext.replace('default', '').strip('_-')
    return os.path.join(os.getcwd(), cleaned_name)

def ordered_load(stream):
    """Custom YAML loader that preserves field order using OrderedDict."""
    class OrderedLoader(yaml.SafeLoader):
        pass

    def construct_mapping(loader, node):
        loader.flatten_mapping(node)
        return OrderedDict(loader.construct_pairs(node))

    OrderedLoader.add_constructor(
        yaml.resolver.BaseResolver.DEFAULT_MAPPING_TAG,
        construct_mapping)

    return yaml.load(stream, OrderedLoader)

def ordered_dump(data, stream=None, **kwargs):
    """Custom YAML dumper that preserves field order and uses block style."""
    class OrderedDumper(yaml.SafeDumper):
        pass

    def _dict_representer(dumper, data):
        return dumper.represent_dict(data.items())

    OrderedDumper.add_representer(OrderedDict, _dict_representer)

    return yaml.dump(data, stream, Dumper=OrderedDumper, **kwargs)

def generate_config(template_path, sequence_dir, codec, output_base=None):
    """Generate YAML configs for all .yuv files in a directory tree based on a template."""
    if not os.path.exists(template_path):
        print('Error: Template YAML not found: {}'.format(template_path))
        return

    with open(template_path, 'r') as f:
        try:
            template = ordered_load(f)
        except Exception as e:
            print('Error parsing YAML: {}'.format(e))
            return

    output_root = get_output_root(template_path, output_base)

    for root, _, files in os.walk(sequence_dir):
        for fname in files:
            if not (fname.endswith('.yuv') or fname.endswith('.rgb')):
                continue

            abs_yuv_path = os.path.abspath(os.path.join(root, fname))
            rel_path = os.path.relpath(root, sequence_dir)
            width, height = extract_resolution(fname)

            if not width or not height:
                print('Warning: Could not extract resolution from {}'.format(fname))
                continue

            fmt = extract_format(fname)
            input_format = get_input_format(fmt)

            if input_format is None:
                print('Warning: Unknown inputFormat for file {} (format: {})'.format(fname, fmt))
                continue

            config = copy.deepcopy(template)
            config['input'] = fname
            config['output'] = '{}.{}'.format(os.path.splitext(fname)[0], codec)
            config['lumWidthSrc'] = width
            config['lumHeightSrc'] = height
            config['inputFormat'] = input_format

            config = resolve_references(config)

            output_dir = os.path.join(output_root, rel_path)
            if not os.path.exists(output_dir):
                os.makedirs(output_dir)

            output_filename = os.path.splitext(fname)[0] + '.yaml'
            output_path = os.path.join(output_dir, output_filename)

            with open(output_path, 'w') as out_f:
                ordered_dump(config, out_f, default_flow_style=False)

            print('Generated: {}'.format(output_path))

def main():
    """CLI entry point for the script. Validates arguments and launches config generation."""
    if len(sys.argv) not in (4, 5):
        print('Usage: {} <template_yaml> <sequence_dir> <codec> [output_dir]'.format(sys.argv[0]))
        sys.exit(1)

    template_yaml = sys.argv[1]
    sequence_dir = sys.argv[2]
    codec = sys.argv[3]
    output_dir = sys.argv[4] if len(sys.argv) == 5 else None

    generate_config(template_yaml, sequence_dir, codec, output_dir)

if __name__ == '__main__':
    main()
