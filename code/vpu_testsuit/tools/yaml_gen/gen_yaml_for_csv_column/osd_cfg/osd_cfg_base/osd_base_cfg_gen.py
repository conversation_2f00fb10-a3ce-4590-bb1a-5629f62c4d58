#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""Generate OSD YAML config files from a template, handling overlay enables and input file resolution extraction."""
"""Usage: python <template_yaml> [--video-configs] [--jpeg-configs] """


from __future__ import print_function
import os
import sys
import yaml
import re
import copy
from collections import OrderedDict

# User configurable overlay table - modify this to enable different overlays
# Each entry represents which overlays to enable (1-12), can enable multiple overlays
OVERLAY_CONFIGS = [
    # Example configurations - modify as needed
    [1],           # Enable only overlay 1
    [1, 2],        # Enable overlays 1 and 2
    [1, 3, 5],     # Enable overlays 1, 3, and 5
    [2, 4, 6, 8],  # Enable overlays 2, 4, 6, and 8
    # Add more configurations as needed
]

# Video-specific overlay configurations organized by categories
VIDEO_OVERLAY_CONFIGS = {
    'format': {
        'format_rgb888': [1],                    # Single overlay with RGB888 format
        'format_nv12': [1],                      # Single overlay with NV12 format
        'format_bmp': [1],                       # Single overlay with BMP format
    },
    'alpha': {
        'alpha_nv12_max_alpha': [1],            # Single overlay with NV12 format and max alpha
        'alpha_bmp_max_alpha': [1],             # Single overlay with BMP format and max alpha
        'alpha_nv12_min_alpha': [1],            # Single overlay with NV12 format and min alpha
        'alpha_bmp_min_alpha': [1],             # Single overlay with BMP format and min alpha
        'alpha_nv12_normal_alpha': [1],         # Single overlay with NV12 format and normal alpha
        'alpha_bmp_normal_alpha': [1],          # Single overlay with BMP format and normal alpha
    },
    'bitmap': {
        'bmp_min_bitmapYUV': [1],         # Single overlay with BMP format and bitmap yuv
        'bmp_normal_bitmapYUV': [1],      # Single overlay with BMP format and bitmap yuv
        'bmp_max_bitmapYUV': [1],      # Single overlay with BMP format and bitmap yuv
    },
    'region': {
        'region_full_overlay': [1, 2],                  # 2 overlays enabled
        'region_12': list(range(1, 13)),         # All 12 overlays enabled (same as full)
        'region_max_olCropSize': [1],                   # Max crop size
    }
}

JPEG_OVERLAY_CONFIGS = {
    'format': {
        'format_rgb888': [1],                    # Single overlay with RGB888 format
        'format_nv12': [1],                      # Single overlay with NV12 format
        'format_bmp': [1],                       # Single overlay with BMP format
    },
    'alpha': {
        'alpha_nv12_max_alpha': [1],            # Single overlay with NV12 format and max alpha
        'alpha_bmp_max_alpha': [1],             # Single overlay with BMP format and max alpha
        'alpha_nv12_min_alpha': [1],            # Single overlay with NV12 format and min alpha
        'alpha_bmp_min_alpha': [1],             # Single overlay with BMP format and min alpha
        'alpha_nv12_normal_alpha': [1],         # Single overlay with NV12 format and normal alpha
        'alpha_bmp_normal_alpha': [1],          # Single overlay with BMP format and normal alpha
    },
    'bitmap': {
        'bmp_min_bitmapYUV': [1],         # Single overlay with BMP format and bitmap yuv
        'bmp_normal_bitmapYUV': [1],      # Single overlay with BMP format and bitmap yuv
        'bmp_max_bitmapYUV': [1],      # Single overlay with BMP format and bitmap yuv
    },
    'region': {
        'region_full_overlay': [1, 2],                  # 2 overlays enabled
        'region_12': list(range(1, 13)),         # All 12 overlays enabled (same as full)
        'region_max_olCropSize': [1],                   # Max crop size
    },
    'scale': {
        'scale_normal_128': [1],                 # Single overlay with normal scale 128
        'scale_small_64': [1],                   # Single overlay with small scale 64
        'scale_large_256': [1],                  # Single overlay with large scale 256
    }
}

# Parameter configuration ranges and value extraction rules
PARAMETER_CONFIGS = {
    'olAlphaN': {
        'range': [0, 255],
        'keywords': {
            'max': 255,
            'min': 0,
            'normal': 128,
        }
    },
    'olBitmapYN': {
        'range': [0, 255],
        'keywords': {
            'max': 255,
            'min': 0,
            'normal': 200,
        }
    },
    'olBitmapUN': {
        'range': [0, 255],
        'keywords': {
            'max': 255,
            'min': 0,
            'normal': 200,
        }
    },
    'olBitmapVN': {
        'range': [0, 255],
        'keywords': {
            'max': 255,
            'min': 0,
            'normal': 200,
        }
    },
    'olCropWidthN': {
        'range': [1, 8192],
        'keywords': {
            'max': 8192,
            'min': 1,
        }
    },
    'olCropHeightN': {
        'range': [1, 8192],
        'keywords': {
            'max': 8192,
            'min': 1,
        }
    },
    'olScaleWidthN': {
        'range': [1, 8192],
        'keywords': {
            'max': 8192,
            'min': 1,
            'normal': 128,
            'small': 64,
            'large': 256,
        }
    },
    'olScaleHeightN': {
        'range': [1, 8192],
        'keywords': {
            'max': 8192,
            'min': 1,
            'normal': 128,
            'small': 32,
            'large': 256,
        }
    },
    # Add more parameter ranges as needed
}

def extract_parameter_values(config_name):
    """Extract parameter values from config name based on keywords."""
    param_values = {}

    if not config_name:
        return param_values

    # Special handling for bitmapYUV (all three components) - check this first
    if 'bitmapYUV' in config_name:
        for keyword in ['max', 'min', 'normal']:
            if keyword in config_name:
                if 'olBitmapYN' in PARAMETER_CONFIGS:
                    param_values['olBitmapYN'] = PARAMETER_CONFIGS['olBitmapYN']['keywords'].get(keyword, 0)
                if 'olBitmapUN' in PARAMETER_CONFIGS:
                    param_values['olBitmapUN'] = PARAMETER_CONFIGS['olBitmapUN']['keywords'].get(keyword, 0)
                if 'olBitmapVN' in PARAMETER_CONFIGS:
                    param_values['olBitmapVN'] = PARAMETER_CONFIGS['olBitmapVN']['keywords'].get(keyword, 0)
                break
        return param_values  # Return early to avoid setting other parameters (including alpha)

    # Handle alpha parameters (only if not a bitmapYUV config)
    if 'alpha' in config_name:
        for keyword in ['max', 'min', 'normal']:
            if keyword in config_name:
                if 'olAlphaN' in PARAMETER_CONFIGS:
                    param_values['olAlphaN'] = PARAMETER_CONFIGS['olAlphaN']['keywords'].get(keyword, 0)
                break
        return param_values  # Return early to avoid setting other parameters

    # Handle JPEG scale parameters
    if 'scale' in config_name or 'jpeg' in config_name.lower():
        for keyword in ['max', 'min', 'normal', 'small', 'large']:
            if keyword in config_name:
                if 'olScaleWidthN' in PARAMETER_CONFIGS:
                    param_values['olScaleWidthN'] = PARAMETER_CONFIGS['olScaleWidthN']['keywords'].get(keyword, 128)
                if 'olScaleHeightN' in PARAMETER_CONFIGS:
                    param_values['olScaleHeightN'] = PARAMETER_CONFIGS['olScaleHeightN']['keywords'].get(keyword, 128)
                break
        return param_values  # Return early to avoid setting other parameters

    return param_values

def extract_resolution(filename):
    """Extract resolution (e.g., 1920x1080, 1920_1080, 80_80) from filename using regex."""
    # Try different patterns for resolution
    patterns = [
        r'(\d+)[xX](\d+)',      # 1920x1080, 1920X1080
        r'(\d+)_(\d+)',         # 1920_1080, 80_80
        r'(\d+)-(\d+)',         # 1920-1080
    ]

    for pattern in patterns:
        match = re.search(pattern, filename)
        if match:
            width, height = int(match.group(1)), int(match.group(2))
            # Basic sanity check - both dimensions should be reasonable
            if 1 <= width <= 65536 and 1 <= height <= 65536:
                return width, height

    return None, None

def get_ol_format(filename):
    """Determine olFormat value based on file extension."""
    filename_lower = filename.lower()
    if filename_lower.endswith('.rgb888'):
        return 0  # ARGB8888
    elif filename_lower.endswith('.nv12'):
        return 1  # NV12
    elif filename_lower.endswith('.bmp'):
        return 2  # bitmap
    else:
        # Default to ARGB8888 for unknown extensions
        return 0

def calculate_overlay_enables(overlay_list):
    """Calculate overlayEnables value based on list of enabled overlays (1-12)."""
    enables = 0
    for overlay_num in overlay_list:
        if 1 <= overlay_num <= 12:
            enables |= (1 << (overlay_num - 1))  # bit 0 for overlay 1, bit 1 for overlay 2, etc.
    return enables

def generate_overlay_keys(overlay_list, template, input_files, config_name=None):
    """Generate overlay key configurations for enabled overlays."""
    config = copy.deepcopy(template)

    # Determine if this is a bitmap configuration (only configs with 'bitmapYUV' should have bitmap parameters)
    is_bitmap_config = config_name and 'bitmapYUV' in config_name

    # Apply parameter configurations if config_name is provided
    if config_name:
        param_values = extract_parameter_values(config_name)
        for param_key, param_value in param_values.items():
            config[param_key] = param_value

    # Calculate overlayEnables value
    config['overlayEnables'] = calculate_overlay_enables(overlay_list)

    # Initialize position tracking variables
    initial_x_offset = template.get('olXoffsetN', 0)
    initial_y_offset = template.get('olYoffsetN', 0)
    current_x_offset = initial_x_offset
    current_y_offset = initial_y_offset
    default_crop_width = template.get('olCropWidthN', 64)
    default_crop_height = template.get('olCropHeightN', 192)

    # Generate keys for each enabled overlay
    for i, overlay_num in enumerate(overlay_list):
        key_suffix = '{:02d}'.format(overlay_num)  # 01, 02, ..., 12

        # Set input file (use provided input files or default pattern)
        if i < len(input_files):
            input_file = input_files[i]
        elif len(input_files) > 0:
            # Use first input file as default if no specific file for this overlay
            input_file = input_files[0]
        else:
            # No input files provided, use default pattern
            input_file = 'olInput{}.yuv'.format(overlay_num)

        config['olInput{}'.format(key_suffix)] = input_file

        # Extract resolution from the actual input filename for this overlay
        width, height = extract_resolution(input_file)
        if width and height:
            config['olWidth{}'.format(key_suffix)] = width
            config['olHeight{}'.format(key_suffix)] = height
        else:
            # Use default values if resolution cannot be extracted
            config['olWidth{}'.format(key_suffix)] = 0
            config['olHeight{}'.format(key_suffix)] = 0

        # Determine olFormat based on file extension
        ol_format = get_ol_format(input_file)
        config['olFormat{}'.format(key_suffix)] = ol_format

        # Copy other overlay parameters from template (keeping N suffix)
        # Apply parameter configurations if available
        if config_name:
            param_values = extract_parameter_values(config_name)
            config['olAlpha{}'.format(key_suffix)] = param_values.get('olAlphaN', template.get('olAlphaN', 0))
            # Apply bitmap YUV parameters ONLY if they exist in param_values (i.e., for bitmap configs)
            if 'olBitmapYN' in param_values:
                config['olBitmapY{}'.format(key_suffix)] = param_values['olBitmapYN']
            if 'olBitmapUN' in param_values:
                config['olBitmapU{}'.format(key_suffix)] = param_values['olBitmapUN']
            if 'olBitmapVN' in param_values:
                config['olBitmapV{}'.format(key_suffix)] = param_values['olBitmapVN']
            # Apply JPEG scale parameters if they exist in param_values
            if 'olScaleWidthN' in param_values:
                config['olScaleWidth{}'.format(key_suffix)] = param_values['olScaleWidthN']
            if 'olScaleHeightN' in param_values:
                config['olScaleHeight{}'.format(key_suffix)] = param_values['olScaleHeightN']
        else:
            config['olAlpha{}'.format(key_suffix)] = template.get('olAlphaN', 0)
            # Only copy bitmap YUV parameters from template if this is a bitmap config
            if is_bitmap_config:
                if 'olBitmapYN' in template:
                    config['olBitmapY{}'.format(key_suffix)] = template.get('olBitmapYN', 0)
                if 'olBitmapUN' in template:
                    config['olBitmapU{}'.format(key_suffix)] = template.get('olBitmapUN', 0)
                if 'olBitmapVN' in template:
                    config['olBitmapV{}'.format(key_suffix)] = template.get('olBitmapVN', 0)
            # Copy JPEG scale parameters from template if they exist
            if 'olScaleWidthN' in template:
                config['olScaleWidth{}'.format(key_suffix)] = template.get('olScaleWidthN', 128)
            if 'olScaleHeightN' in template:
                config['olScaleHeight{}'.format(key_suffix)] = template.get('olScaleHeightN', 128)

        config['olCropXoffset{}'.format(key_suffix)] = template.get('olCropXoffsetN', 0)
        config['olCropYoffset{}'.format(key_suffix)] = template.get('olCropYoffsetN', 0)
        config['olCropWidth{}'.format(key_suffix)] = default_crop_width
        config['olCropHeight{}'.format(key_suffix)] = default_crop_height

        # Calculate position for this overlay to avoid overlapping
        config['olXoffset{}'.format(key_suffix)] = current_x_offset
        config['olYoffset{}'.format(key_suffix)] = current_y_offset

        # Update position for next overlay
        # Move right by crop width
        current_x_offset += default_crop_width

        # Every 4 overlays, move to next row and reset X position
        if (i + 1) % 4 == 0:
            current_x_offset = initial_x_offset  # Reset to initial X
            current_y_offset += default_crop_height  # Move down by crop height

    # Remove template keys with N suffix
    keys_to_remove = [k for k in config.keys() if k.endswith('N')]
    for key in keys_to_remove:
        del config[key]

    # Remove bitmap parameters from non-bitmap configurations
    if not is_bitmap_config:
        bitmap_keys_to_remove = []
        for key in config.keys():
            if key.startswith('olBitmapY') or key.startswith('olBitmapU') or key.startswith('olBitmapV'):
                bitmap_keys_to_remove.append(key)
        for key in bitmap_keys_to_remove:
            del config[key]

    return config

def ordered_load(stream):
    """Custom YAML loader that preserves field order using OrderedDict."""
    class OrderedLoader(yaml.SafeLoader):
        pass

    def construct_mapping(loader, node):
        loader.flatten_mapping(node)
        return OrderedDict(loader.construct_pairs(node))

    OrderedLoader.add_constructor(
        yaml.resolver.BaseResolver.DEFAULT_MAPPING_TAG,
        construct_mapping)

    return yaml.load(stream, OrderedLoader)

def ordered_dump(data, stream=None, **kwargs):
    """Custom YAML dumper that preserves field order and uses block style."""
    class OrderedDumper(yaml.SafeDumper):
        pass

    def _dict_representer(dumper, data):
        return dumper.represent_dict(data.items())

    OrderedDumper.add_representer(OrderedDict, _dict_representer)

    return yaml.dump(data, stream, Dumper=OrderedDumper, **kwargs)

def get_output_root(template_path, user_output_dir=None):
    """Determine output root directory based on user input or template file name (removing 'default')."""
    if user_output_dir:
        return os.path.abspath(user_output_dir)

    template_dir = os.path.dirname(os.path.abspath(template_path))
    base_name = os.path.basename(template_path)
    base_no_ext = os.path.splitext(base_name)[0]
    cleaned_name = base_no_ext.replace('default', '').strip('_-')
    return os.path.join(template_dir, cleaned_name)

def generate_video_configs(template_path, input_files=None, output_dir=None):
    """Generate YAML configs for video-specific overlay configurations."""
    if not os.path.exists(template_path):
        print('Error: Template YAML not found: {}'.format(template_path))
        return

    with open(template_path, 'r') as f:
        try:
            template = ordered_load(f)
        except Exception as e:
            print('Error parsing YAML: {}'.format(e))
            return

    if output_dir is None:
        # Use template directory and append video suffix
        template_dir = os.path.dirname(os.path.abspath(template_path))
        output_root = os.path.join(template_dir, 'osd_cfg_overlay_video')
    else:
        output_root = os.path.abspath(output_dir)

    if not os.path.exists(output_root):
        os.makedirs(output_root)

    # Use provided input files or default video files
    if input_files is None or len(input_files) == 0:
        input_files = ['osd_RandomAlpha_4096_2048.rgb888']  # Default video overlay file

    # Generate configs for each video overlay configuration category
    for category, configs in VIDEO_OVERLAY_CONFIGS.items():
        # Create category subdirectory with osd_cfg_ prefix
        category_dir_name = 'osd_cfg_{}'.format(category)
        category_output_dir = os.path.join(output_root, category_dir_name)
        if not os.path.exists(category_output_dir):
            os.makedirs(category_output_dir)

        for config_name, overlay_list in configs.items():
            # Set specific input file based on config type
            if 'format_rgb888' in config_name:
                video_input_files = ['osd_RandomAlpha_4096_2048.rgb888']
            elif 'format_nv12' in config_name:
                video_input_files = ['kuubaRandom_352x288.nv12']
            elif 'format_bmp' in config_name:
                video_input_files = ['osd_80_80.bmp']
            else:
                video_input_files = input_files

            config = generate_overlay_keys(overlay_list, template, video_input_files, config_name)

            # Create output filename based on config name
            output_filename = 'osd_cfg_{}.yaml'.format(config_name)
            output_path = os.path.join(category_output_dir, output_filename)

            with open(output_path, 'w') as out_f:
                ordered_dump(config, out_f, default_flow_style=False)

            # Show format and position info for each overlay
            overlay_info = []
            for overlay_num in overlay_list:
                key_suffix = '{:02d}'.format(overlay_num)
                ol_format = config.get('olFormat{}'.format(key_suffix), 0)
                x_offset = config.get('olXoffset{}'.format(key_suffix), 0)
                y_offset = config.get('olYoffset{}'.format(key_suffix), 0)
                format_names = {0: 'ARGB8888', 1: 'NV12', 2: 'bitmap'}
                overlay_info.append('{}:{}@({},{})'.format(
                    overlay_num,
                    format_names.get(ol_format, 'unknown'),
                    x_offset,
                    y_offset
                ))

            print('Generated: {} (overlays: {}, enables: 0x{:03x})'.format(
                output_path, overlay_list, config['overlayEnables']))
            print('  Overlay details: {}'.format(', '.join(overlay_info)))

def generate_configs(template_path, input_files=None, output_dir=None):
    """Generate YAML configs for all overlay configurations."""
    if not os.path.exists(template_path):
        print('Error: Template YAML not found: {}'.format(template_path))
        return

    with open(template_path, 'r') as f:
        try:
            template = ordered_load(f)
        except Exception as e:
            print('Error parsing YAML: {}'.format(e))
            return

    output_root = get_output_root(template_path, output_dir)
    if not os.path.exists(output_root):
        os.makedirs(output_root)

    # Use provided input files or empty list
    if input_files is None:
        input_files = []

    # Generate configs for each overlay configuration
    for i, overlay_list in enumerate(OVERLAY_CONFIGS):
        config = generate_overlay_keys(overlay_list, template, input_files)

        # Create output filename based on enabled overlays
        overlay_str = '_'.join('{:02d}'.format(num) for num in sorted(overlay_list))
        output_filename = 'osd_cfg_overlay_{}.yaml'.format(overlay_str)
        output_path = os.path.join(output_root, output_filename)

        with open(output_path, 'w') as out_f:
            ordered_dump(config, out_f, default_flow_style=False)

        # Show format and position info for each overlay
        overlay_info = []
        for overlay_num in overlay_list:
            key_suffix = '{:02d}'.format(overlay_num)
            ol_format = config.get('olFormat{}'.format(key_suffix), 0)
            x_offset = config.get('olXoffset{}'.format(key_suffix), 0)
            y_offset = config.get('olYoffset{}'.format(key_suffix), 0)
            format_names = {0: 'ARGB8888', 1: 'NV12', 2: 'bitmap'}
            overlay_info.append('{}:{}@({},{})'.format(
                overlay_num,
                format_names.get(ol_format, 'unknown'),
                x_offset,
                y_offset
            ))

        print('Generated: {} (overlays: {}, enables: 0x{:03x})'.format(
            output_path, overlay_list, config['overlayEnables']))
        print('  Overlay details: {}'.format(', '.join(overlay_info)))

def main():
    """CLI entry point for the script."""
    if len(sys.argv) < 2:
        print('Usage: {} <template_yaml> [options] [input_file1] [input_file2] ...'.format(sys.argv[0]))
        print('Example: {} osd_cfg_ol_input_default.yaml overlay1_640x480.rgb888 overlay2_320x240.nv12'.format(sys.argv[0]))
        print('')
        print('Options:')
        print('  --output-dir=<dir>     Specify output directory')
        print('  --video-configs        Generate video-specific configurations')
        print('')
        print('Input file handling:')
        print('  - If only input_file1 provided: used as default for all overlays')
        print('  - If multiple files provided: used in order, then input_file1 as default')
        print('  - If no files provided: uses default pattern olInputN.yuv')
        print('')
        print('Supported input file formats:')
        print('  .rgb888 -> olFormatN = 0 (ARGB8888)')
        print('  .nv12   -> olFormatN = 1 (NV12)')
        print('  .bmp    -> olFormatN = 2 (bitmap)')
        print('  other   -> olFormatN = 0 (default ARGB8888)')
        print('')
        print('Position calculation:')
        print('  - Overlays are positioned to avoid overlapping')
        print('  - Each overlay moves right by olCropWidth from previous')
        print('  - Every 4 overlays, moves to next row (down by olCropHeight)')
        print('')
        print('Current overlay configurations:')
        for i, overlay_list in enumerate(OVERLAY_CONFIGS):
            enables = calculate_overlay_enables(overlay_list)
            print('  Config {}: overlays {} (enables: 0x{:03x})'.format(i+1, overlay_list, enables))
        print('')
        print('Parameter ranges:')
        for param_name, param_config in PARAMETER_CONFIGS.items():
            param_range = param_config.get('range', [])
            keywords = param_config.get('keywords', {})
            keyword_str = ', '.join(['{}: {}'.format(k, v) for k, v in keywords.items()])
            print('  {}: {} [{}]'.format(param_name, param_range, keyword_str))
        print('')
        print('Video overlay configurations:')
        for category, configs in VIDEO_OVERLAY_CONFIGS.items():
            print('  Category: {}'.format(category))
            for config_name, overlay_list in configs.items():
                enables = calculate_overlay_enables(overlay_list)
                param_info = ""
                param_values = extract_parameter_values(config_name)
                if param_values:
                    param_list = ['{}: {}'.format(k, v) for k, v in param_values.items()]
                    param_info = " [{}]".format(', '.join(param_list))
                print('    {}: overlays {} (enables: 0x{:03x}){}'.format(config_name, overlay_list, enables, param_info))
        sys.exit(1)

    template_yaml = sys.argv[1]

    # Parse arguments
    input_files = []
    output_dir = None
    video_configs = False

    for arg in sys.argv[2:]:
        if arg.startswith('--output-dir='):
            output_dir = arg.split('=', 1)[1]
        elif arg == '--video-configs':
            video_configs = True
        else:
            input_files.append(arg)

    if video_configs:
        generate_video_configs(template_yaml, input_files, output_dir)
    else:
        generate_configs(template_yaml, input_files, output_dir)

if __name__ == '__main__':
    main()