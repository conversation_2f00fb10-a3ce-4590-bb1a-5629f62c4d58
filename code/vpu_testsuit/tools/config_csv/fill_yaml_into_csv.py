#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""Generate CSV entries from YAML files for given codecs in batch order."""

from __future__ import print_function
import os
import csv
import argparse
import re
import sys
import yaml



def load_cfg_file(cfg_path):
    """Load config values from a YAML configuration file."""
    with open(cfg_path, 'r') as f:
        data = yaml.safe_load(f)
    return (
        data.get('DEFAULT_CODECS', []),
        data.get('DEFAULT_CSV_COLUMNS', []),
        data.get('BLACKLIST_KEYWORDS', []),
        data.get('WHITELIST_KEYWORDS', []),
        data.get('CODEC_SPECIFIC_BLACKLIST', {}),
        data.get('INCLUDE_DEFAULT_FILES', True)
    )




def is_file_allowed(filename, codec=None):
    """Determine if a file should be included based on blacklist and whitelist."""
    # Skip filtering if no config file was loaded
    if not hasattr(sys.modules[__name__], 'BLACKLIST_KEYWORDS'):
        return True
        
    base = os.path.basename(filename)
    path = filename
    
    # Check whitelist first (only on filename)
    for white in WHITELIST_KEYWORDS:
        if white in base:
            return True
            
    # Check blacklists on both filename and path components
    for black in BLACKLIST_KEYWORDS:
        if black == 'default' and INCLUDE_DEFAULT_FILES:
            continue  # Skip default check if INCLUDE_DEFAULT_FILES is True
        if str(black) in str(base) or str(black) in str(path):
            return False
            
    if codec and CODEC_SPECIFIC_BLACKLIST.get(codec):
        for black in CODEC_SPECIFIC_BLACKLIST[codec]:
            if str(black) in str(base) or str(black) in str(path):
                return False
    return True


def find_yaml_files(yaml_dir):
    """Recursively find all .yaml files under yaml_dir."""
    yaml_files = []
    for root, _, files in os.walk(yaml_dir):
        for name in files:
            if name.endswith('.yaml'):
                full_path = os.path.join(root, name)
                if is_file_allowed(full_path, codec=None):
                    yaml_files.append(full_path)
    return sorted(yaml_files)


def extract_key_from_filename(filename, csv_column):
    """Extract key from a filename like csv_column_key_value.yaml or default.yaml."""
    base = os.path.basename(filename)
    name, _ = os.path.splitext(base)
    if name == 'default' and INCLUDE_DEFAULT_FILES:
        return 'default'
    prefix = csv_column + '_'
    if name.startswith(prefix):
        rest = name[len(prefix):]
        parts = rest.split('_')
        if len(parts) >= 1:
            return parts[0]
    return 'unknown'


def write_to_csv(csv_file, csv_column, yaml_files, codecs):
    """Append entries to CSV file in codec batches without changing column order."""
    file_exists = os.path.isfile(csv_file)

    existing_rows = []
    max_case_id = 0
    fieldnames = ['case_id', csv_column, 'codec', 'coverage']

    if file_exists:
        with open(csv_file, 'r') as f:
            reader = csv.DictReader(f)
            existing_rows = list(reader)
            if reader.fieldnames:
                fieldnames = list(reader.fieldnames)
            for row in existing_rows:
                if 'case_id' in row and row['case_id'].isdigit():
                    max_case_id = max(max_case_id, int(row['case_id']))

        for required in ['case_id', csv_column, 'codec', 'coverage']:
            if required not in fieldnames:
                fieldnames.append(required)
    else:
        fieldnames = ['case_id', csv_column, 'codec', 'coverage']

    with open(csv_file, 'w') as f:
        writer = csv.DictWriter(f, fieldnames=fieldnames)
        writer.writeheader()
        for row in existing_rows:
            writer.writerow(row)

        case_id = max_case_id + 1
        for codec in codecs:
            for yaml_file in yaml_files:
                if not is_file_allowed(yaml_file, codec):
                    continue
                row = dict((k, '') for k in fieldnames)
                row['case_id'] = case_id
                row[csv_column] = os.path.basename(yaml_file)
                row['codec'] = codec
                key = extract_key_from_filename(yaml_file, csv_column)
                row['coverage'] = "{}[{}]".format(csv_column, key)
                writer.writerow(row)
                case_id += 1


def collect_yaml_files_per_column(yaml_dir, csv_column):
    """Search only under subdirectory named csv_column including its subdirectories."""
    target_dir = os.path.join(yaml_dir, csv_column)
    if not os.path.isdir(target_dir):
        return []
    return find_yaml_files(target_dir)


def parse_args():
    """Parse command-line arguments."""
    parser = argparse.ArgumentParser(
        description='Append YAML file paths and codec info to a CSV file.')
    parser.add_argument('--yaml_dir', required=True,
                        help='Directory containing YAML files')
    parser.add_argument('--csv_file', required=True,
                        help='Target CSV file to update')
    parser.add_argument('--csv_column', required=False,
                        help='CSV column name to store YAML file name')
    parser.add_argument('--codec', required=False, nargs='+',
                        help='One or more codec names (e.g., h264 jpeg)')
    parser.add_argument('--cfg_file', required=False,
                        help='YAML config file to override default behavior')
    return parser.parse_args()


def main():
    """Main entry point."""
    global DEFAULT_CODECS, DEFAULT_CSV_COLUMNS, BLACKLIST_KEYWORDS, WHITELIST_KEYWORDS, CODEC_SPECIFIC_BLACKLIST, INCLUDE_DEFAULT_FILES

    args = parse_args()

    if args.cfg_file and args.cfg_file.endswith(('.yaml', '.yml')):
        DEFAULT_CODECS, DEFAULT_CSV_COLUMNS, BLACKLIST_KEYWORDS, WHITELIST_KEYWORDS, CODEC_SPECIFIC_BLACKLIST, INCLUDE_DEFAULT_FILES = load_cfg_file(args.cfg_file)
    elif not args.csv_column and not args.codec:
        print("[WARNING] You must provide --cfg_file if --csv_column or --codec is not specified.")
        return

    if not args.csv_column and not args.cfg_file and not args.codec:
        print("[WARNING] Neither --cfg_file, --csv_column, nor --codec provided. Nothing will be processed.")
        return

    codecs = args.codec if args.codec else DEFAULT_CODECS

    if args.csv_column:
        yaml_files = find_yaml_files(args.yaml_dir)
        if yaml_files:
            for codec in codecs:
                write_to_csv(
                    csv_file=args.csv_file,
                    csv_column=args.csv_column,
                    yaml_files=yaml_files,
                    codecs=[codec])
    else:
        for codec in codecs:
            for csv_column in DEFAULT_CSV_COLUMNS:
                yaml_files = collect_yaml_files_per_column(args.yaml_dir, csv_column)
                if yaml_files:
                    write_to_csv(
                        csv_file=args.csv_file,
                        csv_column=csv_column,
                        yaml_files=yaml_files,
                        codecs=[codec])


if __name__ == '__main__':
    main()
