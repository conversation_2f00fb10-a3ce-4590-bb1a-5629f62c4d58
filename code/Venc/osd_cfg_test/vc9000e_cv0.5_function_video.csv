case_id,flag,milestone,cv_id,codec,category,coverage,io_cfg,firstPic,lastPic,pp_cfg,bs_fmt_cfg,const_chroma,gop_cfg,rdo_cfg,sei_cfg,vui_cfg,hdr10_cfg,eos_cfg,intrarecon_cfg,me_cfg,rc_cfg,vq_cfg,frame_rate_cfg,roi_cfg,statistics_cfg,rfc_cfg,osd_cfg,osd_map_cfg,mosaic_cfg,alignment_cfg,parallel_cfg,axi_cfg,irq_cfg,debug_cfg,md5_ref,md5,result
1,,cv0.5,,h264,function,io_cfg[FrameRate],ducks_take_off_1080p50_1920x1080_420.yaml,,,,,,,,,,,,,,,,,,,,osd_cfg_overlay_01_02_03_04_05_06_07_08_09_10_11_12.yaml,,,,,,,,,,
2,,cv0.5,,h264,function,pp_cfg[codedChromaIdc],format_176x144_i010.yaml,0,4,pp_cfg_codedChromaIdc_0.yaml,,,,,,,,,,,,,,,,,,,,,,,,,,,
3,,cv0.5,,h264,function,io_cfg[FrameRate],ducks_take_off_1080p50_1920x1080_420.yaml,,,,,,,,,,,,,,,,,,,,osd_cfg_overlay_01_02.yaml,,,,,,,,,,
4,,cv0.5,,h264,function,io_cfg[FrameRate],ducks_take_off_1080p50_1920x1080_420.yaml,,,,,,,,,,,,,,,,,,,,osd_cfg_overlay_01_03_05.yaml,,,,,,,,,,
4,,cv0.5,,h265,function,io_cfg[FrameRate],ducks_take_off_1080p50_1920x1080_420.yaml,,,,,,,,,,,,,,,,,,,,osd_cfg_overlay_01_03_05.yaml,,,,,,,,,,
3,,cv0.5,,h265,function,io_cfg[FrameRate],ducks_take_off_1080p50_1920x1080_420.yaml,,,,,,,,,,,,,,,,,,,,osd_cfg_overlay_01_02.yaml,,,,,,,,,,
1,,cv0.5,,h265,function,io_cfg[FrameRate],ducks_take_off_1080p50_1920x1080_420.yaml,,,,,,,,,,,,,,,,,,,,osd_cfg_overlay_01_02_03_04_05_06_07_08_09_10_11_12.yaml,,,,,,,,,,
