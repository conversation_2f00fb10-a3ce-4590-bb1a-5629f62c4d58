#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""Generate OSD YAML config files from a template, handling overlay enables and input file resolution extraction."""

from __future__ import print_function
import os
import sys
import yaml
import re
import copy
from collections import OrderedDict

# User configurable overlay table - modify this to enable different overlays
# Each entry represents which overlays to enable (1-12), can enable multiple overlays
OVERLAY_CONFIGS = [
    # Example configurations - modify as needed
    [1],           # Enable only overlay 1
    [1, 2],        # Enable overlays 1 and 2
    [1, 3, 5],     # Enable overlays 1, 3, and 5
    [2, 4, 6, 8],  # Enable overlays 2, 4, 6, and 8
    # Add more configurations as needed
]

def extract_resolution(filename):
    """Extract resolution (e.g., 1920x1080) from filename using regex."""
    match = re.search(r'(\d+)[xX](\d+)', filename)
    if match:
        return int(match.group(1)), int(match.group(2))
    return None, None

def calculate_overlay_enables(overlay_list):
    """Calculate overlayEnables value based on list of enabled overlays (1-12)."""
    enables = 0
    for overlay_num in overlay_list:
        if 1 <= overlay_num <= 12:
            enables |= (1 << (overlay_num - 1))  # bit 0 for overlay 1, bit 1 for overlay 2, etc.
    return enables

def generate_overlay_keys(overlay_list, template, input_files):
    """Generate overlay key configurations for enabled overlays."""
    config = copy.deepcopy(template)

    # Calculate overlayEnables value
    config['overlayEnables'] = calculate_overlay_enables(overlay_list)

    # Generate keys for each enabled overlay
    for i, overlay_num in enumerate(overlay_list):
        key_suffix = '{:02d}'.format(overlay_num)  # 01, 02, ..., 12

        # Set input file (use provided input files or default pattern)
        if i < len(input_files):
            input_file = input_files[i]
        else:
            input_file = 'olInput{}.yuv'.format(overlay_num)

        config['olInput{}'.format(key_suffix)] = input_file

        # Extract resolution from input filename
        width, height = extract_resolution(input_file)
        if width and height:
            config['olWidth{}'.format(key_suffix)] = width
            config['olHeight{}'.format(key_suffix)] = height
        else:
            # Use default values if resolution cannot be extracted
            config['olWidth{}'.format(key_suffix)] = 0
            config['olHeight{}'.format(key_suffix)] = 0

        # Copy other overlay parameters from template (keeping N suffix)
        config['olAlpha{}'.format(key_suffix)] = template.get('olAlphaN', 0)
        config['olXoffset{}'.format(key_suffix)] = template.get('olXoffsetN', 0)
        config['olYoffset{}'.format(key_suffix)] = template.get('olYoffsetN', 0)
        config['olCropXoffset{}'.format(key_suffix)] = template.get('olCropXoffsetN', 0)
        config['olCropYoffset{}'.format(key_suffix)] = template.get('olCropYoffsetN', 0)
        config['olCropWidth{}'.format(key_suffix)] = template.get('olCropWidthN', 64)
        config['olCropHeight{}'.format(key_suffix)] = template.get('olCropHeightN', 192)

    # Remove template keys with N suffix
    keys_to_remove = [k for k in config.keys() if k.endswith('N')]
    for key in keys_to_remove:
        del config[key]

    return config

def ordered_load(stream):
    """Custom YAML loader that preserves field order using OrderedDict."""
    class OrderedLoader(yaml.SafeLoader):
        pass

    def construct_mapping(loader, node):
        loader.flatten_mapping(node)
        return OrderedDict(loader.construct_pairs(node))

    OrderedLoader.add_constructor(
        yaml.resolver.BaseResolver.DEFAULT_MAPPING_TAG,
        construct_mapping)

    return yaml.load(stream, OrderedLoader)

def ordered_dump(data, stream=None, **kwargs):
    """Custom YAML dumper that preserves field order and uses block style."""
    class OrderedDumper(yaml.SafeDumper):
        pass

    def _dict_representer(dumper, data):
        return dumper.represent_dict(data.items())

    OrderedDumper.add_representer(OrderedDict, _dict_representer)

    return yaml.dump(data, stream, Dumper=OrderedDumper, **kwargs)

def get_output_root(template_path, user_output_dir=None):
    """Determine output root directory based on user input or template file name (removing 'default')."""
    if user_output_dir:
        return os.path.abspath(user_output_dir)

    template_dir = os.path.dirname(os.path.abspath(template_path))
    base_name = os.path.basename(template_path)
    base_no_ext = os.path.splitext(base_name)[0]
    cleaned_name = base_no_ext.replace('default', '').strip('_-')
    return os.path.join(template_dir, cleaned_name)

def generate_configs(template_path, input_files=None, output_dir=None):
    """Generate YAML configs for all overlay configurations."""
    if not os.path.exists(template_path):
        print('Error: Template YAML not found: {}'.format(template_path))
        return

    with open(template_path, 'r') as f:
        try:
            template = ordered_load(f)
        except Exception as e:
            print('Error parsing YAML: {}'.format(e))
            return

    output_root = get_output_root(template_path, output_dir)
    if not os.path.exists(output_root):
        os.makedirs(output_root)

    # Use provided input files or empty list
    if input_files is None:
        input_files = []

    # Generate configs for each overlay configuration
    for i, overlay_list in enumerate(OVERLAY_CONFIGS):
        config = generate_overlay_keys(overlay_list, template, input_files)

        # Create output filename based on enabled overlays
        overlay_str = '_'.join('{:02d}'.format(num) for num in sorted(overlay_list))
        output_filename = 'osd_cfg_overlay_{}.yaml'.format(overlay_str)
        output_path = os.path.join(output_root, output_filename)

        with open(output_path, 'w') as out_f:
            ordered_dump(config, out_f, default_flow_style=False)

        print('Generated: {} (overlays: {}, enables: 0x{:03x})'.format(
            output_path, overlay_list, config['overlayEnables']))

def main():
    """CLI entry point for the script."""
    if len(sys.argv) < 2:
        print('Usage: {} <template_yaml> [input_file1] [input_file2] ... [--output-dir=<dir>]'.format(sys.argv[0]))
        print('Example: {} osd_cfg_ol_input_default.yaml overlay1_640x480.yuv overlay2_320x240.yuv'.format(sys.argv[0]))
        print('')
        print('Current overlay configurations:')
        for i, overlay_list in enumerate(OVERLAY_CONFIGS):
            enables = calculate_overlay_enables(overlay_list)
            print('  Config {}: overlays {} (enables: 0x{:03x})'.format(i+1, overlay_list, enables))
        sys.exit(1)

    template_yaml = sys.argv[1]

    # Parse arguments
    input_files = []
    output_dir = None

    for arg in sys.argv[2:]:
        if arg.startswith('--output-dir='):
            output_dir = arg.split('=', 1)[1]
        else:
            input_files.append(arg)

    generate_configs(template_yaml, input_files, output_dir)

if __name__ == '__main__':
    main()