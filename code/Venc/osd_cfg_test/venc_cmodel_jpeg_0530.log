+ /root/workspace/code/vpu_testsuit/input/vc9000e/app/cmodel/ASM_n/jpeg_testenc_vce --input=/root/workspace/code/vpu_testsuit/input/vc9000e/stream/bs_link/warpdrive/size/1080p_1920x1080_420.yuv --output=/root/workspace/code/vpu_testsuit/output/114/1080p_1920x1080_420.jpg --lumWidthSrc=1920 --lumHeightSrc=1080 --frameType=0 --overlayEnables=1 --olInput01=/root/workspace/code/vpu_testsuit/input/vc9000e/stream/bs_link/vsi/osd_RandomAlpha_4096_2048.rgb888 --olWidth01=4096 --olHeight01=2048 --olFormat01=0 --olAlpha01=255 --olCropXoffset01=0 --olCropYoffset01=0 --olCropWidth01=64 --olCropHeight01=64 --olXoffset01=0 --olYoffset01=0

* * * * * * * * * * * * * * * * * * * * *

      HANTRO JPEG ENCODER TESTBENCH

* * * * * * * * * * * * * * * * * * * * *

VCX Encoder API v2.5.87
vcx_vcmd_driver: main module register 0:0x90004300
vcx_vcmd_driver: main module register 80:0xbbbfc400
vcx_vcmd_driver: main module register 214:0x48801000
vcx_vcmd_driver: main module register 226:0x11031b01
vcx_vcmd_driver: main module register 287:0x40e18000
vcx_vcmd_driver: main module register 509:0x70002189
Total  HW  Memory: 0
Total SWHW Memory: 0
HW ID:  0x90004300	 SW Build: 1079147

Init config: 1920x1080 @ x0y0 => 1920x1080   

	**********************************************************

	-JPEG: ENCODER CONFIGURATION
	-JPEG: qp 		:1
	-JPEG: inX 		:1920
	-JPEG: inY 		:1080
	-JPEG: outX 		:1920
	-JPEG: outY 		:1080
	-JPEG: rst 		:0
	-JPEG: xOff 		:0
	-JPEG: yOff 		:0
	-JPEG: frameType 	:0
	-JPEG: colorConversionType :0
	-JPEG: colorConversionA    :0
	-JPEG: colorConversionB    :0
	-JPEG: colorConversionC    :0
	-JPEG: colorConversionE    :0
	-JPEG: colorConversionF    :0
	-JPEG: rotation 	:0
	-JPEG: codingType 	:0
	-JPEG: codingMode 	:0
	-JPEG: markerType 	:0
	-JPEG: units 		:0
	-JPEG: xDen 		:1
	-JPEG: yDen 		:1
	-JPEG: thumbnail format	:0
	-JPEG: Xthumbnail	:32
	-JPEG: Ythumbnail	:32
	-JPEG: First picture	:0
	-JPEG: Last picture		:0
	-JPEG: inputLineBufEn 		:0
	-JPEG: inputLineBufLoopBackEn 	:0
	-JPEG: inputLineBufHwModeEn 	:0
	-JPEG: inputLineBufDepth 	:1
	-JPEG: amountPerLoopBack 	:0
	-JPEG: streamMultiSegmentMode 	:0
	-JPEG: streamMultiSegmentAmount 	:4
	-JPEG: constChromaEn 	:0
	-JPEG: constCb 	:128
	-JPEG: constCr 	:128

	NOTE! Using comment values defined in testbench!

	**********************************************************

vcx_vcmd_driver: main module register 0:0x90004300
vcx_vcmd_driver: main module register 80:0xbbbfc400
vcx_vcmd_driver: main module register 214:0x48801000
vcx_vcmd_driver: main module register 226:0x11031b01
vcx_vcmd_driver: main module register 287:0x40e18000
vcx_vcmd_driver: main module register 509:0x70002189
Input 0x0 + 1920x1080 encoding at 32x32 + 1920x1080 
Input buffer size:          3112960 bytes
Input buffer bus address:   0x60b3000
Input buffer user address:   0x60b3000
Output buffer0 size:         4149248 bytes
Output buffer0 bus address:  0x63ac000
Output buffer0 user address:  0x63ac000
encIn.pOutBuf[0] 0x63ac000
Frame   0 started...
Reading frame 0 slice 0 (3110400 bytes) from /root/workspace/code/vpu_testsuit/input/vc9000e/stream/bs_link/warpdrive/size/1080p_1920x1080_420.yuv... OK
Finished Processing One VcmdBuf!
Frame   0 ready!  96820 bytes
Writing stream (96820 bytes)... OK
=== Encoded 0 bits=96820 HWCycles=0 Time(us 78171 HW +SW) 
Release encoder
Total  HW  Memory: 36664832
Total SWHW Memory: 4148224
+ /root/workspace/code/vpu_testsuit/input/vc9000e/app/cmodel/ASM_n/jpeg_testenc_vce --input=/root/workspace/code/vpu_testsuit/input/vc9000e/stream/bs_link/warpdrive/size/1080p_1920x1080_420.yuv --output=/root/workspace/code/vpu_testsuit/output/115/1080p_1920x1080_420.jpg --lumWidthSrc=1920 --lumHeightSrc=1080 --frameType=0 --overlayEnables=1 --olInput01=/root/workspace/code/vpu_testsuit/input/vc9000e/stream/bs_link/vsi/osd_RandomAlpha_4096_2048.rgb888 --olWidth01=4096 --olHeight01=2048 --olFormat01=0 --olAlpha01=0 --olCropXoffset01=0 --olCropYoffset01=0 --olCropWidth01=64 --olCropHeight01=64 --olXoffset01=0 --olYoffset01=0

* * * * * * * * * * * * * * * * * * * * *

      HANTRO JPEG ENCODER TESTBENCH

* * * * * * * * * * * * * * * * * * * * *

VCX Encoder API v2.5.87
vcx_vcmd_driver: main module register 0:0x90004300
vcx_vcmd_driver: main module register 80:0xbbbfc400
vcx_vcmd_driver: main module register 214:0x48801000
vcx_vcmd_driver: main module register 226:0x11031b01
vcx_vcmd_driver: main module register 287:0x40e18000
vcx_vcmd_driver: main module register 509:0x70002189
Total  HW  Memory: 0
Total SWHW Memory: 0
HW ID:  0x90004300	 SW Build: 1079147

Init config: 1920x1080 @ x0y0 => 1920x1080   

	**********************************************************

	-JPEG: ENCODER CONFIGURATION
	-JPEG: qp 		:1
	-JPEG: inX 		:1920
	-JPEG: inY 		:1080
	-JPEG: outX 		:1920
	-JPEG: outY 		:1080
	-JPEG: rst 		:0
	-JPEG: xOff 		:0
	-JPEG: yOff 		:0
	-JPEG: frameType 	:0
	-JPEG: colorConversionType :0
	-JPEG: colorConversionA    :0
	-JPEG: colorConversionB    :0
	-JPEG: colorConversionC    :0
	-JPEG: colorConversionE    :0
	-JPEG: colorConversionF    :0
	-JPEG: rotation 	:0
	-JPEG: codingType 	:0
	-JPEG: codingMode 	:0
	-JPEG: markerType 	:0
	-JPEG: units 		:0
	-JPEG: xDen 		:1
	-JPEG: yDen 		:1
	-JPEG: thumbnail format	:0
	-JPEG: Xthumbnail	:32
	-JPEG: Ythumbnail	:32
	-JPEG: First picture	:0
	-JPEG: Last picture		:0
	-JPEG: inputLineBufEn 		:0
	-JPEG: inputLineBufLoopBackEn 	:0
	-JPEG: inputLineBufHwModeEn 	:0
	-JPEG: inputLineBufDepth 	:1
	-JPEG: amountPerLoopBack 	:0
	-JPEG: streamMultiSegmentMode 	:0
	-JPEG: streamMultiSegmentAmount 	:4
	-JPEG: constChromaEn 	:0
	-JPEG: constCb 	:128
	-JPEG: constCr 	:128

	NOTE! Using comment values defined in testbench!

	**********************************************************

vcx_vcmd_driver: main module register 0:0x90004300
vcx_vcmd_driver: main module register 80:0xbbbfc400
vcx_vcmd_driver: main module register 214:0x48801000
vcx_vcmd_driver: main module register 226:0x11031b01
vcx_vcmd_driver: main module register 287:0x40e18000
vcx_vcmd_driver: main module register 509:0x70002189
Input 0x0 + 1920x1080 encoding at 32x32 + 1920x1080 
Input buffer size:          3112960 bytes
Input buffer bus address:   0xc0fa000
Input buffer user address:   0xc0fa000
Output buffer0 size:         4149248 bytes
Output buffer0 bus address:  0xc3f3000
Output buffer0 user address:  0xc3f3000
encIn.pOutBuf[0] 0xc3f3000
Frame   0 started...
Reading frame 0 slice 0 (3110400 bytes) from /root/workspace/code/vpu_testsuit/input/vc9000e/stream/bs_link/warpdrive/size/1080p_1920x1080_420.yuv... OK
Finished Processing One VcmdBuf!
Frame   0 ready!  96820 bytes
Writing stream (96820 bytes)... OK
=== Encoded 0 bits=96820 HWCycles=0 Time(us 100524 HW +SW) 
Release encoder
Total  HW  Memory: 36664832
Total SWHW Memory: 4148224
+ /root/workspace/code/vpu_testsuit/input/vc9000e/app/cmodel/ASM_n/jpeg_testenc_vce --input=/root/workspace/code/vpu_testsuit/input/vc9000e/stream/bs_link/warpdrive/size/1080p_1920x1080_420.yuv --output=/root/workspace/code/vpu_testsuit/output/116/1080p_1920x1080_420.jpg --lumWidthSrc=1920 --lumHeightSrc=1080 --frameType=0 --overlayEnables=1 --olInput01=/root/workspace/code/vpu_testsuit/input/vc9000e/stream/bs_link/vsi/osd_RandomAlpha_4096_2048.rgb888 --olWidth01=4096 --olHeight01=2048 --olFormat01=0 --olAlpha01=128 --olCropXoffset01=0 --olCropYoffset01=0 --olCropWidth01=64 --olCropHeight01=64 --olXoffset01=0 --olYoffset01=0

* * * * * * * * * * * * * * * * * * * * *

      HANTRO JPEG ENCODER TESTBENCH

* * * * * * * * * * * * * * * * * * * * *

VCX Encoder API v2.5.87
vcx_vcmd_driver: main module register 0:0x90004300
vcx_vcmd_driver: main module register 80:0xbbbfc400
vcx_vcmd_driver: main module register 214:0x48801000
vcx_vcmd_driver: main module register 226:0x11031b01
vcx_vcmd_driver: main module register 287:0x40e18000
vcx_vcmd_driver: main module register 509:0x70002189
Total  HW  Memory: 0
Total SWHW Memory: 0
HW ID:  0x90004300	 SW Build: 1079147

Init config: 1920x1080 @ x0y0 => 1920x1080   

	**********************************************************

	-JPEG: ENCODER CONFIGURATION
	-JPEG: qp 		:1
	-JPEG: inX 		:1920
	-JPEG: inY 		:1080
	-JPEG: outX 		:1920
	-JPEG: outY 		:1080
	-JPEG: rst 		:0
	-JPEG: xOff 		:0
	-JPEG: yOff 		:0
	-JPEG: frameType 	:0
	-JPEG: colorConversionType :0
	-JPEG: colorConversionA    :0
	-JPEG: colorConversionB    :0
	-JPEG: colorConversionC    :0
	-JPEG: colorConversionE    :0
	-JPEG: colorConversionF    :0
	-JPEG: rotation 	:0
	-JPEG: codingType 	:0
	-JPEG: codingMode 	:0
	-JPEG: markerType 	:0
	-JPEG: units 		:0
	-JPEG: xDen 		:1
	-JPEG: yDen 		:1
	-JPEG: thumbnail format	:0
	-JPEG: Xthumbnail	:32
	-JPEG: Ythumbnail	:32
	-JPEG: First picture	:0
	-JPEG: Last picture		:0
	-JPEG: inputLineBufEn 		:0
	-JPEG: inputLineBufLoopBackEn 	:0
	-JPEG: inputLineBufHwModeEn 	:0
	-JPEG: inputLineBufDepth 	:1
	-JPEG: amountPerLoopBack 	:0
	-JPEG: streamMultiSegmentMode 	:0
	-JPEG: streamMultiSegmentAmount 	:4
	-JPEG: constChromaEn 	:0
	-JPEG: constCb 	:128
	-JPEG: constCr 	:128

	NOTE! Using comment values defined in testbench!

	**********************************************************

vcx_vcmd_driver: main module register 0:0x90004300
vcx_vcmd_driver: main module register 80:0xbbbfc400
vcx_vcmd_driver: main module register 214:0x48801000
vcx_vcmd_driver: main module register 226:0x11031b01
vcx_vcmd_driver: main module register 287:0x40e18000
vcx_vcmd_driver: main module register 509:0x70002189
Input 0x0 + 1920x1080 encoding at 32x32 + 1920x1080 
Input buffer size:          3112960 bytes
Input buffer bus address:   0x3fe17000
Input buffer user address:  0x3fe17000
Output buffer0 size:         4149248 bytes
Output buffer0 bus address:  0x40110000
Output buffer0 user address: 0x40110000
encIn.pOutBuf[0] 0x40110000
Frame   0 started...
Reading frame 0 slice 0 (3110400 bytes) from /root/workspace/code/vpu_testsuit/input/vc9000e/stream/bs_link/warpdrive/size/1080p_1920x1080_420.yuv... OK
Finished Processing One VcmdBuf!
Frame   0 ready!  96820 bytes
Writing stream (96820 bytes)... OK
=== Encoded 0 bits=96820 HWCycles=0 Time(us 74763 HW +SW) 
Release encoder
Total  HW  Memory: 36664832
Total SWHW Memory: 4148224
+ /root/workspace/code/vpu_testsuit/input/vc9000e/app/cmodel/ASM_n/jpeg_testenc_vce --input=/root/workspace/code/vpu_testsuit/input/vc9000e/stream/bs_link/warpdrive/size/1080p_1920x1080_420.yuv --output=/root/workspace/code/vpu_testsuit/output/117/1080p_1920x1080_420.jpg --lumWidthSrc=1920 --lumHeightSrc=1080 --frameType=0 --overlayEnables=1 --olInput01=/root/workspace/code/vpu_testsuit/input/vc9000e/stream/bs_link/vsi/osd_RandomAlpha_4096_2048.rgb888 --olWidth01=4096 --olHeight01=2048 --olFormat01=0 --olAlpha01=255 --olCropXoffset01=0 --olCropYoffset01=0 --olCropWidth01=64 --olCropHeight01=64 --olXoffset01=0 --olYoffset01=0

* * * * * * * * * * * * * * * * * * * * *

      HANTRO JPEG ENCODER TESTBENCH

* * * * * * * * * * * * * * * * * * * * *

VCX Encoder API v2.5.87
vcx_vcmd_driver: main module register 0:0x90004300
vcx_vcmd_driver: main module register 80:0xbbbfc400
vcx_vcmd_driver: main module register 214:0x48801000
vcx_vcmd_driver: main module register 226:0x11031b01
vcx_vcmd_driver: main module register 287:0x40e18000
vcx_vcmd_driver: main module register 509:0x70002189
Total  HW  Memory: 0
Total SWHW Memory: 0
HW ID:  0x90004300	 SW Build: 1079147

Init config: 1920x1080 @ x0y0 => 1920x1080   

	**********************************************************

	-JPEG: ENCODER CONFIGURATION
	-JPEG: qp 		:1
	-JPEG: inX 		:1920
	-JPEG: inY 		:1080
	-JPEG: outX 		:1920
	-JPEG: outY 		:1080
	-JPEG: rst 		:0
	-JPEG: xOff 		:0
	-JPEG: yOff 		:0
	-JPEG: frameType 	:0
	-JPEG: colorConversionType :0
	-JPEG: colorConversionA    :0
	-JPEG: colorConversionB    :0
	-JPEG: colorConversionC    :0
	-JPEG: colorConversionE    :0
	-JPEG: colorConversionF    :0
	-JPEG: rotation 	:0
	-JPEG: codingType 	:0
	-JPEG: codingMode 	:0
	-JPEG: markerType 	:0
	-JPEG: units 		:0
	-JPEG: xDen 		:1
	-JPEG: yDen 		:1
	-JPEG: thumbnail format	:0
	-JPEG: Xthumbnail	:32
	-JPEG: Ythumbnail	:32
	-JPEG: First picture	:0
	-JPEG: Last picture		:0
	-JPEG: inputLineBufEn 		:0
	-JPEG: inputLineBufLoopBackEn 	:0
	-JPEG: inputLineBufHwModeEn 	:0
	-JPEG: inputLineBufDepth 	:1
	-JPEG: amountPerLoopBack 	:0
	-JPEG: streamMultiSegmentMode 	:0
	-JPEG: streamMultiSegmentAmount 	:4
	-JPEG: constChromaEn 	:0
	-JPEG: constCb 	:128
	-JPEG: constCr 	:128

	NOTE! Using comment values defined in testbench!

	**********************************************************

vcx_vcmd_driver: main module register 0:0x90004300
vcx_vcmd_driver: main module register 80:0xbbbfc400
vcx_vcmd_driver: main module register 214:0x48801000
vcx_vcmd_driver: main module register 226:0x11031b01
vcx_vcmd_driver: main module register 287:0x40e18000
vcx_vcmd_driver: main module register 509:0x70002189
Input 0x0 + 1920x1080 encoding at 32x32 + 1920x1080 
Input buffer size:          3112960 bytes
Input buffer bus address:   0x149ea000
Input buffer user address:  0x149ea000
Output buffer0 size:         4149248 bytes
Output buffer0 bus address:  0x14ce3000
Output buffer0 user address: 0x14ce3000
encIn.pOutBuf[0] 0x14ce3000
Frame   0 started...
Reading frame 0 slice 0 (3110400 bytes) from /root/workspace/code/vpu_testsuit/input/vc9000e/stream/bs_link/warpdrive/size/1080p_1920x1080_420.yuv... OK
Finished Processing One VcmdBuf!
Frame   0 ready!  96820 bytes
Writing stream (96820 bytes)... OK
=== Encoded 0 bits=96820 HWCycles=0 Time(us 80953 HW +SW) 
Release encoder
Total  HW  Memory: 36664832
Total SWHW Memory: 4148224
+ /root/workspace/code/vpu_testsuit/input/vc9000e/app/cmodel/ASM_n/jpeg_testenc_vce --input=/root/workspace/code/vpu_testsuit/input/vc9000e/stream/bs_link/warpdrive/size/1080p_1920x1080_420.yuv --output=/root/workspace/code/vpu_testsuit/output/118/1080p_1920x1080_420.jpg --lumWidthSrc=1920 --lumHeightSrc=1080 --frameType=0 --overlayEnables=1 --olInput01=/root/workspace/code/vpu_testsuit/input/vc9000e/stream/bs_link/vsi/osd_RandomAlpha_4096_2048.rgb888 --olWidth01=4096 --olHeight01=2048 --olFormat01=0 --olAlpha01=0 --olCropXoffset01=0 --olCropYoffset01=0 --olCropWidth01=64 --olCropHeight01=64 --olXoffset01=0 --olYoffset01=0

* * * * * * * * * * * * * * * * * * * * *

      HANTRO JPEG ENCODER TESTBENCH

* * * * * * * * * * * * * * * * * * * * *

VCX Encoder API v2.5.87
vcx_vcmd_driver: main module register 0:0x90004300
vcx_vcmd_driver: main module register 80:0xbbbfc400
vcx_vcmd_driver: main module register 214:0x48801000
vcx_vcmd_driver: main module register 226:0x11031b01
vcx_vcmd_driver: main module register 287:0x40e18000
vcx_vcmd_driver: main module register 509:0x70002189
Total  HW  Memory: 0
Total SWHW Memory: 0
HW ID:  0x90004300	 SW Build: 1079147

Init config: 1920x1080 @ x0y0 => 1920x1080   

	**********************************************************

	-JPEG: ENCODER CONFIGURATION
	-JPEG: qp 		:1
	-JPEG: inX 		:1920
	-JPEG: inY 		:1080
	-JPEG: outX 		:1920
	-JPEG: outY 		:1080
	-JPEG: rst 		:0
	-JPEG: xOff 		:0
	-JPEG: yOff 		:0
	-JPEG: frameType 	:0
	-JPEG: colorConversionType :0
	-JPEG: colorConversionA    :0
	-JPEG: colorConversionB    :0
	-JPEG: colorConversionC    :0
	-JPEG: colorConversionE    :0
	-JPEG: colorConversionF    :0
	-JPEG: rotation 	:0
	-JPEG: codingType 	:0
	-JPEG: codingMode 	:0
	-JPEG: markerType 	:0
	-JPEG: units 		:0
	-JPEG: xDen 		:1
	-JPEG: yDen 		:1
	-JPEG: thumbnail format	:0
	-JPEG: Xthumbnail	:32
	-JPEG: Ythumbnail	:32
	-JPEG: First picture	:0
	-JPEG: Last picture		:0
	-JPEG: inputLineBufEn 		:0
	-JPEG: inputLineBufLoopBackEn 	:0
	-JPEG: inputLineBufHwModeEn 	:0
	-JPEG: inputLineBufDepth 	:1
	-JPEG: amountPerLoopBack 	:0
	-JPEG: streamMultiSegmentMode 	:0
	-JPEG: streamMultiSegmentAmount 	:4
	-JPEG: constChromaEn 	:0
	-JPEG: constCb 	:128
	-JPEG: constCr 	:128

	NOTE! Using comment values defined in testbench!

	**********************************************************

vcx_vcmd_driver: main module register 0:0x90004300
vcx_vcmd_driver: main module register 80:0xbbbfc400
vcx_vcmd_driver: main module register 214:0x48801000
vcx_vcmd_driver: main module register 226:0x11031b01
vcx_vcmd_driver: main module register 287:0x40e18000
vcx_vcmd_driver: main module register 509:0x70002189
Input 0x0 + 1920x1080 encoding at 32x32 + 1920x1080 
Input buffer size:          3112960 bytes
Input buffer bus address:   0x156a4000
Input buffer user address:  0x156a4000
Output buffer0 size:         4149248 bytes
Output buffer0 bus address:  0x1599d000
Output buffer0 user address: 0x1599d000
encIn.pOutBuf[0] 0x1599d000
Frame   0 started...
Reading frame 0 slice 0 (3110400 bytes) from /root/workspace/code/vpu_testsuit/input/vc9000e/stream/bs_link/warpdrive/size/1080p_1920x1080_420.yuv... OK
Finished Processing One VcmdBuf!
Frame   0 ready!  96820 bytes
Writing stream (96820 bytes)... OK
=== Encoded 0 bits=96820 HWCycles=0 Time(us 94399 HW +SW) 
Release encoder
Total  HW  Memory: 36664832
Total SWHW Memory: 4148224
+ /root/workspace/code/vpu_testsuit/input/vc9000e/app/cmodel/ASM_n/jpeg_testenc_vce --input=/root/workspace/code/vpu_testsuit/input/vc9000e/stream/bs_link/warpdrive/size/1080p_1920x1080_420.yuv --output=/root/workspace/code/vpu_testsuit/output/119/1080p_1920x1080_420.jpg --lumWidthSrc=1920 --lumHeightSrc=1080 --frameType=0 --overlayEnables=1 --olInput01=/root/workspace/code/vpu_testsuit/input/vc9000e/stream/bs_link/vsi/osd_RandomAlpha_4096_2048.rgb888 --olWidth01=4096 --olHeight01=2048 --olFormat01=0 --olAlpha01=128 --olCropXoffset01=0 --olCropYoffset01=0 --olCropWidth01=64 --olCropHeight01=64 --olXoffset01=0 --olYoffset01=0

* * * * * * * * * * * * * * * * * * * * *

      HANTRO JPEG ENCODER TESTBENCH

* * * * * * * * * * * * * * * * * * * * *

VCX Encoder API v2.5.87
vcx_vcmd_driver: main module register 0:0x90004300
vcx_vcmd_driver: main module register 80:0xbbbfc400
vcx_vcmd_driver: main module register 214:0x48801000
vcx_vcmd_driver: main module register 226:0x11031b01
vcx_vcmd_driver: main module register 287:0x40e18000
vcx_vcmd_driver: main module register 509:0x70002189
Total  HW  Memory: 0
Total SWHW Memory: 0
HW ID:  0x90004300	 SW Build: 1079147

Init config: 1920x1080 @ x0y0 => 1920x1080   

	**********************************************************

	-JPEG: ENCODER CONFIGURATION
	-JPEG: qp 		:1
	-JPEG: inX 		:1920
	-JPEG: inY 		:1080
	-JPEG: outX 		:1920
	-JPEG: outY 		:1080
	-JPEG: rst 		:0
	-JPEG: xOff 		:0
	-JPEG: yOff 		:0
	-JPEG: frameType 	:0
	-JPEG: colorConversionType :0
	-JPEG: colorConversionA    :0
	-JPEG: colorConversionB    :0
	-JPEG: colorConversionC    :0
	-JPEG: colorConversionE    :0
	-JPEG: colorConversionF    :0
	-JPEG: rotation 	:0
	-JPEG: codingType 	:0
	-JPEG: codingMode 	:0
	-JPEG: markerType 	:0
	-JPEG: units 		:0
	-JPEG: xDen 		:1
	-JPEG: yDen 		:1
	-JPEG: thumbnail format	:0
	-JPEG: Xthumbnail	:32
	-JPEG: Ythumbnail	:32
	-JPEG: First picture	:0
	-JPEG: Last picture		:0
	-JPEG: inputLineBufEn 		:0
	-JPEG: inputLineBufLoopBackEn 	:0
	-JPEG: inputLineBufHwModeEn 	:0
	-JPEG: inputLineBufDepth 	:1
	-JPEG: amountPerLoopBack 	:0
	-JPEG: streamMultiSegmentMode 	:0
	-JPEG: streamMultiSegmentAmount 	:4
	-JPEG: constChromaEn 	:0
	-JPEG: constCb 	:128
	-JPEG: constCr 	:128

	NOTE! Using comment values defined in testbench!

	**********************************************************

vcx_vcmd_driver: main module register 0:0x90004300
vcx_vcmd_driver: main module register 80:0xbbbfc400
vcx_vcmd_driver: main module register 214:0x48801000
vcx_vcmd_driver: main module register 226:0x11031b01
vcx_vcmd_driver: main module register 287:0x40e18000
vcx_vcmd_driver: main module register 509:0x70002189
Input 0x0 + 1920x1080 encoding at 32x32 + 1920x1080 
Input buffer size:          3112960 bytes
Input buffer bus address:   0xf2ea000
Input buffer user address:   0xf2ea000
Output buffer0 size:         4149248 bytes
Output buffer0 bus address:  0xf5e3000
Output buffer0 user address:  0xf5e3000
encIn.pOutBuf[0] 0xf5e3000
Frame   0 started...
Reading frame 0 slice 0 (3110400 bytes) from /root/workspace/code/vpu_testsuit/input/vc9000e/stream/bs_link/warpdrive/size/1080p_1920x1080_420.yuv... OK
Finished Processing One VcmdBuf!
Frame   0 ready!  96820 bytes
Writing stream (96820 bytes)... OK
=== Encoded 0 bits=96820 HWCycles=0 Time(us 80789 HW +SW) 
Release encoder
Total  HW  Memory: 36664832
Total SWHW Memory: 4148224
+ /root/workspace/code/vpu_testsuit/input/vc9000e/app/cmodel/ASM_n/jpeg_testenc_vce --input=/root/workspace/code/vpu_testsuit/input/vc9000e/stream/bs_link/warpdrive/size/1080p_1920x1080_420.yuv --output=/root/workspace/code/vpu_testsuit/output/120/1080p_1920x1080_420.jpg --lumWidthSrc=1920 --lumHeightSrc=1080 --frameType=0 --overlayEnables=1 --olInput01=/root/workspace/code/vpu_testsuit/input/vc9000e/stream/bs_link/vsi/osd_RandomAlpha_4096_2048.rgb888 --olWidth01=4096 --olHeight01=2048 --olFormat01=0 --olAlpha01=0 --olCropXoffset01=0 --olCropYoffset01=0 --olCropWidth01=64 --olCropHeight01=64 --olXoffset01=0 --olYoffset01=0

* * * * * * * * * * * * * * * * * * * * *

      HANTRO JPEG ENCODER TESTBENCH

* * * * * * * * * * * * * * * * * * * * *

VCX Encoder API v2.5.87
vcx_vcmd_driver: main module register 0:0x90004300
vcx_vcmd_driver: main module register 80:0xbbbfc400
vcx_vcmd_driver: main module register 214:0x48801000
vcx_vcmd_driver: main module register 226:0x11031b01
vcx_vcmd_driver: main module register 287:0x40e18000
vcx_vcmd_driver: main module register 509:0x70002189
Total  HW  Memory: 0
Total SWHW Memory: 0
HW ID:  0x90004300	 SW Build: 1079147

Init config: 1920x1080 @ x0y0 => 1920x1080   

	**********************************************************

	-JPEG: ENCODER CONFIGURATION
	-JPEG: qp 		:1
	-JPEG: inX 		:1920
	-JPEG: inY 		:1080
	-JPEG: outX 		:1920
	-JPEG: outY 		:1080
	-JPEG: rst 		:0
	-JPEG: xOff 		:0
	-JPEG: yOff 		:0
	-JPEG: frameType 	:0
	-JPEG: colorConversionType :0
	-JPEG: colorConversionA    :0
	-JPEG: colorConversionB    :0
	-JPEG: colorConversionC    :0
	-JPEG: colorConversionE    :0
	-JPEG: colorConversionF    :0
	-JPEG: rotation 	:0
	-JPEG: codingType 	:0
	-JPEG: codingMode 	:0
	-JPEG: markerType 	:0
	-JPEG: units 		:0
	-JPEG: xDen 		:1
	-JPEG: yDen 		:1
	-JPEG: thumbnail format	:0
	-JPEG: Xthumbnail	:32
	-JPEG: Ythumbnail	:32
	-JPEG: First picture	:0
	-JPEG: Last picture		:0
	-JPEG: inputLineBufEn 		:0
	-JPEG: inputLineBufLoopBackEn 	:0
	-JPEG: inputLineBufHwModeEn 	:0
	-JPEG: inputLineBufDepth 	:1
	-JPEG: amountPerLoopBack 	:0
	-JPEG: streamMultiSegmentMode 	:0
	-JPEG: streamMultiSegmentAmount 	:4
	-JPEG: constChromaEn 	:0
	-JPEG: constCb 	:128
	-JPEG: constCr 	:128

	NOTE! Using comment values defined in testbench!

	**********************************************************

vcx_vcmd_driver: main module register 0:0x90004300
vcx_vcmd_driver: main module register 80:0xbbbfc400
vcx_vcmd_driver: main module register 214:0x48801000
vcx_vcmd_driver: main module register 226:0x11031b01
vcx_vcmd_driver: main module register 287:0x40e18000
vcx_vcmd_driver: main module register 509:0x70002189
Input 0x0 + 1920x1080 encoding at 32x32 + 1920x1080 
Input buffer size:          3112960 bytes
Input buffer bus address:   0x2d7ef000
Input buffer user address:  0x2d7ef000
Output buffer0 size:         4149248 bytes
Output buffer0 bus address:  0x2dae8000
Output buffer0 user address: 0x2dae8000
encIn.pOutBuf[0] 0x2dae8000
Frame   0 started...
Reading frame 0 slice 0 (3110400 bytes) from /root/workspace/code/vpu_testsuit/input/vc9000e/stream/bs_link/warpdrive/size/1080p_1920x1080_420.yuv... OK
Finished Processing One VcmdBuf!
Frame   0 ready!  96820 bytes
Writing stream (96820 bytes)... OK
=== Encoded 0 bits=96820 HWCycles=0 Time(us 80999 HW +SW) 
Release encoder
Total  HW  Memory: 36664832
Total SWHW Memory: 4148224
+ /root/workspace/code/vpu_testsuit/input/vc9000e/app/cmodel/ASM_n/jpeg_testenc_vce --input=/root/workspace/code/vpu_testsuit/input/vc9000e/stream/bs_link/warpdrive/size/1080p_1920x1080_420.yuv --output=/root/workspace/code/vpu_testsuit/output/121/1080p_1920x1080_420.jpg --lumWidthSrc=1920 --lumHeightSrc=1080 --frameType=0 --overlayEnables=1 --olInput01=/root/workspace/code/vpu_testsuit/input/vc9000e/stream/bs_link/vsi/osd_RandomAlpha_4096_2048.rgb888 --olWidth01=4096 --olHeight01=2048 --olFormat01=0 --olAlpha01=0 --olCropXoffset01=0 --olCropYoffset01=0 --olCropWidth01=64 --olCropHeight01=64 --olXoffset01=0 --olYoffset01=0

* * * * * * * * * * * * * * * * * * * * *

      HANTRO JPEG ENCODER TESTBENCH

* * * * * * * * * * * * * * * * * * * * *

VCX Encoder API v2.5.87
vcx_vcmd_driver: main module register 0:0x90004300
vcx_vcmd_driver: main module register 80:0xbbbfc400
vcx_vcmd_driver: main module register 214:0x48801000
vcx_vcmd_driver: main module register 226:0x11031b01
vcx_vcmd_driver: main module register 287:0x40e18000
vcx_vcmd_driver: main module register 509:0x70002189
Total  HW  Memory: 0
Total SWHW Memory: 0
HW ID:  0x90004300	 SW Build: 1079147

Init config: 1920x1080 @ x0y0 => 1920x1080   

	**********************************************************

	-JPEG: ENCODER CONFIGURATION
	-JPEG: qp 		:1
	-JPEG: inX 		:1920
	-JPEG: inY 		:1080
	-JPEG: outX 		:1920
	-JPEG: outY 		:1080
	-JPEG: rst 		:0
	-JPEG: xOff 		:0
	-JPEG: yOff 		:0
	-JPEG: frameType 	:0
	-JPEG: colorConversionType :0
	-JPEG: colorConversionA    :0
	-JPEG: colorConversionB    :0
	-JPEG: colorConversionC    :0
	-JPEG: colorConversionE    :0
	-JPEG: colorConversionF    :0
	-JPEG: rotation 	:0
	-JPEG: codingType 	:0
	-JPEG: codingMode 	:0
	-JPEG: markerType 	:0
	-JPEG: units 		:0
	-JPEG: xDen 		:1
	-JPEG: yDen 		:1
	-JPEG: thumbnail format	:0
	-JPEG: Xthumbnail	:32
	-JPEG: Ythumbnail	:32
	-JPEG: First picture	:0
	-JPEG: Last picture		:0
	-JPEG: inputLineBufEn 		:0
	-JPEG: inputLineBufLoopBackEn 	:0
	-JPEG: inputLineBufHwModeEn 	:0
	-JPEG: inputLineBufDepth 	:1
	-JPEG: amountPerLoopBack 	:0
	-JPEG: streamMultiSegmentMode 	:0
	-JPEG: streamMultiSegmentAmount 	:4
	-JPEG: constChromaEn 	:0
	-JPEG: constCb 	:128
	-JPEG: constCr 	:128

	NOTE! Using comment values defined in testbench!

	**********************************************************

vcx_vcmd_driver: main module register 0:0x90004300
vcx_vcmd_driver: main module register 80:0xbbbfc400
vcx_vcmd_driver: main module register 214:0x48801000
vcx_vcmd_driver: main module register 226:0x11031b01
vcx_vcmd_driver: main module register 287:0x40e18000
vcx_vcmd_driver: main module register 509:0x70002189
Input 0x0 + 1920x1080 encoding at 32x32 + 1920x1080 
Input buffer size:          3112960 bytes
Input buffer bus address:   0x1e9a3000
Input buffer user address:  0x1e9a3000
Output buffer0 size:         4149248 bytes
Output buffer0 bus address:  0x1ec9c000
Output buffer0 user address: 0x1ec9c000
encIn.pOutBuf[0] 0x1ec9c000
Frame   0 started...
Reading frame 0 slice 0 (3110400 bytes) from /root/workspace/code/vpu_testsuit/input/vc9000e/stream/bs_link/warpdrive/size/1080p_1920x1080_420.yuv... OK
Finished Processing One VcmdBuf!
Frame   0 ready!  96820 bytes
Writing stream (96820 bytes)... OK
=== Encoded 0 bits=96820 HWCycles=0 Time(us 75777 HW +SW) 
Release encoder
Total  HW  Memory: 36664832
Total SWHW Memory: 4148224
+ /root/workspace/code/vpu_testsuit/input/vc9000e/app/cmodel/ASM_n/jpeg_testenc_vce --input=/root/workspace/code/vpu_testsuit/input/vc9000e/stream/bs_link/warpdrive/size/1080p_1920x1080_420.yuv --output=/root/workspace/code/vpu_testsuit/output/122/1080p_1920x1080_420.jpg --lumWidthSrc=1920 --lumHeightSrc=1080 --frameType=0 --overlayEnables=1 --olInput01=/root/workspace/code/vpu_testsuit/input/vc9000e/stream/bs_link/vsi/osd_RandomAlpha_4096_2048.rgb888 --olWidth01=4096 --olHeight01=2048 --olFormat01=0 --olAlpha01=0 --olCropXoffset01=0 --olCropYoffset01=0 --olCropWidth01=64 --olCropHeight01=64 --olXoffset01=0 --olYoffset01=0

* * * * * * * * * * * * * * * * * * * * *

      HANTRO JPEG ENCODER TESTBENCH

* * * * * * * * * * * * * * * * * * * * *

VCX Encoder API v2.5.87
vcx_vcmd_driver: main module register 0:0x90004300
vcx_vcmd_driver: main module register 80:0xbbbfc400
vcx_vcmd_driver: main module register 214:0x48801000
vcx_vcmd_driver: main module register 226:0x11031b01
vcx_vcmd_driver: main module register 287:0x40e18000
vcx_vcmd_driver: main module register 509:0x70002189
Total  HW  Memory: 0
Total SWHW Memory: 0
HW ID:  0x90004300	 SW Build: 1079147

Init config: 1920x1080 @ x0y0 => 1920x1080   

	**********************************************************

	-JPEG: ENCODER CONFIGURATION
	-JPEG: qp 		:1
	-JPEG: inX 		:1920
	-JPEG: inY 		:1080
	-JPEG: outX 		:1920
	-JPEG: outY 		:1080
	-JPEG: rst 		:0
	-JPEG: xOff 		:0
	-JPEG: yOff 		:0
	-JPEG: frameType 	:0
	-JPEG: colorConversionType :0
	-JPEG: colorConversionA    :0
	-JPEG: colorConversionB    :0
	-JPEG: colorConversionC    :0
	-JPEG: colorConversionE    :0
	-JPEG: colorConversionF    :0
	-JPEG: rotation 	:0
	-JPEG: codingType 	:0
	-JPEG: codingMode 	:0
	-JPEG: markerType 	:0
	-JPEG: units 		:0
	-JPEG: xDen 		:1
	-JPEG: yDen 		:1
	-JPEG: thumbnail format	:0
	-JPEG: Xthumbnail	:32
	-JPEG: Ythumbnail	:32
	-JPEG: First picture	:0
	-JPEG: Last picture		:0
	-JPEG: inputLineBufEn 		:0
	-JPEG: inputLineBufLoopBackEn 	:0
	-JPEG: inputLineBufHwModeEn 	:0
	-JPEG: inputLineBufDepth 	:1
	-JPEG: amountPerLoopBack 	:0
	-JPEG: streamMultiSegmentMode 	:0
	-JPEG: streamMultiSegmentAmount 	:4
	-JPEG: constChromaEn 	:0
	-JPEG: constCb 	:128
	-JPEG: constCr 	:128

	NOTE! Using comment values defined in testbench!

	**********************************************************

vcx_vcmd_driver: main module register 0:0x90004300
vcx_vcmd_driver: main module register 80:0xbbbfc400
vcx_vcmd_driver: main module register 214:0x48801000
vcx_vcmd_driver: main module register 226:0x11031b01
vcx_vcmd_driver: main module register 287:0x40e18000
vcx_vcmd_driver: main module register 509:0x70002189
Input 0x0 + 1920x1080 encoding at 32x32 + 1920x1080 
Input buffer size:          3112960 bytes
Input buffer bus address:   0x4f2f000
Input buffer user address:   0x4f2f000
Output buffer0 size:         4149248 bytes
Output buffer0 bus address:  0x5228000
Output buffer0 user address:  0x5228000
encIn.pOutBuf[0] 0x5228000
Frame   0 started...
Reading frame 0 slice 0 (3110400 bytes) from /root/workspace/code/vpu_testsuit/input/vc9000e/stream/bs_link/warpdrive/size/1080p_1920x1080_420.yuv... OK
Finished Processing One VcmdBuf!
Frame   0 ready!  96820 bytes
Writing stream (96820 bytes)... OK
=== Encoded 0 bits=96820 HWCycles=0 Time(us 82858 HW +SW) 
Release encoder
Total  HW  Memory: 36664832
Total SWHW Memory: 4148224
+ /root/workspace/code/vpu_testsuit/input/vc9000e/app/cmodel/ASM_n/jpeg_testenc_vce --input=/root/workspace/code/vpu_testsuit/input/vc9000e/stream/bs_link/warpdrive/size/1080p_1920x1080_420.yuv --output=/root/workspace/code/vpu_testsuit/output/123/1080p_1920x1080_420.jpg --lumWidthSrc=1920 --lumHeightSrc=1080 --frameType=0 --overlayEnables=1 --olInput01=/root/workspace/code/vpu_testsuit/input/vc9000e/stream/bs_link/vsi/osd_80_80.bmp --olWidth01=80 --olHeight01=80 --olFormat01=2 --olAlpha01=0 --olCropXoffset01=0 --olCropYoffset01=0 --olCropWidth01=64 --olCropHeight01=64 --olXoffset01=0 --olYoffset01=0

* * * * * * * * * * * * * * * * * * * * *

      HANTRO JPEG ENCODER TESTBENCH

* * * * * * * * * * * * * * * * * * * * *

VCX Encoder API v2.5.87
vcx_vcmd_driver: main module register 0:0x90004300
vcx_vcmd_driver: main module register 80:0xbbbfc400
vcx_vcmd_driver: main module register 214:0x48801000
vcx_vcmd_driver: main module register 226:0x11031b01
vcx_vcmd_driver: main module register 287:0x40e18000
vcx_vcmd_driver: main module register 509:0x70002189
Total  HW  Memory: 0
Total SWHW Memory: 0
HW ID:  0x90004300	 SW Build: 1079147

Init config: 1920x1080 @ x0y0 => 1920x1080   

	**********************************************************

	-JPEG: ENCODER CONFIGURATION
	-JPEG: qp 		:1
	-JPEG: inX 		:1920
	-JPEG: inY 		:1080
	-JPEG: outX 		:1920
	-JPEG: outY 		:1080
	-JPEG: rst 		:0
	-JPEG: xOff 		:0
	-JPEG: yOff 		:0
	-JPEG: frameType 	:0
	-JPEG: colorConversionType :0
	-JPEG: colorConversionA    :0
	-JPEG: colorConversionB    :0
	-JPEG: colorConversionC    :0
	-JPEG: colorConversionE    :0
	-JPEG: colorConversionF    :0
	-JPEG: rotation 	:0
	-JPEG: codingType 	:0
	-JPEG: codingMode 	:0
	-JPEG: markerType 	:0
	-JPEG: units 		:0
	-JPEG: xDen 		:1
	-JPEG: yDen 		:1
	-JPEG: thumbnail format	:0
	-JPEG: Xthumbnail	:32
	-JPEG: Ythumbnail	:32
	-JPEG: First picture	:0
	-JPEG: Last picture		:0
	-JPEG: inputLineBufEn 		:0
	-JPEG: inputLineBufLoopBackEn 	:0
	-JPEG: inputLineBufHwModeEn 	:0
	-JPEG: inputLineBufDepth 	:1
	-JPEG: amountPerLoopBack 	:0
	-JPEG: streamMultiSegmentMode 	:0
	-JPEG: streamMultiSegmentAmount 	:4
	-JPEG: constChromaEn 	:0
	-JPEG: constCb 	:128
	-JPEG: constCr 	:128

	NOTE! Using comment values defined in testbench!

	**********************************************************

vcx_vcmd_driver: main module register 0:0x90004300
vcx_vcmd_driver: main module register 80:0xbbbfc400
vcx_vcmd_driver: main module register 214:0x48801000
vcx_vcmd_driver: main module register 226:0x11031b01
vcx_vcmd_driver: main module register 287:0x40e18000
vcx_vcmd_driver: main module register 509:0x70002189
Input 0x0 + 1920x1080 encoding at 32x32 + 1920x1080 
Input buffer size:          3112960 bytes
Input buffer bus address:   0x36873000
Input buffer user address:  0x36873000
Output buffer0 size:         4149248 bytes
Output buffer0 bus address:  0x36b6c000
Output buffer0 user address: 0x36b6c000
encIn.pOutBuf[0] 0x36b6c000
Frame   0 started...
Reading frame 0 slice 0 (3110400 bytes) from /root/workspace/code/vpu_testsuit/input/vc9000e/stream/bs_link/warpdrive/size/1080p_1920x1080_420.yuv... OK
Finished Processing One VcmdBuf!
Frame   0 ready!  96832 bytes
Writing stream (96832 bytes)... OK
=== Encoded 0 bits=96832 HWCycles=0 Time(us 82708 HW +SW) 
Release encoder
Total  HW  Memory: 3111200
Total SWHW Memory: 4148224
+ /root/workspace/code/vpu_testsuit/input/vc9000e/app/cmodel/ASM_n/jpeg_testenc_vce --input=/root/workspace/code/vpu_testsuit/input/vc9000e/stream/bs_link/warpdrive/size/1080p_1920x1080_420.yuv --output=/root/workspace/code/vpu_testsuit/output/124/1080p_1920x1080_420.jpg --lumWidthSrc=1920 --lumHeightSrc=1080 --frameType=0 --overlayEnables=1 --olInput01=/root/workspace/code/vpu_testsuit/input/vc9000e/stream/bs_link/vsi/kuubaRandom_352x288.nv12 --olWidth01=352 --olHeight01=288 --olFormat01=1 --olAlpha01=0 --olCropXoffset01=0 --olCropYoffset01=0 --olCropWidth01=64 --olCropHeight01=64 --olXoffset01=0 --olYoffset01=0

* * * * * * * * * * * * * * * * * * * * *

      HANTRO JPEG ENCODER TESTBENCH

* * * * * * * * * * * * * * * * * * * * *

VCX Encoder API v2.5.87
vcx_vcmd_driver: main module register 0:0x90004300
vcx_vcmd_driver: main module register 80:0xbbbfc400
vcx_vcmd_driver: main module register 214:0x48801000
vcx_vcmd_driver: main module register 226:0x11031b01
vcx_vcmd_driver: main module register 287:0x40e18000
vcx_vcmd_driver: main module register 509:0x70002189
Total  HW  Memory: 0
Total SWHW Memory: 0
HW ID:  0x90004300	 SW Build: 1079147

Init config: 1920x1080 @ x0y0 => 1920x1080   

	**********************************************************

	-JPEG: ENCODER CONFIGURATION
	-JPEG: qp 		:1
	-JPEG: inX 		:1920
	-JPEG: inY 		:1080
	-JPEG: outX 		:1920
	-JPEG: outY 		:1080
	-JPEG: rst 		:0
	-JPEG: xOff 		:0
	-JPEG: yOff 		:0
	-JPEG: frameType 	:0
	-JPEG: colorConversionType :0
	-JPEG: colorConversionA    :0
	-JPEG: colorConversionB    :0
	-JPEG: colorConversionC    :0
	-JPEG: colorConversionE    :0
	-JPEG: colorConversionF    :0
	-JPEG: rotation 	:0
	-JPEG: codingType 	:0
	-JPEG: codingMode 	:0
	-JPEG: markerType 	:0
	-JPEG: units 		:0
	-JPEG: xDen 		:1
	-JPEG: yDen 		:1
	-JPEG: thumbnail format	:0
	-JPEG: Xthumbnail	:32
	-JPEG: Ythumbnail	:32
	-JPEG: First picture	:0
	-JPEG: Last picture		:0
	-JPEG: inputLineBufEn 		:0
	-JPEG: inputLineBufLoopBackEn 	:0
	-JPEG: inputLineBufHwModeEn 	:0
	-JPEG: inputLineBufDepth 	:1
	-JPEG: amountPerLoopBack 	:0
	-JPEG: streamMultiSegmentMode 	:0
	-JPEG: streamMultiSegmentAmount 	:4
	-JPEG: constChromaEn 	:0
	-JPEG: constCb 	:128
	-JPEG: constCr 	:128

	NOTE! Using comment values defined in testbench!

	**********************************************************

vcx_vcmd_driver: main module register 0:0x90004300
vcx_vcmd_driver: main module register 80:0xbbbfc400
vcx_vcmd_driver: main module register 214:0x48801000
vcx_vcmd_driver: main module register 226:0x11031b01
vcx_vcmd_driver: main module register 287:0x40e18000
vcx_vcmd_driver: main module register 509:0x70002189
Input 0x0 + 1920x1080 encoding at 32x32 + 1920x1080 
Input buffer size:          3112960 bytes
Input buffer bus address:   0xb65c000
Input buffer user address:   0xb65c000
Output buffer0 size:         4149248 bytes
Output buffer0 bus address:  0xb955000
Output buffer0 user address:  0xb955000
encIn.pOutBuf[0] 0xb955000
Frame   0 started...
Reading frame 0 slice 0 (3110400 bytes) from /root/workspace/code/vpu_testsuit/input/vc9000e/stream/bs_link/warpdrive/size/1080p_1920x1080_420.yuv... OK
Finished Processing One VcmdBuf!
Frame   0 ready!  96832 bytes
Writing stream (96832 bytes)... OK
=== Encoded 0 bits=96832 HWCycles=0 Time(us 90038 HW +SW) 
Release encoder
Total  HW  Memory: 3262464
Total SWHW Memory: 4148224
+ /root/workspace/code/vpu_testsuit/input/vc9000e/app/cmodel/ASM_n/jpeg_testenc_vce --input=/root/workspace/code/vpu_testsuit/input/vc9000e/stream/bs_link/warpdrive/size/1080p_1920x1080_420.yuv --output=/root/workspace/code/vpu_testsuit/output/125/1080p_1920x1080_420.jpg --lumWidthSrc=1920 --lumHeightSrc=1080 --frameType=0 --overlayEnables=1 --olInput01=/root/workspace/code/vpu_testsuit/input/vc9000e/stream/bs_link/vsi/osd_RandomAlpha_4096_2048.rgb888 --olWidth01=4096 --olHeight01=2048 --olFormat01=0 --olAlpha01=0 --olCropXoffset01=0 --olCropYoffset01=0 --olCropWidth01=64 --olCropHeight01=64 --olXoffset01=0 --olYoffset01=0

* * * * * * * * * * * * * * * * * * * * *

      HANTRO JPEG ENCODER TESTBENCH

* * * * * * * * * * * * * * * * * * * * *

VCX Encoder API v2.5.87
vcx_vcmd_driver: main module register 0:0x90004300
vcx_vcmd_driver: main module register 80:0xbbbfc400
vcx_vcmd_driver: main module register 214:0x48801000
vcx_vcmd_driver: main module register 226:0x11031b01
vcx_vcmd_driver: main module register 287:0x40e18000
vcx_vcmd_driver: main module register 509:0x70002189
Total  HW  Memory: 0
Total SWHW Memory: 0
HW ID:  0x90004300	 SW Build: 1079147

Init config: 1920x1080 @ x0y0 => 1920x1080   

	**********************************************************

	-JPEG: ENCODER CONFIGURATION
	-JPEG: qp 		:1
	-JPEG: inX 		:1920
	-JPEG: inY 		:1080
	-JPEG: outX 		:1920
	-JPEG: outY 		:1080
	-JPEG: rst 		:0
	-JPEG: xOff 		:0
	-JPEG: yOff 		:0
	-JPEG: frameType 	:0
	-JPEG: colorConversionType :0
	-JPEG: colorConversionA    :0
	-JPEG: colorConversionB    :0
	-JPEG: colorConversionC    :0
	-JPEG: colorConversionE    :0
	-JPEG: colorConversionF    :0
	-JPEG: rotation 	:0
	-JPEG: codingType 	:0
	-JPEG: codingMode 	:0
	-JPEG: markerType 	:0
	-JPEG: units 		:0
	-JPEG: xDen 		:1
	-JPEG: yDen 		:1
	-JPEG: thumbnail format	:0
	-JPEG: Xthumbnail	:32
	-JPEG: Ythumbnail	:32
	-JPEG: First picture	:0
	-JPEG: Last picture		:0
	-JPEG: inputLineBufEn 		:0
	-JPEG: inputLineBufLoopBackEn 	:0
	-JPEG: inputLineBufHwModeEn 	:0
	-JPEG: inputLineBufDepth 	:1
	-JPEG: amountPerLoopBack 	:0
	-JPEG: streamMultiSegmentMode 	:0
	-JPEG: streamMultiSegmentAmount 	:4
	-JPEG: constChromaEn 	:0
	-JPEG: constCb 	:128
	-JPEG: constCr 	:128

	NOTE! Using comment values defined in testbench!

	**********************************************************

vcx_vcmd_driver: main module register 0:0x90004300
vcx_vcmd_driver: main module register 80:0xbbbfc400
vcx_vcmd_driver: main module register 214:0x48801000
vcx_vcmd_driver: main module register 226:0x11031b01
vcx_vcmd_driver: main module register 287:0x40e18000
vcx_vcmd_driver: main module register 509:0x70002189
Input 0x0 + 1920x1080 encoding at 32x32 + 1920x1080 
Input buffer size:          3112960 bytes
Input buffer bus address:   0x2e74b000
Input buffer user address:  0x2e74b000
Output buffer0 size:         4149248 bytes
Output buffer0 bus address:  0x2ea44000
Output buffer0 user address: 0x2ea44000
encIn.pOutBuf[0] 0x2ea44000
Frame   0 started...
Reading frame 0 slice 0 (3110400 bytes) from /root/workspace/code/vpu_testsuit/input/vc9000e/stream/bs_link/warpdrive/size/1080p_1920x1080_420.yuv... OK
Finished Processing One VcmdBuf!
Frame   0 ready!  96820 bytes
Writing stream (96820 bytes)... OK
=== Encoded 0 bits=96820 HWCycles=0 Time(us 82981 HW +SW) 
Release encoder
Total  HW  Memory: 36664832
Total SWHW Memory: 4148224
+ /root/workspace/code/vpu_testsuit/input/vc9000e/app/cmodel/ASM_n/jpeg_testenc_vce --input=/root/workspace/code/vpu_testsuit/input/vc9000e/stream/bs_link/warpdrive/size/1080p_1920x1080_420.yuv --output=/root/workspace/code/vpu_testsuit/output/126/1080p_1920x1080_420.jpg --lumWidthSrc=1920 --lumHeightSrc=1080 --frameType=0 --overlayEnables=4095 --olInput01=/root/workspace/code/vpu_testsuit/input/vc9000e/stream/bs_link/vsi/osd_RandomAlpha_4096_2048.rgb888 --olWidth01=4096 --olHeight01=2048 --olFormat01=0 --olAlpha01=0 --olCropXoffset01=0 --olCropYoffset01=0 --olCropWidth01=64 --olCropHeight01=64 --olXoffset01=0 --olYoffset01=0 --olInput02=/root/workspace/code/vpu_testsuit/input/vc9000e/stream/bs_link/vsi/osd_RandomAlpha_4096_2048.rgb888 --olWidth02=4096 --olHeight02=2048 --olFormat02=0 --olAlpha02=0 --olCropXoffset02=0 --olCropYoffset02=0 --olCropWidth02=64 --olCropHeight02=64 --olXoffset02=64 --olYoffset02=0 --olInput03=/root/workspace/code/vpu_testsuit/input/vc9000e/stream/bs_link/vsi/osd_RandomAlpha_4096_2048.rgb888 --olWidth03=4096 --olHeight03=2048 --olFormat03=0 --olAlpha03=0 --olCropXoffset03=0 --olCropYoffset03=0 --olCropWidth03=64 --olCropHeight03=64 --olXoffset03=128 --olYoffset03=0 --olInput04=/root/workspace/code/vpu_testsuit/input/vc9000e/stream/bs_link/vsi/osd_RandomAlpha_4096_2048.rgb888 --olWidth04=4096 --olHeight04=2048 --olFormat04=0 --olAlpha04=0 --olCropXoffset04=0 --olCropYoffset04=0 --olCropWidth04=64 --olCropHeight04=64 --olXoffset04=192 --olYoffset04=0 --olInput05=/root/workspace/code/vpu_testsuit/input/vc9000e/stream/bs_link/vsi/osd_RandomAlpha_4096_2048.rgb888 --olWidth05=4096 --olHeight05=2048 --olFormat05=0 --olAlpha05=0 --olCropXoffset05=0 --olCropYoffset05=0 --olCropWidth05=64 --olCropHeight05=64 --olXoffset05=0 --olYoffset05=64 --olInput06=/root/workspace/code/vpu_testsuit/input/vc9000e/stream/bs_link/vsi/osd_RandomAlpha_4096_2048.rgb888 --olWidth06=4096 --olHeight06=2048 --olFormat06=0 --olAlpha06=0 --olCropXoffset06=0 --olCropYoffset06=0 --olCropWidth06=64 --olCropHeight06=64 --olXoffset06=64 --olYoffset06=64 --olInput07=/root/workspace/code/vpu_testsuit/input/vc9000e/stream/bs_link/vsi/osd_RandomAlpha_4096_2048.rgb888 --olWidth07=4096 --olHeight07=2048 --olFormat07=0 --olAlpha07=0 --olCropXoffset07=0 --olCropYoffset07=0 --olCropWidth07=64 --olCropHeight07=64 --olXoffset07=128 --olYoffset07=64 --olInput08=/root/workspace/code/vpu_testsuit/input/vc9000e/stream/bs_link/vsi/osd_RandomAlpha_4096_2048.rgb888 --olWidth08=4096 --olHeight08=2048 --olFormat08=0 --olAlpha08=0 --olCropXoffset08=0 --olCropYoffset08=0 --olCropWidth08=64 --olCropHeight08=64 --olXoffset08=192 --olYoffset08=64 --olInput09=/root/workspace/code/vpu_testsuit/input/vc9000e/stream/bs_link/vsi/osd_RandomAlpha_4096_2048.rgb888 --olWidth09=4096 --olHeight09=2048 --olFormat09=0 --olAlpha09=0 --olCropXoffset09=0 --olCropYoffset09=0 --olCropWidth09=64 --olCropHeight09=64 --olXoffset09=0 --olYoffset09=128 --olInput10=/root/workspace/code/vpu_testsuit/input/vc9000e/stream/bs_link/vsi/osd_RandomAlpha_4096_2048.rgb888 --olWidth10=4096 --olHeight10=2048 --olFormat10=0 --olAlpha10=0 --olCropXoffset10=0 --olCropYoffset10=0 --olCropWidth10=64 --olCropHeight10=64 --olXoffset10=64 --olYoffset10=128 --olInput11=/root/workspace/code/vpu_testsuit/input/vc9000e/stream/bs_link/vsi/osd_RandomAlpha_4096_2048.rgb888 --olWidth11=4096 --olHeight11=2048 --olFormat11=0 --olAlpha11=0 --olCropXoffset11=0 --olCropYoffset11=0 --olCropWidth11=64 --olCropHeight11=64 --olXoffset11=128 --olYoffset11=128 --olInput12=/root/workspace/code/vpu_testsuit/input/vc9000e/stream/bs_link/vsi/osd_RandomAlpha_4096_2048.rgb888 --olWidth12=4096 --olHeight12=2048 --olFormat12=0 --olAlpha12=0 --olCropXoffset12=0 --olCropYoffset12=0 --olCropWidth12=64 --olCropHeight12=64 --olXoffset12=192 --olYoffset12=128

* * * * * * * * * * * * * * * * * * * * *

      HANTRO JPEG ENCODER TESTBENCH

* * * * * * * * * * * * * * * * * * * * *

VCX Encoder API v2.5.87
vcx_vcmd_driver: main module register 0:0x90004300
vcx_vcmd_driver: main module register 80:0xbbbfc400
vcx_vcmd_driver: main module register 214:0x48801000
vcx_vcmd_driver: main module register 226:0x11031b01
vcx_vcmd_driver: main module register 287:0x40e18000
vcx_vcmd_driver: main module register 509:0x70002189
Total  HW  Memory: 0
Total SWHW Memory: 0
HW ID:  0x90004300	 SW Build: 1079147

Init config: 1920x1080 @ x0y0 => 1920x1080   

	**********************************************************

	-JPEG: ENCODER CONFIGURATION
	-JPEG: qp 		:1
	-JPEG: inX 		:1920
	-JPEG: inY 		:1080
	-JPEG: outX 		:1920
	-JPEG: outY 		:1080
	-JPEG: rst 		:0
	-JPEG: xOff 		:0
	-JPEG: yOff 		:0
	-JPEG: frameType 	:0
	-JPEG: colorConversionType :0
	-JPEG: colorConversionA    :0
	-JPEG: colorConversionB    :0
	-JPEG: colorConversionC    :0
	-JPEG: colorConversionE    :0
	-JPEG: colorConversionF    :0
	-JPEG: rotation 	:0
	-JPEG: codingType 	:0
	-JPEG: codingMode 	:0
	-JPEG: markerType 	:0
	-JPEG: units 		:0
	-JPEG: xDen 		:1
	-JPEG: yDen 		:1
	-JPEG: thumbnail format	:0
	-JPEG: Xthumbnail	:32
	-JPEG: Ythumbnail	:32
	-JPEG: First picture	:0
	-JPEG: Last picture		:0
	-JPEG: inputLineBufEn 		:0
	-JPEG: inputLineBufLoopBackEn 	:0
	-JPEG: inputLineBufHwModeEn 	:0
	-JPEG: inputLineBufDepth 	:1
	-JPEG: amountPerLoopBack 	:0
	-JPEG: streamMultiSegmentMode 	:0
	-JPEG: streamMultiSegmentAmount 	:4
	-JPEG: constChromaEn 	:0
	-JPEG: constCb 	:128
	-JPEG: constCr 	:128

	NOTE! Using comment values defined in testbench!

	**********************************************************

vcx_vcmd_driver: main module register 0:0x90004300
vcx_vcmd_driver: main module register 80:0xbbbfc400
vcx_vcmd_driver: main module register 214:0x48801000
vcx_vcmd_driver: main module register 226:0x11031b01
vcx_vcmd_driver: main module register 287:0x40e18000
vcx_vcmd_driver: main module register 509:0x70002189
Input 0x0 + 1920x1080 encoding at 32x32 + 1920x1080 
Input buffer size:          3112960 bytes
Input buffer bus address:   0x2bee7000
Input buffer user address:  0x2bee7000
Output buffer0 size:         4149248 bytes
Output buffer0 bus address:  0x2c1e0000
Output buffer0 user address: 0x2c1e0000
encIn.pOutBuf[0] 0x2c1e0000
Frame   0 started...
Reading frame 0 slice 0 (3110400 bytes) from /root/workspace/code/vpu_testsuit/input/vc9000e/stream/bs_link/warpdrive/size/1080p_1920x1080_420.yuv... OK
Finished Processing One VcmdBuf!
Frame   0 ready!  97140 bytes
Writing stream (97140 bytes)... OK
=== Encoded 0 bits=97140 HWCycles=0 Time(us 105307 HW +SW) 
Release encoder
Total  HW  Memory: 405763584
Total SWHW Memory: 4148224
+ /root/workspace/code/vpu_testsuit/input/vc9000e/app/cmodel/ASM_n/jpeg_testenc_vce --input=/root/workspace/code/vpu_testsuit/input/vc9000e/stream/bs_link/warpdrive/size/1080p_1920x1080_420.yuv --output=/root/workspace/code/vpu_testsuit/output/127/1080p_1920x1080_420.jpg --lumWidthSrc=1920 --lumHeightSrc=1080 --frameType=0 --overlayEnables=3 --olInput01=/root/workspace/code/vpu_testsuit/input/vc9000e/stream/bs_link/vsi/osd_RandomAlpha_4096_2048.rgb888 --olWidth01=4096 --olHeight01=2048 --olFormat01=0 --olAlpha01=0 --olCropXoffset01=0 --olCropYoffset01=0 --olCropWidth01=960 --olCropHeight01=1080 --olXoffset01=0 --olYoffset01=0 --olInput02=/root/workspace/code/vpu_testsuit/input/vc9000e/stream/bs_link/vsi/osd_RandomAlpha_4096_2048.rgb888 --olWidth02=4096 --olHeight02=2048 --olFormat02=0 --olAlpha02=0 --olCropXoffset02=0 --olCropYoffset02=0 --olCropWidth02=960 --olCropHeight02=1080 --olXoffset02=960 --olYoffset02=0

* * * * * * * * * * * * * * * * * * * * *

      HANTRO JPEG ENCODER TESTBENCH

* * * * * * * * * * * * * * * * * * * * *

VCX Encoder API v2.5.87
vcx_vcmd_driver: main module register 0:0x90004300
vcx_vcmd_driver: main module register 80:0xbbbfc400
vcx_vcmd_driver: main module register 214:0x48801000
vcx_vcmd_driver: main module register 226:0x11031b01
vcx_vcmd_driver: main module register 287:0x40e18000
vcx_vcmd_driver: main module register 509:0x70002189
Total  HW  Memory: 0
Total SWHW Memory: 0
HW ID:  0x90004300	 SW Build: 1079147

Init config: 1920x1080 @ x0y0 => 1920x1080   

	**********************************************************

	-JPEG: ENCODER CONFIGURATION
	-JPEG: qp 		:1
	-JPEG: inX 		:1920
	-JPEG: inY 		:1080
	-JPEG: outX 		:1920
	-JPEG: outY 		:1080
	-JPEG: rst 		:0
	-JPEG: xOff 		:0
	-JPEG: yOff 		:0
	-JPEG: frameType 	:0
	-JPEG: colorConversionType :0
	-JPEG: colorConversionA    :0
	-JPEG: colorConversionB    :0
	-JPEG: colorConversionC    :0
	-JPEG: colorConversionE    :0
	-JPEG: colorConversionF    :0
	-JPEG: rotation 	:0
	-JPEG: codingType 	:0
	-JPEG: codingMode 	:0
	-JPEG: markerType 	:0
	-JPEG: units 		:0
	-JPEG: xDen 		:1
	-JPEG: yDen 		:1
	-JPEG: thumbnail format	:0
	-JPEG: Xthumbnail	:32
	-JPEG: Ythumbnail	:32
	-JPEG: First picture	:0
	-JPEG: Last picture		:0
	-JPEG: inputLineBufEn 		:0
	-JPEG: inputLineBufLoopBackEn 	:0
	-JPEG: inputLineBufHwModeEn 	:0
	-JPEG: inputLineBufDepth 	:1
	-JPEG: amountPerLoopBack 	:0
	-JPEG: streamMultiSegmentMode 	:0
	-JPEG: streamMultiSegmentAmount 	:4
	-JPEG: constChromaEn 	:0
	-JPEG: constCb 	:128
	-JPEG: constCr 	:128

	NOTE! Using comment values defined in testbench!

	**********************************************************

vcx_vcmd_driver: main module register 0:0x90004300
vcx_vcmd_driver: main module register 80:0xbbbfc400
vcx_vcmd_driver: main module register 214:0x48801000
vcx_vcmd_driver: main module register 226:0x11031b01
vcx_vcmd_driver: main module register 287:0x40e18000
vcx_vcmd_driver: main module register 509:0x70002189
Input 0x0 + 1920x1080 encoding at 32x32 + 1920x1080 
Input buffer size:          3112960 bytes
Input buffer bus address:   0xd03d000
Input buffer user address:   0xd03d000
Output buffer0 size:         4149248 bytes
Output buffer0 bus address:  0xd336000
Output buffer0 user address:  0xd336000
encIn.pOutBuf[0] 0xd336000
Frame   0 started...
Reading frame 0 slice 0 (3110400 bytes) from /root/workspace/code/vpu_testsuit/input/vc9000e/stream/bs_link/warpdrive/size/1080p_1920x1080_420.yuv... OK
Finished Processing One VcmdBuf!
Frame   0 ready! 123335 bytes
Writing stream (123335 bytes)... OK
=== Encoded 0 bits=123335 HWCycles=0 Time(us 143524 HW +SW) 
Release encoder
Total  HW  Memory: 70219264
Total SWHW Memory: 4148224
+ /root/workspace/code/vpu_testsuit/input/vc9000e/app/cmodel/ASM_n/jpeg_testenc_vce --input=/root/workspace/code/vpu_testsuit/input/vc9000e/stream/bs_link/warpdrive/size/size_8192x8192_420.yuv --output=/root/workspace/code/vpu_testsuit/output/128/size_8192x8192_420.jpg --lumWidthSrc=8192 --lumHeightSrc=8192 --frameType=0 --overlayEnables=1 --olInput01=/root/workspace/code/vpu_testsuit/input/vc9000e/stream/bs_link/vsi/osd_RandomAlpha_8192_8192.rgb888 --olWidth01=8192 --olHeight01=8192 --olFormat01=0 --olAlpha01=0 --olCropXoffset01=0 --olCropYoffset01=0 --olCropWidth01=8192 --olCropHeight01=8192 --olXoffset01=0 --olYoffset01=0

* * * * * * * * * * * * * * * * * * * * *

      HANTRO JPEG ENCODER TESTBENCH

* * * * * * * * * * * * * * * * * * * * *

VCX Encoder API v2.5.87
vcx_vcmd_driver: main module register 0:0x90004300
vcx_vcmd_driver: main module register 80:0xbbbfc400
vcx_vcmd_driver: main module register 214:0x48801000
vcx_vcmd_driver: main module register 226:0x11031b01
vcx_vcmd_driver: main module register 287:0x40e18000
vcx_vcmd_driver: main module register 509:0x70002189
Total  HW  Memory: 0
Total SWHW Memory: 0
HW ID:  0x90004300	 SW Build: 1079147

Init config: 8192x8192 @ x0y0 => 8192x8192   

	**********************************************************

	-JPEG: ENCODER CONFIGURATION
	-JPEG: qp 		:1
	-JPEG: inX 		:8192
	-JPEG: inY 		:8192
	-JPEG: outX 		:8192
	-JPEG: outY 		:8192
	-JPEG: rst 		:0
	-JPEG: xOff 		:0
	-JPEG: yOff 		:0
	-JPEG: frameType 	:0
	-JPEG: colorConversionType :0
	-JPEG: colorConversionA    :0
	-JPEG: colorConversionB    :0
	-JPEG: colorConversionC    :0
	-JPEG: colorConversionE    :0
	-JPEG: colorConversionF    :0
	-JPEG: rotation 	:0
	-JPEG: codingType 	:0
	-JPEG: codingMode 	:0
	-JPEG: markerType 	:0
	-JPEG: units 		:0
	-JPEG: xDen 		:1
	-JPEG: yDen 		:1
	-JPEG: thumbnail format	:0
	-JPEG: Xthumbnail	:32
	-JPEG: Ythumbnail	:32
	-JPEG: First picture	:0
	-JPEG: Last picture		:0
	-JPEG: inputLineBufEn 		:0
	-JPEG: inputLineBufLoopBackEn 	:0
	-JPEG: inputLineBufHwModeEn 	:0
	-JPEG: inputLineBufDepth 	:1
	-JPEG: amountPerLoopBack 	:0
	-JPEG: streamMultiSegmentMode 	:0
	-JPEG: streamMultiSegmentAmount 	:4
	-JPEG: constChromaEn 	:0
	-JPEG: constCb 	:128
	-JPEG: constCr 	:128

	NOTE! Using comment values defined in testbench!

	**********************************************************

vcx_vcmd_driver: main module register 0:0x90004300
vcx_vcmd_driver: main module register 80:0xbbbfc400
vcx_vcmd_driver: main module register 214:0x48801000
vcx_vcmd_driver: main module register 226:0x11031b01
vcx_vcmd_driver: main module register 287:0x40e18000
vcx_vcmd_driver: main module register 509:0x70002189
Input 0x0 + 8192x8192 encoding at 32x32 + 8192x8192 
Input buffer size:          100663296 bytes
Input buffer bus address:   0x7fb627d14000
Input buffer user address:  0x7fb627d14000
Output buffer0 size:         134221824 bytes
Output buffer0 bus address:  0x7fb61fd11000
Output buffer0 user address: 0x7fb61fd11000
encIn.pOutBuf[0] 0x7fb61fd11000
Frame   0 started...
Reading frame 0 slice 0 (100663296 bytes) from /root/workspace/code/vpu_testsuit/input/vc9000e/stream/bs_link/warpdrive/size/size_8192x8192_420.yuv... OK
Frame   0 ready! 4392689 bytes
Writing stream (4392689 bytes)... OK
=== Encoded 0 bits=4392689 HWCycles=0 Time(us 3669013 HW +SW) 
Release encoder
Total  HW  Memory: 369098752
Total SWHW Memory: 134218752
+ /root/workspace/code/vpu_testsuit/input/vc9000e/app/cmodel/ASM_n/jpeg_testenc_vce --input=/root/workspace/code/vpu_testsuit/input/vc9000e/stream/bs_link/warpdrive/size/1080p_1920x1080_420.yuv --output=/root/workspace/code/vpu_testsuit/output/129/1080p_1920x1080_420.jpg --lumWidthSrc=1920 --lumHeightSrc=1080 --frameType=0 --overlayEnables=1 --olInput01=/root/workspace/code/vpu_testsuit/input/vc9000e/stream/bs_link/vsi/osd_RandomAlpha_4096_2048.rgb888 --olWidth01=4096 --olHeight01=2048 --olFormat01=0 --olAlpha01=0 --olCropXoffset01=0 --olCropYoffset01=0 --olCropWidth01=64 --olCropHeight01=64 --olXoffset01=0 --olYoffset01=0

* * * * * * * * * * * * * * * * * * * * *

      HANTRO JPEG ENCODER TESTBENCH

* * * * * * * * * * * * * * * * * * * * *

VCX Encoder API v2.5.87
vcx_vcmd_driver: main module register 0:0x90004300
vcx_vcmd_driver: main module register 80:0xbbbfc400
vcx_vcmd_driver: main module register 214:0x48801000
vcx_vcmd_driver: main module register 226:0x11031b01
vcx_vcmd_driver: main module register 287:0x40e18000
vcx_vcmd_driver: main module register 509:0x70002189
Total  HW  Memory: 0
Total SWHW Memory: 0
HW ID:  0x90004300	 SW Build: 1079147

Init config: 1920x1080 @ x0y0 => 1920x1080   

	**********************************************************

	-JPEG: ENCODER CONFIGURATION
	-JPEG: qp 		:1
	-JPEG: inX 		:1920
	-JPEG: inY 		:1080
	-JPEG: outX 		:1920
	-JPEG: outY 		:1080
	-JPEG: rst 		:0
	-JPEG: xOff 		:0
	-JPEG: yOff 		:0
	-JPEG: frameType 	:0
	-JPEG: colorConversionType :0
	-JPEG: colorConversionA    :0
	-JPEG: colorConversionB    :0
	-JPEG: colorConversionC    :0
	-JPEG: colorConversionE    :0
	-JPEG: colorConversionF    :0
	-JPEG: rotation 	:0
	-JPEG: codingType 	:0
	-JPEG: codingMode 	:0
	-JPEG: markerType 	:0
	-JPEG: units 		:0
	-JPEG: xDen 		:1
	-JPEG: yDen 		:1
	-JPEG: thumbnail format	:0
	-JPEG: Xthumbnail	:32
	-JPEG: Ythumbnail	:32
	-JPEG: First picture	:0
	-JPEG: Last picture		:0
	-JPEG: inputLineBufEn 		:0
	-JPEG: inputLineBufLoopBackEn 	:0
	-JPEG: inputLineBufHwModeEn 	:0
	-JPEG: inputLineBufDepth 	:1
	-JPEG: amountPerLoopBack 	:0
	-JPEG: streamMultiSegmentMode 	:0
	-JPEG: streamMultiSegmentAmount 	:4
	-JPEG: constChromaEn 	:0
	-JPEG: constCb 	:128
	-JPEG: constCr 	:128

	NOTE! Using comment values defined in testbench!

	**********************************************************

vcx_vcmd_driver: main module register 0:0x90004300
vcx_vcmd_driver: main module register 80:0xbbbfc400
vcx_vcmd_driver: main module register 214:0x48801000
vcx_vcmd_driver: main module register 226:0x11031b01
vcx_vcmd_driver: main module register 287:0x40e18000
vcx_vcmd_driver: main module register 509:0x70002189
Input 0x0 + 1920x1080 encoding at 32x32 + 1920x1080 
Input buffer size:          3112960 bytes
Input buffer bus address:   0x241e6000
Input buffer user address:  0x241e6000
Output buffer0 size:         4149248 bytes
Output buffer0 bus address:  0x244df000
Output buffer0 user address: 0x244df000
encIn.pOutBuf[0] 0x244df000
Frame   0 started...
Reading frame 0 slice 0 (3110400 bytes) from /root/workspace/code/vpu_testsuit/input/vc9000e/stream/bs_link/warpdrive/size/1080p_1920x1080_420.yuv... OK
Finished Processing One VcmdBuf!
Frame   0 ready!  96820 bytes
Writing stream (96820 bytes)... OK
=== Encoded 0 bits=96820 HWCycles=0 Time(us 94771 HW +SW) 
Release encoder
Total  HW  Memory: 36664832
Total SWHW Memory: 4148224
