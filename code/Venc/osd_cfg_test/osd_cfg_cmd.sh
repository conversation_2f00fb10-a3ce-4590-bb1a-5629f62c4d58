# h264
## osd overlay most regions
/root/workspace/code/vpu_testsuit/input/vc9000e/app/cmodel/ASM_y/h264_testenc_vce --input=/root/workspace/data/vsi_stream/enc/yuv_release/gradient_w8192h4096.yuv --output=/root/workspace/code/vpu_testsuit/output/1/gradient_w8192h4096.yuv.h264 --olFormat01=1 --olAlpha01=255 --olWidth01=4096 --olHeight01=2160 --olCropWidth01=64 --olCropHeight01=192 --olBitmapY01=200 --olBitmapU01=200 --olBitmapV01=200 --olFormat02=1 --olAlpha02=255 --olWidth02=4096 --olHeight02=2160 --olXoffset02=64 --olCropXoffset02=64 --olCropYoffset02=66 --olCropWidth02=128 --olCropHeight02=192 --olBitmapY02=200 --olBitmapU02=200 --olBitmapV02=200 --olFormat03=1 --olAlpha03=255 --olWidth03=4096 --olHeight03=2160 --olXoffset03=192 --olCropXoffset03=128 --olCropYoffset03=122 --olCropWidth03=64 --olCropHeight03=192 --olBitmapY03=200 --olBitmapU03=200 --olBitmapV03=200 --olFormat04=1 --olAlpha04=255 --olWidth04=4096 --olHeight04=2160 --olXoffset04=256 --olCropWidth04=64 --olCropHeight04=192 --olBitmapY04=200 --olBitmapU04=200 --olBitmapV04=200 --olFormat05=1 --olAlpha05=255 --olWidth05=4096 --olHeight05=2160 --olXoffset05=320 --olCropWidth05=64 --olCropHeight05=192 --olBitmapY05=200 --olBitmapU05=200 --olBitmapV05=200 --olFormat06=1 --olAlpha06=255 --olWidth06=4096 --olHeight06=2160 --olXoffset06=384 --olCropWidth06=128 --olCropHeight06=192 --olBitmapY06=200 --olBitmapU06=200 --olBitmapV06=200 --olFormat07=1 --olAlpha07=255 --olWidth07=4096 --olHeight07=2160 --olYoffset07=384 --olCropWidth07=128 --olCropHeight07=256 --olBitmapY07=200 --olBitmapU07=200 --olBitmapV07=200 --olFormat08=1 --olAlpha08=255 --olWidth08=4096 --olHeight08=2160 --olXoffset08=128 --olYoffset08=384 --olCropWidth08=64 --olCropHeight08=256 --olBitmapY08=200 --olBitmapU08=200 --olBitmapV08=200 --olFormat09=1 --olAlpha09=255 --olWidth09=4096 --olHeight09=2160 --olXoffset09=192 --olYoffset09=384 --olCropWidth09=128 --olCropHeight09=256 --olBitmapY09=200 --olBitmapU09=200 --olBitmapV09=200 --olFormat10=1 --olAlpha10=255 --olWidth10=4096 --olHeight10=2160 --olXoffset10=320 --olYoffset10=384 --olCropWidth10=64 --olCropHeight10=256 --olBitmapY10=200 --olBitmapU10=200 --olBitmapV10=200 --olFormat11=1 --olAlpha11=255 --olWidth11=4096 --olHeight11=2160 --olXoffset11=384 --olYoffset11=384 --olCropWidth11=64 --olCropHeight11=64 --olBitmapY11=200 --olBitmapU11=200 --olBitmapV11=200 --olFormat12=1 --olAlpha12=255 --olWidth12=4096 --olHeight12=2160 --olXoffset12=448 --olYoffset12=384 --olCropWidth12=64 --olCropHeight12=256 --olBitmapY12=200 --olBitmapU12=200 --olBitmapV12=200 --gopSize=1 --codecFormat=hevc --olInput01=/root/workspace/data/vsi_stream/enc/yuv_release/DinSko_4096x2160.yuv --olInput02=/root/workspace/data/vsi_stream/enc/yuv_release/DinSko_4096x2160.yuv --olInput03=/root/workspace/data/vsi_stream/enc/yuv_release/DinSko_4096x2160.yuv --olInput04=/root/workspace/data/vsi_stream/enc/yuv_release/DinSko_4096x2160.yuv --olInput05=/root/workspace/data/vsi_stream/enc/yuv_release/DinSko_4096x2160.yuv --olInput06=/root/workspace/data/vsi_stream/enc/yuv_release/DinSko_4096x2160.yuv --olInput07=/root/workspace/data/vsi_stream/enc/yuv_release/DinSko_4096x2160.yuv --olInput08=/root/workspace/data/vsi_stream/enc/yuv_release/DinSko_4096x2160.yuv --olInput09=/root/workspace/data/vsi_stream/enc/yuv_release/DinSko_4096x2160.yuv --olInput10=/root/workspace/data/vsi_stream/enc/yuv_release/DinSko_4096x2160.yuv --olInput11=/root/workspace/data/vsi_stream/enc/yuv_release/DinSko_4096x2160.yuv --olInput12=/root/workspace/data/vsi_stream/enc/yuv_release/DinSko_4096x2160.yuv --overlayEnables=4095 --lumWidthSrc=4096 --lumHeightSrc=4096 --width=512 --height=640 --codecFormat=h264 
/root/workspace/code/vpu_testsuit/input/vc9000e/app/cmodel/ASM_y/h264_testenc_vce --input=/root/workspace/data/vsi_stream/enc/yuv_release/pedestrianArea_1920x1080_5f.yuv --output=/root/workspace/code/vpu_testsuit/output/1/pedestrianArea_1920x1080_5f.yuv.h264 \
--olFormat01=1 --olAlpha01=255 --olWidth01=4096 --olHeight01=2160 --olCropWidth01=64 --olCropHeight01=192 --olBitmapY01=200 --olBitmapU01=200 --olBitmapV01=200 \
--olFormat02=1 --olAlpha02=255 --olWidth02=4096 --olHeight02=2160 --olXoffset02=64 --olCropXoffset02=64 --olCropYoffset02=66 --olCropWidth02=128 --olCropHeight02=192 --olBitmapY02=200 --olBitmapU02=200 --olBitmapV02=200 \
--olFormat03=1 --olAlpha03=255 --olWidth03=4096 --olHeight03=2160 --olXoffset03=192 --olCropXoffset03=128 --olCropYoffset03=122 --olCropWidth03=64 --olCropHeight03=192 --olBitmapY03=200 --olBitmapU03=200 --olBitmapV03=200 \
--olFormat04=1 --olAlpha04=255 --olWidth04=4096 --olHeight04=2160 --olXoffset04=256 --olCropWidth04=64 --olCropHeight04=192 --olBitmapY04=200 --olBitmapU04=200 --olBitmapV04=200 \
--olFormat05=1 --olAlpha05=255 --olWidth05=4096 --olHeight05=2160 --olXoffset05=320 --olCropWidth05=64 --olCropHeight05=192 --olBitmapY05=200 --olBitmapU05=200 --olBitmapV05=200 \
--olFormat06=1 --olAlpha06=255 --olWidth06=4096 --olHeight06=2160 --olXoffset06=384 --olCropWidth06=128 --olCropHeight06=192 --olBitmapY06=200 --olBitmapU06=200 --olBitmapV06=200 \
--olFormat07=1 --olAlpha07=255 --olWidth07=4096 --olHeight07=2160 --olYoffset07=384 --olCropWidth07=128 --olCropHeight07=256 --olBitmapY07=200 --olBitmapU07=200 --olBitmapV07=200 \
--olFormat08=1 --olAlpha08=255 --olWidth08=4096 --olHeight08=2160 --olXoffset08=128 --olYoffset08=384 --olCropWidth08=64 --olCropHeight08=256 --olBitmapY08=200 --olBitmapU08=200 --olBitmapV08=200 \
--olFormat09=1 --olAlpha09=255 --olWidth09=4096 --olHeight09=2160 --olXoffset09=192 --olYoffset09=384 --olCropWidth09=128 --olCropHeight09=256 --olBitmapY09=200 --olBitmapU09=200 --olBitmapV09=200 \
--olFormat10=1 --olAlpha10=255 --olWidth10=4096 --olHeight10=2160 --olXoffset10=320 --olYoffset10=384 --olCropWidth10=64 --olCropHeight10=256 --olBitmapY10=200 --olBitmapU10=200 --olBitmapV10=200 \
--olFormat11=1 --olAlpha11=255 --olWidth11=4096 --olHeight11=2160 --olXoffset11=384 --olYoffset11=384 --olCropWidth11=64 --olCropHeight11=64 --olBitmapY11=200 --olBitmapU11=200 --olBitmapV11=200 \
--olFormat12=1 --olAlpha12=255 --olWidth12=4096 --olHeight12=2160 --olXoffset12=448 --olYoffset12=384 --olCropWidth12=64 --olCropHeight12=256 --olBitmapY12=200 --olBitmapU12=200 --olBitmapV12=200 \
--olInput01=/root/workspace/data/vsi_stream/enc/yuv_release/DinSko_4096x2160.yuv --olInput02=/root/workspace/data/vsi_stream/enc/yuv_release/DinSko_4096x2160.yuv --olInput03=/root/workspace/data/vsi_stream/enc/yuv_release/DinSko_4096x2160.yuv --olInput04=/root/workspace/data/vsi_stream/enc/yuv_release/DinSko_4096x2160.yuv --olInput05=/root/workspace/data/vsi_stream/enc/yuv_release/DinSko_4096x2160.yuv --olInput06=/root/workspace/data/vsi_stream/enc/yuv_release/DinSko_4096x2160.yuv --olInput07=/root/workspace/data/vsi_stream/enc/yuv_release/DinSko_4096x2160.yuv --olInput08=/root/workspace/data/vsi_stream/enc/yuv_release/DinSko_4096x2160.yuv --olInput09=/root/workspace/data/vsi_stream/enc/yuv_release/DinSko_4096x2160.yuv --olInput10=/root/workspace/data/vsi_stream/enc/yuv_release/DinSko_4096x2160.yuv --olInput11=/root/workspace/data/vsi_stream/enc/yuv_release/DinSko_4096x2160.yuv --olInput12=/root/workspace/data/vsi_stream/enc/yuv_release/DinSko_4096x2160.yuv \
--overlayEnables=4095 \
--lumWidthSrc=1920 --lumHeightSrc=1080 --width=1920 --height=1080 --codecFormat=h264 
## osd overlay full overlay
/root/workspace/code/vpu_testsuit/input/vc9000e/app/cmodel/ASM_y/h264_testenc_vce --input=/root/workspace/data/vsi_stream/enc/yuv_release/pedestrianArea_1920x1080_5f.yuv --output=/root/workspace/code/vpu_testsuit/output/1/pedestrianArea_1920x1080_5f.yuv.h264 \
--olFormat01=1 --olAlpha01=255 --olWidth01=4096 --olHeight01=2160 --olXoffset01=0 --olYoffset01=0 --olCropXoffset01=0 --olCropYoffset01=0 --olCropWidth01=960 --olCropHeight01=1080 --olBitmapY01=200 --olBitmapU01=200 --olBitmapV01=200 \
--olFormat02=1 --olAlpha02=255 --olWidth02=4096 --olHeight02=2160 --olXoffset02=960 --olYoffset02=0 --olCropXoffset02=64 --olCropYoffset02=66 --olCropWidth02=960 --olCropHeight02=1080 --olBitmapY02=200 --olBitmapU02=200 --olBitmapV02=200 \
--olInput01=/root/workspace/data/vsi_stream/enc/yuv_release/DinSko_4096x2160.yuv --olInput02=/root/workspace/data/vsi_stream/enc/yuv_release/DinSko_4096x2160.yuv \
--overlayEnables=03 \
--lumWidthSrc=1920 --lumHeightSrc=1080 --width=1920 --height=1080 --codecFormat=h264 
## osd overlay input format
/root/workspace/code/vpu_testsuit/input/vc9000e/app/cmodel/ASM_n/h264_testenc_vce --input=/root/workspace/data/vsi_stream/enc/foreman_176x144_3f.nv12 --output=/root/workspace/code/vpu_testsuit/output/201/foreman_176x144_3f.nv12.h264\
--olFormat01=0 --olAlpha01=0 --olWidth01=4096 --olHeight01=2048 --olXoffset01=6 --olYoffset01=6 --olYStride01=0 --olUVStride01=0 --olCropXoffset01=8 --olCropYoffset01=0 --olCropWidth01=56 --olCropHeight01=16 --olBitmapY01=200 --olBitmapU01=200 --olBitmapV01=200 \
--olFormat02=1 --olAlpha02=120 --olWidth02=352 --olHeight02=288 --olXoffset02=22 --olYoffset02=74 --olYStride02=352 --olUVStride02=352 --olCropXoffset02=8 --olCropYoffset02=4 --olCropWidth02=66 --olCropHeight02=48 --olBitmapY02=200 --olBitmapU02=200 --olBitmapV02=200 \
--olFormat03=2 --olAlpha03=120 --olWidth03=80 --olHeight03=80 --olXoffset03=128 --olYoffset03=0 --olYStride03=80 --olUVStride03=80 --olCropXoffset03=0 --olCropYoffset03=0 --olCropWidth03=16 --olCropHeight03=80 --olBitmapY03=200 --olBitmapU03=200 --olBitmapV03=200 \
--olInput01=/home/<USER>/data/yuv_release/osd_RandomAlpha_4096_2048.rgb888 --olInput02=/home/<USER>/data/yuv_release/kuubaRandom_352x288.nv12 --olInput03=/home/<USER>/data/yuv_release/osd_80_80.bmp \
--overlayEnables=7 \
--lumWidthSrc=176 --lumHeightSrc=144 --width=176 --height=144 --profile=12 --codecFormat=h264 --inputAlignmentExp=0 --aqInfoAlignmentExp=6 --bitDepthLuma=10 --bitDepthChroma=10 --refAlignmentExp=0 --refChromaAlignmentExp=6 --md5
### ARGB8888
/root/workspace/code/vpu_testsuit/input/vc9000e/app/cmodel/ASM_y/h264_testenc_vce --input=/root/workspace/data/vsi_stream/enc/yuv_release/pedestrianArea_1920x1080_5f.yuv --output=/root/workspace/code/vpu_testsuit/output/1/pedestrianArea_1920x1080_5f.yuv.h264 \
--olFormat01=0 --olAlpha01=0 --olWidth01=4096 --olHeight01=2048 --olCropWidth01=64 --olCropHeight01=192 --olBitmapY01=200 --olBitmapU01=200 --olBitmapV01=200 \
--olInput01=/root/workspace/data/vsi_stream/enc/yuv_release/osd_RandomAlpha_4096_2048.rgb888 \
--overlayEnables=01 \
--lumWidthSrc=1920 --lumHeightSrc=1080 --width=1920 --height=1080 --codecFormat=h264 
### NV12
/root/workspace/code/vpu_testsuit/input/vc9000e/app/cmodel/ASM_y/h264_testenc_vce --input=/root/workspace/data/vsi_stream/enc/yuv_release/pedestrianArea_1920x1080_5f.yuv --output=/root/workspace/code/vpu_testsuit/output/1/pedestrianArea_1920x1080_5f.yuv.h264 \
--olFormat01=1 --olAlpha01=255 --olWidth01=352 --olHeight01=288 --olCropWidth01=64 --olCropHeight01=192 --olBitmapY01=200 --olBitmapU01=200 --olBitmapV01=200 \
--olInput01=/root/workspace/data/vsi_stream/enc/yuv_release/kuubaRandom_352x288.nv12 \
--overlayEnables=01 \
--lumWidthSrc=1920 --lumHeightSrc=1080 --width=1920 --height=1080 --codecFormat=h264 
### bitmap
/root/workspace/code/vpu_testsuit/input/vc9000e/app/cmodel/ASM_y/h264_testenc_vce --input=/root/workspace/data/vsi_stream/enc/yuv_release/pedestrianArea_1920x1080_5f.yuv --output=/root/workspace/code/vpu_testsuit/output/1/pedestrianArea_1920x1080_5f.yuv.h264 \
--olFormat01=2 --olAlpha01=255 --olWidth01=80 --olHeight01=80 --olCropWidth01=0 --olCropHeight01=0 --olBitmapY01=200 --olBitmapU01=200 --olBitmapV01=200 \
--olInput01=/root/workspace/data/vsi_stream/enc/yuv_release/osd_80_80.bmp \
--overlayEnables=01 \
--lumWidthSrc=1920 --lumHeightSrc=1080 --width=1920 --height=1080 --codecFormat=h264 

# osd map max regions

# osd map full overlay

# osd mosaic max regions

# osd mosaic full overlay