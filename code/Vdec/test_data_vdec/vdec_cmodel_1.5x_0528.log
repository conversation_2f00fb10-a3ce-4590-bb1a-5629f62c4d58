+ /root/workspace/code/vpu_testsuit/input/vc9000d/app/cmodel/g2dec /root/workspace/data/265input_file/input_960x540.265 --output-file=/root/workspace/code/vpu_testsuit/output/246/input_960x540.265.yuv --num-pictures=100000 --md5 --pp=0 --antialias=0 --pp-filter=BICUBIC --scale=1920x1080

[TB] * * * * * * * * * * * * * * * * * * * * * * * 


[TB]        VERISILICON G2 DECODER TESTBENCH


[TB] * * * * * * * * * * * * * * * * * * * * * * * 
[TB] VC9000 Decoder API version 3.0.112
[TB] Hardware Build ID: 0x2023, ASIC_ID 0x90012010, Software Build: 0x1087a2
Decoder state change: DECODER_WAITING_HEADERS => DECODER_WAITING_HEADERS
[TB] struct TBCfg: Decoder Data Discard 0
[TB] struct TBCfg: Decoder Latency Compensation 0
[TB] struct TBCfg: Decoder Output Picture Endian 1
[TB] struct TBCfg: Decoder Bus Burst Length 16
[TB] struct TBCfg: Decoder Asic Service Priority 0
[TB] struct TBCfg: Decoder Service Merge Disable 0
[TB][In] Allocate EXT index 0 buffer 0x7f0f06567800<vir=0x7f0f06567800> with size<1048576, 1048576>
[TB][In] Allocate EXT index 1 buffer 0x7f0f06466800<vir=0x7f0f06466800> with size<1048576, 1048576>
[TB][In] Allocate EXT index 2 buffer 0x7f0f06365800<vir=0x7f0f06365800> with size<1048576, 1048576>
[TB][In] Allocate EXT index 3 buffer 0x7f0f06264800<vir=0x7f0f06264800> with size<1048576, 1048576>
[TB][In] Allocate EXT index 4 buffer 0x7f0f06163800<vir=0x7f0f06163800> with size<1048576, 1048576>
[TB] NOTE: for INTERLACED stream, the resolution is of a FRAME.
[TB] Headers: Width 960 Height 544
[TB] Headers: Cropping params: (0, 0) 960x540
[TB] Headers: MonoChrome = 0
[TB] Headers: Pictures in DPB = 6
[TB] Headers: video_range 0
[TB] Headers: matrix_coefficients 2
[TB] Headers: PROGRESSIVE sequence
[TB] Headers: bit_depth = Y8C8
[TB] Headers: chroma_format_idc = 1
Decoder state change: DECODER_WAITING_HEADERS => DECODER_WAITING_RESOURCES
[TB][Out] Allocate EXT index 0 buffer 0x7f0f051d1800<vir=0x7f0f051d1800> with size<3110400, 3110912>
Decoder state change: DECODER_WAITING_RESOURCES => DECODER_DECODING
[TB][Out] Allocate EXT index 1 buffer 0x7f0f04ed8800<vir=0x7f0f04ed8800> with size<3110400, 3110912>
Decoder state change: DECODER_DECODING => DECODER_DECODING
[TB][Out] Allocate EXT index 2 buffer 0x7f0f04bdf800<vir=0x7f0f04bdf800> with size<3110400, 3110912>
Decoder state change: DECODER_DECODING => DECODER_DECODING
[TB][Out] Allocate EXT index 3 buffer 0x7f0f048e6800<vir=0x7f0f048e6800> with size<3110400, 3110912>
Decoder state change: DECODER_DECODING => DECODER_DECODING
[TB][Out] Allocate EXT index 4 buffer 0x7f0f045ed800<vir=0x7f0f045ed800> with size<3110400, 3110912>
Decoder state change: DECODER_DECODING => DECODER_DECODING
[TB][Out] Allocate EXT index 5 buffer 0x7f0f042f4800<vir=0x7f0f042f4800> with size<3110400, 3110912>
Decoder state change: DECODER_DECODING => DECODER_DECODING
[TB] Note: the mb number in cycles/mb is calculated by original size of stream, not
[TB] final output size of PostProcessing!
[TB] PIC  1/ 1, type         IDR,1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] SEI successful: got the SEI(HEVC), which belongs to PIC  1/ 1 
[TB] PIC  2/ 4, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC  3/ 3, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC  4/ 5, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC  5/ 2, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC  6/ 8, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC  7/ 7, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC  8/ 9, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC  9/ 6, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 10/12, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 11/11, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 12/10, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 13/15, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 14/14, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 15/16, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 16/13, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 17/18, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 18/17, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 19/21, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 20/20, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 21/22, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 22/19, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 23/25, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 24/24, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 25/26, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 26/23, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 27/29, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 28/28, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 29/30, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 30/27, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 31/33, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 32/32, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 33/34, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 34/31, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 35/37, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 36/36, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 37/38, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 38/35, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 39/41, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 40/40, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 41/42, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 42/39, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 43/45, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 44/44, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 45/46, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 46/43, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 47/49, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 48/48, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 49/50, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 50/47, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 51/53, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 52/52, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 53/54, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 54/51, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 55/57, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 56/56, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 57/58, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 58/55, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 59/61, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 60/60, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 61/62, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 62/59, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 63/65, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 64/64, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 65/66, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 66/63, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 67/69, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 68/68, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 69/70, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 70/67, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 71/72, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 72/71, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 73/75, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 74/74, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 75/76, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 76/73, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 77/79, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 78/78, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 79/80, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 80/77, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 81/83, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 82/82, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 83/84, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 84/81, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 85/87, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 86/88, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 87/86, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 88/89, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 89/85, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 90/92, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 91/91, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 92/93, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 93/90, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 94/96, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 95/97, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 96/95, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 97/98, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 98/94, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 99/100, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 100/99, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 960 x 540 
Decoder state change: DECODER_DECODING => DECODER_SHUTTING_DOWN
Decoder state change: DECODER_SHUTTING_DOWN => DECODER_TERMINATED
[TB][In] Free EXT index 0 buffer 0x7f0f06567800<vir=0x7f0f06567800>
[TB][In] Free EXT index 1 buffer 0x7f0f06466800<vir=0x7f0f06466800>
[TB][In] Free EXT index 2 buffer 0x7f0f06365800<vir=0x7f0f06365800>
[TB][In] Free EXT index 3 buffer 0x7f0f06264800<vir=0x7f0f06264800>
[TB][In] Free EXT index 4 buffer 0x7f0f06163800<vir=0x7f0f06163800>
[TB][Out] Free EXT index 0 buffer 0x7f0f051d1800<vir=0x7f0f051d1800>
[TB][Out] Free EXT index 1 buffer 0x7f0f04ed8800<vir=0x7f0f04ed8800>
[TB][Out] Free EXT index 2 buffer 0x7f0f04bdf800<vir=0x7f0f04bdf800>
[TB][Out] Free EXT index 3 buffer 0x7f0f048e6800<vir=0x7f0f048e6800>
[TB][Out] Free EXT index 4 buffer 0x7f0f045ed800<vir=0x7f0f045ed800>
[TB][Out] Free EXT index 5 buffer 0x7f0f042f4800<vir=0x7f0f042f4800>
[TB] Output file: /root/workspace/code/vpu_testsuit/output/246/input_960x540.265_8b_0.yuv

Multi-core usage statistics:
	Core[ 0] used    100 times (100%)

+ /root/workspace/code/vpu_testsuit/input/vc9000d/app/cmodel/g2dec /root/workspace/data/265input_file/input_640x360.265 --output-file=/root/workspace/code/vpu_testsuit/output/247/input_640x360.265.yuv --num-pictures=100000 --md5 --pp=0 --antialias=0 --pp-filter=BICUBIC --scale=1920x1080

[TB] * * * * * * * * * * * * * * * * * * * * * * * 


[TB]        VERISILICON G2 DECODER TESTBENCH


[TB] * * * * * * * * * * * * * * * * * * * * * * * 
[TB] VC9000 Decoder API version 3.0.112
[TB] Hardware Build ID: 0x2023, ASIC_ID 0x90012010, Software Build: 0x1087a2
Decoder state change: DECODER_WAITING_HEADERS => DECODER_WAITING_HEADERS
[TB] struct TBCfg: Decoder Data Discard 0
[TB] struct TBCfg: Decoder Latency Compensation 0
[TB] struct TBCfg: Decoder Output Picture Endian 1
[TB] struct TBCfg: Decoder Bus Burst Length 16
[TB] struct TBCfg: Decoder Asic Service Priority 0
[TB] struct TBCfg: Decoder Service Merge Disable 0
[TB][In] Allocate EXT index 0 buffer 0x7f1e210fa800<vir=0x7f1e210fa800> with size<1048576, 1048576>
[TB][In] Allocate EXT index 1 buffer 0x7f1e20ff9800<vir=0x7f1e20ff9800> with size<1048576, 1048576>
[TB][In] Allocate EXT index 2 buffer 0x7f1e20ef8800<vir=0x7f1e20ef8800> with size<1048576, 1048576>
[TB][In] Allocate EXT index 3 buffer 0x7f1e20df7800<vir=0x7f1e20df7800> with size<1048576, 1048576>
[TB][In] Allocate EXT index 4 buffer 0x7f1e20cf6800<vir=0x7f1e20cf6800> with size<1048576, 1048576>
[TB] NOTE: for INTERLACED stream, the resolution is of a FRAME.
[TB] Headers: Width 640 Height 360
[TB] Headers: Cropping params: (0, 0) 640x360
[TB] Headers: MonoChrome = 0
[TB] Headers: Pictures in DPB = 6
[TB] Headers: video_range 0
[TB] Headers: matrix_coefficients 2
[TB] Headers: PROGRESSIVE sequence
[TB] Headers: bit_depth = Y8C8
[TB] Headers: chroma_format_idc = 1
Decoder state change: DECODER_WAITING_HEADERS => DECODER_WAITING_RESOURCES
[TB][Out] Allocate EXT index 0 buffer 0x7f1e1bd07800<vir=0x7f1e1bd07800> with size<3110400, 3110912>
Decoder state change: DECODER_WAITING_RESOURCES => DECODER_DECODING
[TB][Out] Allocate EXT index 1 buffer 0x7f1e1ba0e800<vir=0x7f1e1ba0e800> with size<3110400, 3110912>
Decoder state change: DECODER_DECODING => DECODER_DECODING
[TB][Out] Allocate EXT index 2 buffer 0x7f1e1b715800<vir=0x7f1e1b715800> with size<3110400, 3110912>
Decoder state change: DECODER_DECODING => DECODER_DECODING
[TB][Out] Allocate EXT index 3 buffer 0x7f1e1b41c800<vir=0x7f1e1b41c800> with size<3110400, 3110912>
Decoder state change: DECODER_DECODING => DECODER_DECODING
[TB][Out] Allocate EXT index 4 buffer 0x7f1e1b123800<vir=0x7f1e1b123800> with size<3110400, 3110912>
Decoder state change: DECODER_DECODING => DECODER_DECODING
[TB][Out] Allocate EXT index 5 buffer 0x7f1e1ae2a800<vir=0x7f1e1ae2a800> with size<3110400, 3110912>
Decoder state change: DECODER_DECODING => DECODER_DECODING
[TB] Note: the mb number in cycles/mb is calculated by original size of stream, not
[TB] final output size of PostProcessing!
[TB] PIC  1/ 1, type         IDR,1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] SEI successful: got the SEI(HEVC), which belongs to PIC  1/ 1 
[TB] PIC  2/ 3, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC  3/ 2, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC  4/ 6, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC  5/ 5, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC  6/ 7, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC  7/ 4, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC  8/10, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC  9/11, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 10/ 9, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 11/12, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 12/ 8, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 13/15, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 14/14, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 15/16, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 16/13, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 17/19, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 18/18, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 19/20, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 20/17, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 21/23, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 22/22, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 23/24, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 24/21, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 25/27, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 26/26, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 27/28, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 28/25, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 29/31, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 30/30, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 31/32, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 32/29, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 33/35, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 34/34, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 35/36, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 36/33, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 37/39, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 38/38, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 39/40, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 40/37, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 41/43, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 42/42, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 43/44, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 44/41, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 45/47, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 46/46, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 47/48, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 48/45, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 49/51, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 50/50, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 51/52, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 52/49, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 53/55, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 54/54, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 55/56, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 56/53, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 57/59, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 58/58, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 59/60, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 60/57, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 61/63, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 62/62, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 63/64, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 64/61, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 65/67, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 66/66, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 67/68, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 68/65, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 69/71, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 70/70, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 71/72, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 72/69, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 73/75, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 74/74, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 75/76, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 76/73, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 77/79, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 78/78, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 79/80, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 80/77, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 81/83, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 82/82, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 83/84, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 84/81, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 85/87, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 86/88, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 87/86, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 88/89, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 89/85, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 90/92, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 91/91, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 92/93, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 93/90, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 94/96, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 95/97, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 96/95, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 97/98, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 98/94, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 99/100, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 100/99, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 640 x 360 
Decoder state change: DECODER_DECODING => DECODER_SHUTTING_DOWN
Decoder state change: DECODER_SHUTTING_DOWN => DECODER_TERMINATED
[TB][In] Free EXT index 0 buffer 0x7f1e210fa800<vir=0x7f1e210fa800>
[TB][In] Free EXT index 1 buffer 0x7f1e20ff9800<vir=0x7f1e20ff9800>
[TB][In] Free EXT index 2 buffer 0x7f1e20ef8800<vir=0x7f1e20ef8800>
[TB][In] Free EXT index 3 buffer 0x7f1e20df7800<vir=0x7f1e20df7800>
[TB][In] Free EXT index 4 buffer 0x7f1e20cf6800<vir=0x7f1e20cf6800>
[TB][Out] Free EXT index 0 buffer 0x7f1e1bd07800<vir=0x7f1e1bd07800>
[TB][Out] Free EXT index 1 buffer 0x7f1e1ba0e800<vir=0x7f1e1ba0e800>
[TB][Out] Free EXT index 2 buffer 0x7f1e1b715800<vir=0x7f1e1b715800>
[TB][Out] Free EXT index 3 buffer 0x7f1e1b41c800<vir=0x7f1e1b41c800>
[TB][Out] Free EXT index 4 buffer 0x7f1e1b123800<vir=0x7f1e1b123800>
[TB][Out] Free EXT index 5 buffer 0x7f1e1ae2a800<vir=0x7f1e1ae2a800>
[TB] Output file: /root/workspace/code/vpu_testsuit/output/247/input_640x360.265_8b_0.yuv

Multi-core usage statistics:
	Core[ 0] used    100 times (100%)

+ /root/workspace/code/vpu_testsuit/input/vc9000d/app/cmodel/g2dec /root/workspace/data/265input_file/input_1280x720.265 --output-file=/root/workspace/code/vpu_testsuit/output/248/input_1280x720.265.yuv --num-pictures=100000 --md5 --pp=0 --antialias=0 --pp-filter=BICUBIC --scale=1920x1080

[TB] * * * * * * * * * * * * * * * * * * * * * * * 


[TB]        VERISILICON G2 DECODER TESTBENCH


[TB] * * * * * * * * * * * * * * * * * * * * * * * 
[TB] VC9000 Decoder API version 3.0.112
[TB] Hardware Build ID: 0x2023, ASIC_ID 0x90012010, Software Build: 0x1087a2
Decoder state change: DECODER_WAITING_HEADERS => DECODER_WAITING_HEADERS
[TB] struct TBCfg: Decoder Data Discard 0
[TB] struct TBCfg: Decoder Latency Compensation 0
[TB] struct TBCfg: Decoder Output Picture Endian 1
[TB] struct TBCfg: Decoder Bus Burst Length 16
[TB] struct TBCfg: Decoder Asic Service Priority 0
[TB] struct TBCfg: Decoder Service Merge Disable 0
[TB][In] Allocate EXT index 0 buffer 0x7f738e6a6800<vir=0x7f738e6a6800> with size<1048576, 1048576>
[TB][In] Allocate EXT index 1 buffer 0x7f738e5a5800<vir=0x7f738e5a5800> with size<1048576, 1048576>
[TB][In] Allocate EXT index 2 buffer 0x7f738e4a4800<vir=0x7f738e4a4800> with size<1048576, 1048576>
[TB][In] Allocate EXT index 3 buffer 0x7f738e3a3800<vir=0x7f738e3a3800> with size<1048576, 1048576>
[TB][In] Allocate EXT index 4 buffer 0x7f738e2a2800<vir=0x7f738e2a2800> with size<1048576, 1048576>
[TB] NOTE: for INTERLACED stream, the resolution is of a FRAME.
[TB] Headers: Width 1280 Height 720
[TB] Headers: Cropping params: (0, 0) 1280x720
[TB] Headers: MonoChrome = 0
[TB] Headers: Pictures in DPB = 6
[TB] Headers: video_range 0
[TB] Headers: matrix_coefficients 2
[TB] Headers: PROGRESSIVE sequence
[TB] Headers: bit_depth = Y8C8
[TB] Headers: chroma_format_idc = 1
Decoder state change: DECODER_WAITING_HEADERS => DECODER_WAITING_RESOURCES
[TB][Out] Allocate EXT index 0 buffer 0x7f738cf98800<vir=0x7f738cf98800> with size<3110400, 3110912>
Decoder state change: DECODER_WAITING_RESOURCES => DECODER_DECODING
[TB][Out] Allocate EXT index 1 buffer 0x7f738cc9f800<vir=0x7f738cc9f800> with size<3110400, 3110912>
Decoder state change: DECODER_DECODING => DECODER_DECODING
[TB][Out] Allocate EXT index 2 buffer 0x7f738c9a6800<vir=0x7f738c9a6800> with size<3110400, 3110912>
Decoder state change: DECODER_DECODING => DECODER_DECODING
[TB][Out] Allocate EXT index 3 buffer 0x7f738c6ad800<vir=0x7f738c6ad800> with size<3110400, 3110912>
Decoder state change: DECODER_DECODING => DECODER_DECODING
[TB][Out] Allocate EXT index 4 buffer 0x7f738c3b4800<vir=0x7f738c3b4800> with size<3110400, 3110912>
Decoder state change: DECODER_DECODING => DECODER_DECODING
[TB][Out] Allocate EXT index 5 buffer 0x7f738c0bb800<vir=0x7f738c0bb800> with size<3110400, 3110912>
Decoder state change: DECODER_DECODING => DECODER_DECODING
[TB] Note: the mb number in cycles/mb is calculated by original size of stream, not
[TB] final output size of PostProcessing!
[TB] PIC  1/ 1, type         IDR,1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] SEI successful: got the SEI(HEVC), which belongs to PIC  1/ 1 
[TB] PIC  2/ 3, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC  3/ 2, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC  4/ 6, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC  5/ 5, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC  6/ 7, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC  7/ 4, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC  8/10, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC  9/11, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 10/ 9, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 11/12, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 12/ 8, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 13/15, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 14/14, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 15/16, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 16/13, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 17/19, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 18/18, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 19/20, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 20/17, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 21/23, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 22/22, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 23/24, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 24/21, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 25/27, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 26/26, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 27/28, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 28/25, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 29/31, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 30/30, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 31/32, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 32/29, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 33/35, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 34/34, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 35/36, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 36/33, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 37/39, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 38/38, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 39/40, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 40/37, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 41/43, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 42/42, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 43/44, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 44/41, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 45/47, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 46/46, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 47/48, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 48/45, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 49/51, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 50/50, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 51/52, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 52/49, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 53/55, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 54/54, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 55/56, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 56/53, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 57/59, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 58/58, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 59/60, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 60/57, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 61/63, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 62/62, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 63/64, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 64/61, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 65/67, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 66/66, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 67/68, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 68/65, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 69/71, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 70/70, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 71/72, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 72/69, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 73/75, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 74/74, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 75/76, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 76/73, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 77/79, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 78/78, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 79/80, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 80/77, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 81/83, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 82/82, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 83/84, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 84/81, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 85/87, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 86/88, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 87/86, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 88/89, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 89/85, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 90/92, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 91/91, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 92/93, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 93/90, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 94/96, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 95/97, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 96/95, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 97/98, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 98/94, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 99/100, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 100/99, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 1280 x 720 
Decoder state change: DECODER_DECODING => DECODER_SHUTTING_DOWN
Decoder state change: DECODER_SHUTTING_DOWN => DECODER_TERMINATED
[TB][In] Free EXT index 0 buffer 0x7f738e6a6800<vir=0x7f738e6a6800>
[TB][In] Free EXT index 1 buffer 0x7f738e5a5800<vir=0x7f738e5a5800>
[TB][In] Free EXT index 2 buffer 0x7f738e4a4800<vir=0x7f738e4a4800>
[TB][In] Free EXT index 3 buffer 0x7f738e3a3800<vir=0x7f738e3a3800>
[TB][In] Free EXT index 4 buffer 0x7f738e2a2800<vir=0x7f738e2a2800>
[TB][Out] Free EXT index 0 buffer 0x7f738cf98800<vir=0x7f738cf98800>
[TB][Out] Free EXT index 1 buffer 0x7f738cc9f800<vir=0x7f738cc9f800>
[TB][Out] Free EXT index 2 buffer 0x7f738c9a6800<vir=0x7f738c9a6800>
[TB][Out] Free EXT index 3 buffer 0x7f738c6ad800<vir=0x7f738c6ad800>
[TB][Out] Free EXT index 4 buffer 0x7f738c3b4800<vir=0x7f738c3b4800>
[TB][Out] Free EXT index 5 buffer 0x7f738c0bb800<vir=0x7f738c0bb800>
[TB] Output file: /root/workspace/code/vpu_testsuit/output/248/input_1280x720.265_8b_0.yuv

Multi-core usage statistics:
	Core[ 0] used    100 times (100%)

+ /root/workspace/code/vpu_testsuit/input/vc9000d/app/cmodel/g2dec /root/workspace/data/265input_file/input_768x432.265 --output-file=/root/workspace/code/vpu_testsuit/output/249/input_768x432.265.yuv --num-pictures=100000 --md5 --pp=0 --antialias=0 --pp-filter=BICUBIC --scale=1920x1080

[TB] * * * * * * * * * * * * * * * * * * * * * * * 


[TB]        VERISILICON G2 DECODER TESTBENCH


[TB] * * * * * * * * * * * * * * * * * * * * * * * 
[TB] VC9000 Decoder API version 3.0.112
[TB] Hardware Build ID: 0x2023, ASIC_ID 0x90012010, Software Build: 0x1087a2
Decoder state change: DECODER_WAITING_HEADERS => DECODER_WAITING_HEADERS
[TB] struct TBCfg: Decoder Data Discard 0
[TB] struct TBCfg: Decoder Latency Compensation 0
[TB] struct TBCfg: Decoder Output Picture Endian 1
[TB] struct TBCfg: Decoder Bus Burst Length 16
[TB] struct TBCfg: Decoder Asic Service Priority 0
[TB] struct TBCfg: Decoder Service Merge Disable 0
[TB][In] Allocate EXT index 0 buffer 0x7f4816d34800<vir=0x7f4816d34800> with size<1048576, 1048576>
[TB][In] Allocate EXT index 1 buffer 0x7f4816c33800<vir=0x7f4816c33800> with size<1048576, 1048576>
[TB][In] Allocate EXT index 2 buffer 0x7f4816b32800<vir=0x7f4816b32800> with size<1048576, 1048576>
[TB][In] Allocate EXT index 3 buffer 0x7f4816a31800<vir=0x7f4816a31800> with size<1048576, 1048576>
[TB][In] Allocate EXT index 4 buffer 0x7f4816930800<vir=0x7f4816930800> with size<1048576, 1048576>
[TB] NOTE: for INTERLACED stream, the resolution is of a FRAME.
[TB] Headers: Width 768 Height 432
[TB] Headers: Cropping params: (0, 0) 768x432
[TB] Headers: MonoChrome = 0
[TB] Headers: Pictures in DPB = 6
[TB] Headers: video_range 0
[TB] Headers: matrix_coefficients 2
[TB] Headers: PROGRESSIVE sequence
[TB] Headers: bit_depth = Y8C8
[TB] Headers: chroma_format_idc = 1
Decoder state change: DECODER_WAITING_HEADERS => DECODER_WAITING_RESOURCES
[TB][Out] Allocate EXT index 0 buffer 0x7f4815b48800<vir=0x7f4815b48800> with size<3110400, 3110912>
Decoder state change: DECODER_WAITING_RESOURCES => DECODER_DECODING
[TB][Out] Allocate EXT index 1 buffer 0x7f481584f800<vir=0x7f481584f800> with size<3110400, 3110912>
Decoder state change: DECODER_DECODING => DECODER_DECODING
[TB][Out] Allocate EXT index 2 buffer 0x7f4815556800<vir=0x7f4815556800> with size<3110400, 3110912>
Decoder state change: DECODER_DECODING => DECODER_DECODING
[TB][Out] Allocate EXT index 3 buffer 0x7f481525d800<vir=0x7f481525d800> with size<3110400, 3110912>
Decoder state change: DECODER_DECODING => DECODER_DECODING
[TB][Out] Allocate EXT index 4 buffer 0x7f4814f64800<vir=0x7f4814f64800> with size<3110400, 3110912>
Decoder state change: DECODER_DECODING => DECODER_DECODING
[TB][Out] Allocate EXT index 5 buffer 0x7f4814c6b800<vir=0x7f4814c6b800> with size<3110400, 3110912>
Decoder state change: DECODER_DECODING => DECODER_DECODING
[TB] Note: the mb number in cycles/mb is calculated by original size of stream, not
[TB] final output size of PostProcessing!
[TB] PIC  1/ 1, type         IDR,1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] SEI successful: got the SEI(HEVC), which belongs to PIC  1/ 1 
[TB] PIC  2/ 3, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC  3/ 2, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC  4/ 6, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC  5/ 5, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC  6/ 7, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC  7/ 4, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC  8/10, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC  9/11, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 10/ 9, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 11/12, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 12/ 8, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 13/15, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 14/14, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 15/16, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 16/13, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 17/19, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 18/18, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 19/20, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 20/17, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 21/23, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 22/22, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 23/24, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 24/21, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 25/27, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 26/26, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 27/28, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 28/25, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 29/31, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 30/30, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 31/32, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 32/29, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 33/35, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 34/34, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 35/36, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 36/33, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 37/39, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 38/38, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 39/40, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 40/37, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 41/43, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 42/42, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 43/44, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 44/41, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 45/47, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 46/46, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 47/48, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 48/45, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 49/51, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 50/50, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 51/52, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 52/49, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 53/55, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 54/54, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 55/56, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 56/53, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 57/59, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 58/58, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 59/60, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 60/57, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 61/63, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 62/62, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 63/64, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 64/61, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 65/67, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 66/66, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 67/68, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 68/65, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 69/71, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 70/70, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 71/72, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 72/69, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 73/75, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 74/74, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 75/76, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 76/73, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 77/79, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 78/78, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 79/80, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 80/77, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 81/83, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 82/82, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 83/84, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 84/81, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 85/87, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 86/88, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 87/86, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 88/89, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 89/85, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 90/92, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 91/91, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 92/93, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 93/90, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 94/96, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 95/97, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 96/95, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 97/98, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 98/94, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 99/100, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 100/99, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 768 x 432 
Decoder state change: DECODER_DECODING => DECODER_SHUTTING_DOWN
Decoder state change: DECODER_SHUTTING_DOWN => DECODER_TERMINATED
[TB][In] Free EXT index 0 buffer 0x7f4816d34800<vir=0x7f4816d34800>
[TB][In] Free EXT index 1 buffer 0x7f4816c33800<vir=0x7f4816c33800>
[TB][In] Free EXT index 2 buffer 0x7f4816b32800<vir=0x7f4816b32800>
[TB][In] Free EXT index 3 buffer 0x7f4816a31800<vir=0x7f4816a31800>
[TB][In] Free EXT index 4 buffer 0x7f4816930800<vir=0x7f4816930800>
[TB][Out] Free EXT index 0 buffer 0x7f4815b48800<vir=0x7f4815b48800>
[TB][Out] Free EXT index 1 buffer 0x7f481584f800<vir=0x7f481584f800>
[TB][Out] Free EXT index 2 buffer 0x7f4815556800<vir=0x7f4815556800>
[TB][Out] Free EXT index 3 buffer 0x7f481525d800<vir=0x7f481525d800>
[TB][Out] Free EXT index 4 buffer 0x7f4814f64800<vir=0x7f4814f64800>
[TB][Out] Free EXT index 5 buffer 0x7f4814c6b800<vir=0x7f4814c6b800>
[TB] Output file: /root/workspace/code/vpu_testsuit/output/249/input_768x432.265_8b_0.yuv

Multi-core usage statistics:
	Core[ 0] used    100 times (100%)

+ /root/workspace/code/vpu_testsuit/input/vc9000d/app/cmodel/g2dec /root/workspace/data/265input_file/input_1080p.265 --output-file=/root/workspace/code/vpu_testsuit/output/250/input_1080p.265.yuv --num-pictures=100000 --md5 --pp=0 --antialias=0 --pp-filter=BILINEAR --scale=1920x1080

[TB] * * * * * * * * * * * * * * * * * * * * * * * 


[TB]        VERISILICON G2 DECODER TESTBENCH


[TB] * * * * * * * * * * * * * * * * * * * * * * * 
[TB] VC9000 Decoder API version 3.0.112
[TB] Hardware Build ID: 0x2023, ASIC_ID 0x90012010, Software Build: 0x1087a2
Decoder state change: DECODER_WAITING_HEADERS => DECODER_WAITING_HEADERS
[TB] struct TBCfg: Decoder Data Discard 0
[TB] struct TBCfg: Decoder Latency Compensation 0
[TB] struct TBCfg: Decoder Output Picture Endian 1
[TB] struct TBCfg: Decoder Bus Burst Length 16
[TB] struct TBCfg: Decoder Asic Service Priority 0
[TB] struct TBCfg: Decoder Service Merge Disable 0
[TB][In] Allocate EXT index 0 buffer 0x7fade3eff800<vir=0x7fade3eff800> with size<1048576, 1048576>
[TB][In] Allocate EXT index 1 buffer 0x7fade3dfe800<vir=0x7fade3dfe800> with size<1048576, 1048576>
[TB][In] Allocate EXT index 2 buffer 0x7fade3cfd800<vir=0x7fade3cfd800> with size<1048576, 1048576>
[TB][In] Allocate EXT index 3 buffer 0x7fade3bfc800<vir=0x7fade3bfc800> with size<1048576, 1048576>
[TB][In] Allocate EXT index 4 buffer 0x7fade3afb800<vir=0x7fade3afb800> with size<1048576, 1048576>
[TB] NOTE: for INTERLACED stream, the resolution is of a FRAME.
[TB] Headers: Width 1920 Height 1080
[TB] Headers: Cropping params: (0, 0) 1920x1080
[TB] Headers: MonoChrome = 0
[TB] Headers: Pictures in DPB = 7
[TB] Headers: video_range 0
[TB] Headers: matrix_coefficients 2
[TB] Headers: PROGRESSIVE sequence
[TB] Headers: bit_depth = Y8C8
[TB] Headers: chroma_format_idc = 1
Decoder state change: DECODER_WAITING_HEADERS => DECODER_WAITING_RESOURCES
[TB][Out] Allocate EXT index 0 buffer 0x7fade1a5a800<vir=0x7fade1a5a800> with size<3110400, 3110912>
Decoder state change: DECODER_WAITING_RESOURCES => DECODER_DECODING
[TB][Out] Allocate EXT index 1 buffer 0x7fade1761800<vir=0x7fade1761800> with size<3110400, 3110912>
Decoder state change: DECODER_DECODING => DECODER_DECODING
[TB][Out] Allocate EXT index 2 buffer 0x7fade1468800<vir=0x7fade1468800> with size<3110400, 3110912>
Decoder state change: DECODER_DECODING => DECODER_DECODING
[TB][Out] Allocate EXT index 3 buffer 0x7fade116f800<vir=0x7fade116f800> with size<3110400, 3110912>
Decoder state change: DECODER_DECODING => DECODER_DECODING
[TB][Out] Allocate EXT index 4 buffer 0x7fade0e76800<vir=0x7fade0e76800> with size<3110400, 3110912>
Decoder state change: DECODER_DECODING => DECODER_DECODING
[TB][Out] Allocate EXT index 5 buffer 0x7fade0b7d800<vir=0x7fade0b7d800> with size<3110400, 3110912>
Decoder state change: DECODER_DECODING => DECODER_DECODING
[TB][Out] Allocate EXT index 6 buffer 0x7fade0884800<vir=0x7fade0884800> with size<3110400, 3110912>
Decoder state change: DECODER_DECODING => DECODER_DECODING
[TB] Note: the mb number in cycles/mb is calculated by original size of stream, not
[TB] final output size of PostProcessing!
[TB] PIC  1/ 1, type         IDR,1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] SEI successful: got the SEI(HEVC), which belongs to PIC  1/ 1 
[TB] PIC  2/ 4, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] PIC  3/ 3, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] PIC  4/ 5, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] PIC  5/ 2, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] PIC  6/ 8, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] PIC  7/ 7, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] PIC  8/ 9, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] PIC  9/ 6, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] PIC 10/12, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] PIC 11/13, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] PIC 12/11, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] PIC 13/14, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] PIC 14/10, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] PIC 15/17, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] PIC 16/16, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] PIC 17/18, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] PIC 18/15, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] PIC 19/21, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] PIC 20/20, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] PIC 21/22, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] PIC 22/19, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] PIC 23/25, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] PIC 24/24, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] PIC 25/26, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] PIC 26/23, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] PIC 27/29, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] PIC 28/28, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] PIC 29/30, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] PIC 30/27, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] PIC 31/33, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] PIC 32/32, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] PIC 33/34, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] PIC 34/31, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] PIC 35/37, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] PIC 36/36, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] PIC 37/38, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] PIC 38/35, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] PIC 39/41, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] PIC 40/40, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] PIC 41/42, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] PIC 42/39, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] PIC 43/45, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] PIC 44/44, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] PIC 45/46, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] PIC 46/43, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] PIC 47/49, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] PIC 48/48, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] PIC 49/50, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] PIC 50/47, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] PIC 51/53, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] PIC 52/52, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] PIC 53/54, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] PIC 54/51, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] PIC 55/57, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] PIC 56/56, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] PIC 57/58, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] PIC 58/55, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] PIC 59/61, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] PIC 60/60, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] PIC 61/62, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] PIC 62/59, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] PIC 63/65, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] PIC 64/64, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] PIC 65/66, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] PIC 66/63, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] PIC 67/69, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] PIC 68/68, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] PIC 69/70, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] PIC 70/67, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] PIC 71/73, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] PIC 72/72, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] PIC 73/74, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] PIC 74/71, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] PIC 75/77, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] PIC 76/76, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] PIC 77/78, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] PIC 78/75, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] PIC 79/81, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] PIC 80/80, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] PIC 81/82, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] PIC 82/79, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] PIC 83/85, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] PIC 84/84, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] PIC 85/86, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] PIC 86/83, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] PIC 87/89, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] PIC 88/90, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] PIC 89/88, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] PIC 90/91, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] PIC 91/87, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] PIC 92/94, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] PIC 93/93, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] PIC 94/95, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] PIC 95/92, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] PIC 96/98, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] PIC 97/99, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] PIC 98/97, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] PIC 99/100, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1920 x 1080 
[TB] PIC 100/96, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 1920 x 1080 
Decoder state change: DECODER_DECODING => DECODER_SHUTTING_DOWN
Decoder state change: DECODER_SHUTTING_DOWN => DECODER_TERMINATED
[TB][In] Free EXT index 0 buffer 0x7fade3eff800<vir=0x7fade3eff800>
[TB][In] Free EXT index 1 buffer 0x7fade3dfe800<vir=0x7fade3dfe800>
[TB][In] Free EXT index 2 buffer 0x7fade3cfd800<vir=0x7fade3cfd800>
[TB][In] Free EXT index 3 buffer 0x7fade3bfc800<vir=0x7fade3bfc800>
[TB][In] Free EXT index 4 buffer 0x7fade3afb800<vir=0x7fade3afb800>
[TB][Out] Free EXT index 0 buffer 0x7fade1a5a800<vir=0x7fade1a5a800>
[TB][Out] Free EXT index 1 buffer 0x7fade1761800<vir=0x7fade1761800>
[TB][Out] Free EXT index 2 buffer 0x7fade1468800<vir=0x7fade1468800>
[TB][Out] Free EXT index 3 buffer 0x7fade116f800<vir=0x7fade116f800>
[TB][Out] Free EXT index 4 buffer 0x7fade0e76800<vir=0x7fade0e76800>
[TB][Out] Free EXT index 5 buffer 0x7fade0b7d800<vir=0x7fade0b7d800>
[TB][Out] Free EXT index 6 buffer 0x7fade0884800<vir=0x7fade0884800>
[TB] Output file: /root/workspace/code/vpu_testsuit/output/250/input_1080p.265_8b_0.yuv

Multi-core usage statistics:
	Core[ 0] used    100 times (100%)

+ /root/workspace/code/vpu_testsuit/input/vc9000d/app/cmodel/g2dec /root/workspace/data/265input_file/input_960x540.265 --output-file=/root/workspace/code/vpu_testsuit/output/251/input_960x540.265.yuv --num-pictures=100000 --md5 --pp=0 --antialias=0 --pp-filter=BILINEAR --scale=1920x1080

[TB] * * * * * * * * * * * * * * * * * * * * * * * 


[TB]        VERISILICON G2 DECODER TESTBENCH


[TB] * * * * * * * * * * * * * * * * * * * * * * * 
[TB] VC9000 Decoder API version 3.0.112
[TB] Hardware Build ID: 0x2023, ASIC_ID 0x90012010, Software Build: 0x1087a2
Decoder state change: DECODER_WAITING_HEADERS => DECODER_WAITING_HEADERS
[TB] struct TBCfg: Decoder Data Discard 0
[TB] struct TBCfg: Decoder Latency Compensation 0
[TB] struct TBCfg: Decoder Output Picture Endian 1
[TB] struct TBCfg: Decoder Bus Burst Length 16
[TB] struct TBCfg: Decoder Asic Service Priority 0
[TB] struct TBCfg: Decoder Service Merge Disable 0
[TB][In] Allocate EXT index 0 buffer 0x7fa1aada3800<vir=0x7fa1aada3800> with size<1048576, 1048576>
[TB][In] Allocate EXT index 1 buffer 0x7fa1aaca2800<vir=0x7fa1aaca2800> with size<1048576, 1048576>
[TB][In] Allocate EXT index 2 buffer 0x7fa1aaba1800<vir=0x7fa1aaba1800> with size<1048576, 1048576>
[TB][In] Allocate EXT index 3 buffer 0x7fa1aaaa0800<vir=0x7fa1aaaa0800> with size<1048576, 1048576>
[TB][In] Allocate EXT index 4 buffer 0x7fa1aa99f800<vir=0x7fa1aa99f800> with size<1048576, 1048576>
[TB] NOTE: for INTERLACED stream, the resolution is of a FRAME.
[TB] Headers: Width 960 Height 544
[TB] Headers: Cropping params: (0, 0) 960x540
[TB] Headers: MonoChrome = 0
[TB] Headers: Pictures in DPB = 6
[TB] Headers: video_range 0
[TB] Headers: matrix_coefficients 2
[TB] Headers: PROGRESSIVE sequence
[TB] Headers: bit_depth = Y8C8
[TB] Headers: chroma_format_idc = 1
Decoder state change: DECODER_WAITING_HEADERS => DECODER_WAITING_RESOURCES
[TB][Out] Allocate EXT index 0 buffer 0x7fa1a9a0d800<vir=0x7fa1a9a0d800> with size<3110400, 3110912>
Decoder state change: DECODER_WAITING_RESOURCES => DECODER_DECODING
[TB][Out] Allocate EXT index 1 buffer 0x7fa1a9714800<vir=0x7fa1a9714800> with size<3110400, 3110912>
Decoder state change: DECODER_DECODING => DECODER_DECODING
[TB][Out] Allocate EXT index 2 buffer 0x7fa1a941b800<vir=0x7fa1a941b800> with size<3110400, 3110912>
Decoder state change: DECODER_DECODING => DECODER_DECODING
[TB][Out] Allocate EXT index 3 buffer 0x7fa1a9122800<vir=0x7fa1a9122800> with size<3110400, 3110912>
Decoder state change: DECODER_DECODING => DECODER_DECODING
[TB][Out] Allocate EXT index 4 buffer 0x7fa1a8e29800<vir=0x7fa1a8e29800> with size<3110400, 3110912>
Decoder state change: DECODER_DECODING => DECODER_DECODING
[TB][Out] Allocate EXT index 5 buffer 0x7fa1a8b30800<vir=0x7fa1a8b30800> with size<3110400, 3110912>
Decoder state change: DECODER_DECODING => DECODER_DECODING
[TB] Note: the mb number in cycles/mb is calculated by original size of stream, not
[TB] final output size of PostProcessing!
[TB] PIC  1/ 1, type         IDR,1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] SEI successful: got the SEI(HEVC), which belongs to PIC  1/ 1 
[TB] PIC  2/ 4, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC  3/ 3, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC  4/ 5, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC  5/ 2, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC  6/ 8, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC  7/ 7, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC  8/ 9, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC  9/ 6, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 10/12, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 11/11, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 12/10, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 13/15, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 14/14, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 15/16, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 16/13, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 17/18, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 18/17, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 19/21, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 20/20, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 21/22, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 22/19, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 23/25, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 24/24, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 25/26, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 26/23, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 27/29, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 28/28, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 29/30, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 30/27, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 31/33, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 32/32, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 33/34, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 34/31, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 35/37, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 36/36, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 37/38, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 38/35, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 39/41, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 40/40, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 41/42, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 42/39, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 43/45, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 44/44, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 45/46, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 46/43, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 47/49, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 48/48, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 49/50, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 50/47, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 51/53, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 52/52, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 53/54, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 54/51, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 55/57, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 56/56, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 57/58, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 58/55, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 59/61, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 60/60, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 61/62, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 62/59, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 63/65, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 64/64, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 65/66, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 66/63, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 67/69, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 68/68, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 69/70, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 70/67, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 71/72, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 72/71, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 73/75, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 74/74, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 75/76, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 76/73, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 77/79, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 78/78, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 79/80, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 80/77, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 81/83, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 82/82, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 83/84, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 84/81, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 85/87, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 86/88, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 87/86, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 88/89, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 89/85, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 90/92, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 91/91, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 92/93, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 93/90, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 94/96, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 95/97, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 96/95, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 97/98, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 98/94, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 99/100, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 960 x 540 
[TB] PIC 100/99, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 960 x 540 
Decoder state change: DECODER_DECODING => DECODER_SHUTTING_DOWN
Decoder state change: DECODER_SHUTTING_DOWN => DECODER_TERMINATED
[TB][In] Free EXT index 0 buffer 0x7fa1aada3800<vir=0x7fa1aada3800>
[TB][In] Free EXT index 1 buffer 0x7fa1aaca2800<vir=0x7fa1aaca2800>
[TB][In] Free EXT index 2 buffer 0x7fa1aaba1800<vir=0x7fa1aaba1800>
[TB][In] Free EXT index 3 buffer 0x7fa1aaaa0800<vir=0x7fa1aaaa0800>
[TB][In] Free EXT index 4 buffer 0x7fa1aa99f800<vir=0x7fa1aa99f800>
[TB][Out] Free EXT index 0 buffer 0x7fa1a9a0d800<vir=0x7fa1a9a0d800>
[TB][Out] Free EXT index 1 buffer 0x7fa1a9714800<vir=0x7fa1a9714800>
[TB][Out] Free EXT index 2 buffer 0x7fa1a941b800<vir=0x7fa1a941b800>
[TB][Out] Free EXT index 3 buffer 0x7fa1a9122800<vir=0x7fa1a9122800>
[TB][Out] Free EXT index 4 buffer 0x7fa1a8e29800<vir=0x7fa1a8e29800>
[TB][Out] Free EXT index 5 buffer 0x7fa1a8b30800<vir=0x7fa1a8b30800>
[TB] Output file: /root/workspace/code/vpu_testsuit/output/251/input_960x540.265_8b_0.yuv

Multi-core usage statistics:
	Core[ 0] used    100 times (100%)

+ /root/workspace/code/vpu_testsuit/input/vc9000d/app/cmodel/g2dec /root/workspace/data/265input_file/input_640x360.265 --output-file=/root/workspace/code/vpu_testsuit/output/252/input_640x360.265.yuv --num-pictures=100000 --md5 --pp=0 --antialias=0 --pp-filter=BILINEAR --scale=1920x1080

[TB] * * * * * * * * * * * * * * * * * * * * * * * 


[TB]        VERISILICON G2 DECODER TESTBENCH


[TB] * * * * * * * * * * * * * * * * * * * * * * * 
[TB] VC9000 Decoder API version 3.0.112
[TB] Hardware Build ID: 0x2023, ASIC_ID 0x90012010, Software Build: 0x1087a2
Decoder state change: DECODER_WAITING_HEADERS => DECODER_WAITING_HEADERS
[TB] struct TBCfg: Decoder Data Discard 0
[TB] struct TBCfg: Decoder Latency Compensation 0
[TB] struct TBCfg: Decoder Output Picture Endian 1
[TB] struct TBCfg: Decoder Bus Burst Length 16
[TB] struct TBCfg: Decoder Asic Service Priority 0
[TB] struct TBCfg: Decoder Service Merge Disable 0
[TB][In] Allocate EXT index 0 buffer 0x7f988d8a6800<vir=0x7f988d8a6800> with size<1048576, 1048576>
[TB][In] Allocate EXT index 1 buffer 0x7f988d7a5800<vir=0x7f988d7a5800> with size<1048576, 1048576>
[TB][In] Allocate EXT index 2 buffer 0x7f988d6a4800<vir=0x7f988d6a4800> with size<1048576, 1048576>
[TB][In] Allocate EXT index 3 buffer 0x7f988d5a3800<vir=0x7f988d5a3800> with size<1048576, 1048576>
[TB][In] Allocate EXT index 4 buffer 0x7f988d4a2800<vir=0x7f988d4a2800> with size<1048576, 1048576>
[TB] NOTE: for INTERLACED stream, the resolution is of a FRAME.
[TB] Headers: Width 640 Height 360
[TB] Headers: Cropping params: (0, 0) 640x360
[TB] Headers: MonoChrome = 0
[TB] Headers: Pictures in DPB = 6
[TB] Headers: video_range 0
[TB] Headers: matrix_coefficients 2
[TB] Headers: PROGRESSIVE sequence
[TB] Headers: bit_depth = Y8C8
[TB] Headers: chroma_format_idc = 1
Decoder state change: DECODER_WAITING_HEADERS => DECODER_WAITING_RESOURCES
[TB][Out] Allocate EXT index 0 buffer 0x7f988c79e800<vir=0x7f988c79e800> with size<3110400, 3110912>
Decoder state change: DECODER_WAITING_RESOURCES => DECODER_DECODING
[TB][Out] Allocate EXT index 1 buffer 0x7f988c4a5800<vir=0x7f988c4a5800> with size<3110400, 3110912>
Decoder state change: DECODER_DECODING => DECODER_DECODING
[TB][Out] Allocate EXT index 2 buffer 0x7f988c1ac800<vir=0x7f988c1ac800> with size<3110400, 3110912>
Decoder state change: DECODER_DECODING => DECODER_DECODING
[TB][Out] Allocate EXT index 3 buffer 0x7f9887d07800<vir=0x7f9887d07800> with size<3110400, 3110912>
Decoder state change: DECODER_DECODING => DECODER_DECODING
[TB][Out] Allocate EXT index 4 buffer 0x7f9887a0e800<vir=0x7f9887a0e800> with size<3110400, 3110912>
Decoder state change: DECODER_DECODING => DECODER_DECODING
[TB][Out] Allocate EXT index 5 buffer 0x7f9887715800<vir=0x7f9887715800> with size<3110400, 3110912>
Decoder state change: DECODER_DECODING => DECODER_DECODING
[TB] Note: the mb number in cycles/mb is calculated by original size of stream, not
[TB] final output size of PostProcessing!
[TB] PIC  1/ 1, type         IDR,1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] SEI successful: got the SEI(HEVC), which belongs to PIC  1/ 1 
[TB] PIC  2/ 3, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC  3/ 2, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC  4/ 6, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC  5/ 5, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC  6/ 7, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC  7/ 4, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC  8/10, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC  9/11, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 10/ 9, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 11/12, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 12/ 8, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 13/15, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 14/14, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 15/16, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 16/13, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 17/19, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 18/18, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 19/20, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 20/17, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 21/23, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 22/22, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 23/24, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 24/21, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 25/27, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 26/26, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 27/28, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 28/25, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 29/31, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 30/30, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 31/32, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 32/29, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 33/35, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 34/34, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 35/36, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 36/33, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 37/39, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 38/38, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 39/40, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 40/37, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 41/43, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 42/42, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 43/44, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 44/41, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 45/47, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 46/46, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 47/48, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 48/45, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 49/51, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 50/50, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 51/52, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 52/49, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 53/55, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 54/54, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 55/56, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 56/53, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 57/59, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 58/58, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 59/60, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 60/57, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 61/63, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 62/62, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 63/64, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 64/61, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 65/67, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 66/66, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 67/68, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 68/65, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 69/71, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 70/70, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 71/72, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 72/69, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 73/75, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 74/74, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 75/76, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 76/73, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 77/79, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 78/78, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 79/80, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 80/77, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 81/83, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 82/82, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 83/84, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 84/81, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 85/87, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 86/88, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 87/86, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 88/89, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 89/85, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 90/92, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 91/91, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 92/93, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 93/90, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 94/96, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 95/97, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 96/95, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 97/98, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 98/94, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 99/100, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 640 x 360 
[TB] PIC 100/99, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 640 x 360 
Decoder state change: DECODER_DECODING => DECODER_SHUTTING_DOWN
Decoder state change: DECODER_SHUTTING_DOWN => DECODER_TERMINATED
[TB][In] Free EXT index 0 buffer 0x7f988d8a6800<vir=0x7f988d8a6800>
[TB][In] Free EXT index 1 buffer 0x7f988d7a5800<vir=0x7f988d7a5800>
[TB][In] Free EXT index 2 buffer 0x7f988d6a4800<vir=0x7f988d6a4800>
[TB][In] Free EXT index 3 buffer 0x7f988d5a3800<vir=0x7f988d5a3800>
[TB][In] Free EXT index 4 buffer 0x7f988d4a2800<vir=0x7f988d4a2800>
[TB][Out] Free EXT index 0 buffer 0x7f988c79e800<vir=0x7f988c79e800>
[TB][Out] Free EXT index 1 buffer 0x7f988c4a5800<vir=0x7f988c4a5800>
[TB][Out] Free EXT index 2 buffer 0x7f988c1ac800<vir=0x7f988c1ac800>
[TB][Out] Free EXT index 3 buffer 0x7f9887d07800<vir=0x7f9887d07800>
[TB][Out] Free EXT index 4 buffer 0x7f9887a0e800<vir=0x7f9887a0e800>
[TB][Out] Free EXT index 5 buffer 0x7f9887715800<vir=0x7f9887715800>
[TB] Output file: /root/workspace/code/vpu_testsuit/output/252/input_640x360.265_8b_0.yuv

Multi-core usage statistics:
	Core[ 0] used    100 times (100%)

+ /root/workspace/code/vpu_testsuit/input/vc9000d/app/cmodel/g2dec /root/workspace/data/265input_file/input_1280x720.265 --output-file=/root/workspace/code/vpu_testsuit/output/253/input_1280x720.265.yuv --num-pictures=100000 --md5 --pp=0 --antialias=0 --pp-filter=BILINEAR --scale=1920x1080

[TB] * * * * * * * * * * * * * * * * * * * * * * * 


[TB]        VERISILICON G2 DECODER TESTBENCH


[TB] * * * * * * * * * * * * * * * * * * * * * * * 
[TB] VC9000 Decoder API version 3.0.112
[TB] Hardware Build ID: 0x2023, ASIC_ID 0x90012010, Software Build: 0x1087a2
Decoder state change: DECODER_WAITING_HEADERS => DECODER_WAITING_HEADERS
[TB] struct TBCfg: Decoder Data Discard 0
[TB] struct TBCfg: Decoder Latency Compensation 0
[TB] struct TBCfg: Decoder Output Picture Endian 1
[TB] struct TBCfg: Decoder Bus Burst Length 16
[TB] struct TBCfg: Decoder Asic Service Priority 0
[TB] struct TBCfg: Decoder Service Merge Disable 0
[TB][In] Allocate EXT index 0 buffer 0x7fc3d812f800<vir=0x7fc3d812f800> with size<1048576, 1048576>
[TB][In] Allocate EXT index 1 buffer 0x7fc3d802e800<vir=0x7fc3d802e800> with size<1048576, 1048576>
[TB][In] Allocate EXT index 2 buffer 0x7fc3d3eff800<vir=0x7fc3d3eff800> with size<1048576, 1048576>
[TB][In] Allocate EXT index 3 buffer 0x7fc3d3dfe800<vir=0x7fc3d3dfe800> with size<1048576, 1048576>
[TB][In] Allocate EXT index 4 buffer 0x7fc3d3cfd800<vir=0x7fc3d3cfd800> with size<1048576, 1048576>
[TB] NOTE: for INTERLACED stream, the resolution is of a FRAME.
[TB] Headers: Width 1280 Height 720
[TB] Headers: Cropping params: (0, 0) 1280x720
[TB] Headers: MonoChrome = 0
[TB] Headers: Pictures in DPB = 6
[TB] Headers: video_range 0
[TB] Headers: matrix_coefficients 2
[TB] Headers: PROGRESSIVE sequence
[TB] Headers: bit_depth = Y8C8
[TB] Headers: chroma_format_idc = 1
Decoder state change: DECODER_WAITING_HEADERS => DECODER_WAITING_RESOURCES
[TB][Out] Allocate EXT index 0 buffer 0x7fc3d29f3800<vir=0x7fc3d29f3800> with size<3110400, 3110912>
Decoder state change: DECODER_WAITING_RESOURCES => DECODER_DECODING
[TB][Out] Allocate EXT index 1 buffer 0x7fc3d26fa800<vir=0x7fc3d26fa800> with size<3110400, 3110912>
Decoder state change: DECODER_DECODING => DECODER_DECODING
[TB][Out] Allocate EXT index 2 buffer 0x7fc3d2401800<vir=0x7fc3d2401800> with size<3110400, 3110912>
Decoder state change: DECODER_DECODING => DECODER_DECODING
[TB][Out] Allocate EXT index 3 buffer 0x7fc3d2108800<vir=0x7fc3d2108800> with size<3110400, 3110912>
Decoder state change: DECODER_DECODING => DECODER_DECODING
[TB][Out] Allocate EXT index 4 buffer 0x7fc3d1e0f800<vir=0x7fc3d1e0f800> with size<3110400, 3110912>
Decoder state change: DECODER_DECODING => DECODER_DECODING
[TB][Out] Allocate EXT index 5 buffer 0x7fc3d1b16800<vir=0x7fc3d1b16800> with size<3110400, 3110912>
Decoder state change: DECODER_DECODING => DECODER_DECODING
[TB] Note: the mb number in cycles/mb is calculated by original size of stream, not
[TB] final output size of PostProcessing!
[TB] PIC  1/ 1, type         IDR,1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] SEI successful: got the SEI(HEVC), which belongs to PIC  1/ 1 
[TB] PIC  2/ 3, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC  3/ 2, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC  4/ 6, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC  5/ 5, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC  6/ 7, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC  7/ 4, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC  8/10, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC  9/11, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 10/ 9, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 11/12, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 12/ 8, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 13/15, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 14/14, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 15/16, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 16/13, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 17/19, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 18/18, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 19/20, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 20/17, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 21/23, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 22/22, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 23/24, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 24/21, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 25/27, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 26/26, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 27/28, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 28/25, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 29/31, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 30/30, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 31/32, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 32/29, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 33/35, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 34/34, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 35/36, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 36/33, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 37/39, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 38/38, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 39/40, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 40/37, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 41/43, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 42/42, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 43/44, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 44/41, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 45/47, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 46/46, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 47/48, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 48/45, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 49/51, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 50/50, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 51/52, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 52/49, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 53/55, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 54/54, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 55/56, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 56/53, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 57/59, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 58/58, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 59/60, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 60/57, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 61/63, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 62/62, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 63/64, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 64/61, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 65/67, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 66/66, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 67/68, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 68/65, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 69/71, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 70/70, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 71/72, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 72/69, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 73/75, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 74/74, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 75/76, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 76/73, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 77/79, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 78/78, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 79/80, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 80/77, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 81/83, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 82/82, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 83/84, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 84/81, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 85/87, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 86/88, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 87/86, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 88/89, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 89/85, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 90/92, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 91/91, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 92/93, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 93/90, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 94/96, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 95/97, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 96/95, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 97/98, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 98/94, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 99/100, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 1280 x 720 
[TB] PIC 100/99, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 1280 x 720 
Decoder state change: DECODER_DECODING => DECODER_SHUTTING_DOWN
Decoder state change: DECODER_SHUTTING_DOWN => DECODER_TERMINATED
[TB][In] Free EXT index 0 buffer 0x7fc3d812f800<vir=0x7fc3d812f800>
[TB][In] Free EXT index 1 buffer 0x7fc3d802e800<vir=0x7fc3d802e800>
[TB][In] Free EXT index 2 buffer 0x7fc3d3eff800<vir=0x7fc3d3eff800>
[TB][In] Free EXT index 3 buffer 0x7fc3d3dfe800<vir=0x7fc3d3dfe800>
[TB][In] Free EXT index 4 buffer 0x7fc3d3cfd800<vir=0x7fc3d3cfd800>
[TB][Out] Free EXT index 0 buffer 0x7fc3d29f3800<vir=0x7fc3d29f3800>
[TB][Out] Free EXT index 1 buffer 0x7fc3d26fa800<vir=0x7fc3d26fa800>
[TB][Out] Free EXT index 2 buffer 0x7fc3d2401800<vir=0x7fc3d2401800>
[TB][Out] Free EXT index 3 buffer 0x7fc3d2108800<vir=0x7fc3d2108800>
[TB][Out] Free EXT index 4 buffer 0x7fc3d1e0f800<vir=0x7fc3d1e0f800>
[TB][Out] Free EXT index 5 buffer 0x7fc3d1b16800<vir=0x7fc3d1b16800>
[TB] Output file: /root/workspace/code/vpu_testsuit/output/253/input_1280x720.265_8b_0.yuv

Multi-core usage statistics:
	Core[ 0] used    100 times (100%)

+ /root/workspace/code/vpu_testsuit/input/vc9000d/app/cmodel/g2dec /root/workspace/data/265input_file/input_768x432.265 --output-file=/root/workspace/code/vpu_testsuit/output/254/input_768x432.265.yuv --num-pictures=100000 --md5 --pp=0 --antialias=0 --pp-filter=BILINEAR --scale=1920x1080

[TB] * * * * * * * * * * * * * * * * * * * * * * * 


[TB]        VERISILICON G2 DECODER TESTBENCH


[TB] * * * * * * * * * * * * * * * * * * * * * * * 
[TB] VC9000 Decoder API version 3.0.112
[TB] Hardware Build ID: 0x2023, ASIC_ID 0x90012010, Software Build: 0x1087a2
Decoder state change: DECODER_WAITING_HEADERS => DECODER_WAITING_HEADERS
[TB] struct TBCfg: Decoder Data Discard 0
[TB] struct TBCfg: Decoder Latency Compensation 0
[TB] struct TBCfg: Decoder Output Picture Endian 1
[TB] struct TBCfg: Decoder Bus Burst Length 16
[TB] struct TBCfg: Decoder Asic Service Priority 0
[TB] struct TBCfg: Decoder Service Merge Disable 0
[TB][In] Allocate EXT index 0 buffer 0x7efd1c838800<vir=0x7efd1c838800> with size<1048576, 1048576>
[TB][In] Allocate EXT index 1 buffer 0x7efd1c737800<vir=0x7efd1c737800> with size<1048576, 1048576>
[TB][In] Allocate EXT index 2 buffer 0x7efd1c636800<vir=0x7efd1c636800> with size<1048576, 1048576>
[TB][In] Allocate EXT index 3 buffer 0x7efd1c535800<vir=0x7efd1c535800> with size<1048576, 1048576>
[TB][In] Allocate EXT index 4 buffer 0x7efd1c434800<vir=0x7efd1c434800> with size<1048576, 1048576>
[TB] NOTE: for INTERLACED stream, the resolution is of a FRAME.
[TB] Headers: Width 768 Height 432
[TB] Headers: Cropping params: (0, 0) 768x432
[TB] Headers: MonoChrome = 0
[TB] Headers: Pictures in DPB = 6
[TB] Headers: video_range 0
[TB] Headers: matrix_coefficients 2
[TB] Headers: PROGRESSIVE sequence
[TB] Headers: bit_depth = Y8C8
[TB] Headers: chroma_format_idc = 1
Decoder state change: DECODER_WAITING_HEADERS => DECODER_WAITING_RESOURCES
[TB][Out] Allocate EXT index 0 buffer 0x7efd17506800<vir=0x7efd17506800> with size<3110400, 3110912>
Decoder state change: DECODER_WAITING_RESOURCES => DECODER_DECODING
[TB][Out] Allocate EXT index 1 buffer 0x7efd1720d800<vir=0x7efd1720d800> with size<3110400, 3110912>
Decoder state change: DECODER_DECODING => DECODER_DECODING
[TB][Out] Allocate EXT index 2 buffer 0x7efd16f14800<vir=0x7efd16f14800> with size<3110400, 3110912>
Decoder state change: DECODER_DECODING => DECODER_DECODING
[TB][Out] Allocate EXT index 3 buffer 0x7efd16c1b800<vir=0x7efd16c1b800> with size<3110400, 3110912>
Decoder state change: DECODER_DECODING => DECODER_DECODING
[TB][Out] Allocate EXT index 4 buffer 0x7efd16922800<vir=0x7efd16922800> with size<3110400, 3110912>
Decoder state change: DECODER_DECODING => DECODER_DECODING
[TB][Out] Allocate EXT index 5 buffer 0x7efd16629800<vir=0x7efd16629800> with size<3110400, 3110912>
Decoder state change: DECODER_DECODING => DECODER_DECODING
[TB] Note: the mb number in cycles/mb is calculated by original size of stream, not
[TB] final output size of PostProcessing!
[TB] PIC  1/ 1, type         IDR,1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] SEI successful: got the SEI(HEVC), which belongs to PIC  1/ 1 
[TB] PIC  2/ 3, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC  3/ 2, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC  4/ 6, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC  5/ 5, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC  6/ 7, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC  7/ 4, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC  8/10, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC  9/11, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 10/ 9, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 11/12, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 12/ 8, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 13/15, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 14/14, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 15/16, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 16/13, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 17/19, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 18/18, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 19/20, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 20/17, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 21/23, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 22/22, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 23/24, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 24/21, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 25/27, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 26/26, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 27/28, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 28/25, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 29/31, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 30/30, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 31/32, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 32/29, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 33/35, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 34/34, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 35/36, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 36/33, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 37/39, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 38/38, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 39/40, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 40/37, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 41/43, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 42/42, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 43/44, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 44/41, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 45/47, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 46/46, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 47/48, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 48/45, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 49/51, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 50/50, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 51/52, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 52/49, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 53/55, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 54/54, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 55/56, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 56/53, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 57/59, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 58/58, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 59/60, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 60/57, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 61/63, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 62/62, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 63/64, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 64/61, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 65/67, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 66/66, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 67/68, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 68/65, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 69/71, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 70/70, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 71/72, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 72/69, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 73/75, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 74/74, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 75/76, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 76/73, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 77/79, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 78/78, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 79/80, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 80/77, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 81/83, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 82/82, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 83/84, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 84/81, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 85/87, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 86/88, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 87/86, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 88/89, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 89/85, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 90/92, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 91/91, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 92/93, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 93/90, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 94/96, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 95/97, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 96/95, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 97/98, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 98/94, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 99/100, type Non-IDR (B),1920 x 1080, Crop: (0, 0), 768 x 432 
[TB] PIC 100/99, type Non-IDR (P),1920 x 1080, Crop: (0, 0), 768 x 432 
Decoder state change: DECODER_DECODING => DECODER_SHUTTING_DOWN
Decoder state change: DECODER_SHUTTING_DOWN => DECODER_TERMINATED
[TB][In] Free EXT index 0 buffer 0x7efd1c838800<vir=0x7efd1c838800>
[TB][In] Free EXT index 1 buffer 0x7efd1c737800<vir=0x7efd1c737800>
[TB][In] Free EXT index 2 buffer 0x7efd1c636800<vir=0x7efd1c636800>
[TB][In] Free EXT index 3 buffer 0x7efd1c535800<vir=0x7efd1c535800>
[TB][In] Free EXT index 4 buffer 0x7efd1c434800<vir=0x7efd1c434800>
[TB][Out] Free EXT index 0 buffer 0x7efd17506800<vir=0x7efd17506800>
[TB][Out] Free EXT index 1 buffer 0x7efd1720d800<vir=0x7efd1720d800>
[TB][Out] Free EXT index 2 buffer 0x7efd16f14800<vir=0x7efd16f14800>
[TB][Out] Free EXT index 3 buffer 0x7efd16c1b800<vir=0x7efd16c1b800>
[TB][Out] Free EXT index 4 buffer 0x7efd16922800<vir=0x7efd16922800>
[TB][Out] Free EXT index 5 buffer 0x7efd16629800<vir=0x7efd16629800>
[TB] Output file: /root/workspace/code/vpu_testsuit/output/254/input_768x432.265_8b_0.yuv

Multi-core usage statistics:
	Core[ 0] used    100 times (100%)

